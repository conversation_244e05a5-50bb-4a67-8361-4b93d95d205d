"use client";

import React from 'react';
import { AnalyzingIcon } from '../icons/YoutubeIcons';

const LoadingIndicator: React.FC = () => {
  return (
    <div className="mt-8 flex items-center justify-center rounded-2xl border border-white/10 bg-[#0A0F14] p-8">
      <div className="flex items-center gap-3">
        <span className="animate-pulse"><AnalyzingIcon /></span>
        <span className="text-lg">Analyzing…</span>
        <span className="inline-flex">
          <span className="mx-0.5 h-1.5 w-1.5 animate-bounce rounded-full bg-white/80 [animation-delay:-200ms]"></span>
          <span className="mx-0.5 h-1.5 w-1.5 animate-bounce rounded-full bg-white/80"></span>
          <span className="mx-0.5 h-1.5 w-1.5 animate-bounce rounded-full bg-white/80 [animation-delay:200ms]"></span>
        </span>
      </div>
    </div>
  );
};

export default LoadingIndicator;
