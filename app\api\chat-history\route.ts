import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '../../../lib/mongodb';

export async function GET() {
  try {
    const { db } = await connectToDatabase();
    const collection = db.collection('chat_history');
    
    // Get all chat sessions, sorted by creation date (newest first)
    const sessions = await collection.aggregate([
      { $sort: { createdAt: -1 } },
      { $limit: 50 }
    ], { allowDiskUse: true }).toArray();
    
    // Convert MongoDB dates to ISO strings for JSON serialization
    // Also map sessionId to id for frontend compatibility
    const sessionsWithStringDates = sessions.map(session => ({
      ...session,
      id: session.sessionId, // Map sessionId to id for frontend
      createdAt: session.createdAt?.toISOString(),
      updatedAt: session.updatedAt?.toISOString(),
      messages: session.messages?.map((msg: any) => ({
        ...msg,
        timestamp: msg.timestamp ? 
          (msg.timestamp instanceof Date ? msg.timestamp.toISOString() : 
           typeof msg.timestamp === 'string' ? msg.timestamp : 
           new Date(msg.timestamp).toISOString()) : 
          new Date().toISOString()
      })) || []
    }));
    
    return NextResponse.json({ sessions: sessionsWithStringDates });
  } catch (error) {
    console.error('Error fetching chat history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch chat history' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const collection = db.collection('chat_history');
    
    const body = await request.json();
    const { sessionId, title, messages } = body;
    
    if (!sessionId || !title) {
      return NextResponse.json(
        { error: 'Session ID and title are required' },
        { status: 400 }
      );
    }

    // Don't save sessions with no messages or only empty messages
    if (!messages || messages.length === 0) {
      return NextResponse.json(
        { error: 'Cannot save session with no messages' },
        { status: 400 }
      );
    }

    // Check if there are any non-empty messages
    const hasValidMessages = messages.some((msg: any) => 
      msg.content && msg.content.trim().length > 0
    );

    if (!hasValidMessages) {
      return NextResponse.json(
        { error: 'Cannot save session with only empty messages' },
        { status: 400 }
      );
    }
    
    // Check if session already exists
    const existingSession = await collection.findOne({ sessionId });
    
    if (existingSession) {
      // Update existing session
      await collection.updateOne(
        { sessionId },
        {
          $set: {
            title,
            messages,
            updatedAt: new Date()
          }
        }
      );
    } else {
      // Create new session
      await collection.insertOne({
        sessionId,
        title,
        messages: messages || [],
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving chat history:', error);
    return NextResponse.json(
      { error: 'Failed to save chat history' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const collection = db.collection('chat_history');
    
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    
    if (sessionId) {
      // Delete specific session
      await collection.deleteOne({ sessionId });
    } else {
      // Delete all sessions
      await collection.deleteMany({});
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting chat history:', error);
    return NextResponse.json(
      { error: 'Failed to delete chat history' },
      { status: 500 }
    );
  }
}
