import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

export const runtime = 'nodejs';

let sharpModule: any = null;
async function ensureSharp() {
  if (sharpModule) return sharpModule;
  try {
    const mod = await import('sharp');
    // @ts-ignore
    sharpModule = mod.default || mod;
  } catch (e) {
    console.warn('sharp not available, skipping server-side resize');
    sharpModule = null;
  }
  return sharpModule;
}

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// Simple in-memory store for resized images (Map<id, { mime: string; buffer: Buffer }>)
const memoryStore = new Map<string, { mime: string; buffer: Buffer }>();

function validateEnv() {
  console.log('[gpt-image-designer] REPLICATE_API_TOKEN present:', !!process.env.REPLICATE_API_TOKEN);
  const openaiKey = process.env.OpenAI_API_KEY || process.env.OpenAI_API_KEY || process.env.OpenAI_API_KEY;
  const effective = process.env.OpenAI_API_KEY || process.env.OPENAI_API_KEY;
  console.log('[gpt-image-designer] OPENAI key present:', !!effective);
  if (!process.env.REPLICATE_API_TOKEN) {
    throw new Error('Missing REPLICATE_API_TOKEN');
  }
  if (!effective) {
    throw new Error('Missing OPENAI_API_KEY (or OpenAI_API_KEY)');
  }
  return effective!;
}

export async function POST(req: NextRequest) {
  try {
    console.log('[gpt-image-designer] POST', req.method, req.url);
    const openaiKey = validateEnv();

    const body = await req.json();
    const {
      prompt,
      input_images = [],
      aspect_ratio = '16:9',
      number_of_images = 1,
      quality = 'high',
      background = 'auto',
      output_format = 'png',
      input_fidelity = 'high',
      output_compression = 90,
      moderation = 'auto',
      user_id = null,
      resize = true,
    } = body || {};

    console.log('[gpt-image-designer] body keys:', Object.keys(body || {}));
    console.log('[gpt-image-designer] input_images count:', Array.isArray(input_images) ? input_images.length : 0);

    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json({ error: 'prompt is required' }, { status: 400 });
    }

    // Ensure input_images are strings (http(s) or data URLs)
    const sanitizedImages: string[] = Array.isArray(input_images)
      ? input_images.filter((s: any) => typeof s === 'string' && s.length > 0)
      : [];

    // Replicate model constraints:
    // - aspect_ratio allowed only: "1:1", "3:2", "2:3"
    // Map incoming (possibly "16:9") to closest allowed. We also log the mapping.
    const requestedAR = aspect_ratio;
    let ar = '3:2'; // default
    if (requestedAR === '1:1' || requestedAR === '3:2' || requestedAR === '2:3') {
      ar = requestedAR;
    } else {
      // choose landscape 3:2 as closest to 16:9 for this model
      console.warn('[gpt-image-designer] aspect_ratio not supported by model:', requestedAR, '-> using 3:2');
    }

    // Validate that every input image is a valid URI (http(s) or data:)
    // Replicate enforces a uri format; data URLs are allowed but must start with data:
    const validUri = (u: string) => /^https?:\/\//i.test(u) || /^data:/i.test(u);
    const invalids = sanitizedImages.filter((u) => !validUri(u));
    if (invalids.length) {
      console.warn('[gpt-image-designer] invalid input_images (not uri/dataurl):', invalids);
    }
    const imagesForModel = sanitizedImages.filter(validUri);
    console.log('[gpt-image-designer] imagesForModel count:', imagesForModel.length);

    // Build replicate input
    const input: Record<string, any> = {
      openai_api_key: openaiKey,
      prompt,
      aspect_ratio: ar,
      number_of_images: Math.min(Math.max(Number(number_of_images) || 1, 1), 10),
      quality,
      background,
      output_format,
      input_fidelity,
      output_compression,
      moderation,
    };
    if (imagesForModel.length) input.input_images = imagesForModel;
    if (user_id) input.user_id = user_id;

    console.log('[gpt-image-designer] replicate input (trunc):', JSON.stringify({ ...input, openai_api_key: '[redacted]' }).slice(0, 900));

    // Run on Replicate using predictions.create to capture full lifecycle
    // Extra logging: show first 2 image uri prefixes for debugging
    console.log('[gpt-image-designer] AR sent to model:', input.aspect_ratio);
    console.log('[gpt-image-designer] input_images sample:', (input.input_images || []).slice(0, 2));

    const prediction = await replicate.predictions.create({
      model: 'openai/gpt-image-1',
      input,
    });
    console.log('[gpt-image-designer] prediction created id:', (prediction as any)?.id, 'status:', (prediction as any)?.status);

    // Poll for completion
    let pred: any = prediction;
    const start = Date.now();
    const timeoutMs = 520000;
    while (!['succeeded', 'failed', 'canceled'].includes(pred.status)) {
      await new Promise((r) => setTimeout(r, 1500));
      pred = await replicate.predictions.get(pred.id);
      console.log('[gpt-image-designer] poll status:', pred.status);
      if (Date.now() - start > timeoutMs) {
        console.warn('[gpt-image-designer] polling timeout');
        break;
      }
    }

    let urls: string[] = [];
    const out = pred?.output;
    if (Array.isArray(out)) {
      for (const item of out) {
        if (typeof item === 'string') urls.push(item);
        else if (item && typeof (item as any).url === 'function') {
          try {
            const u = (item as any).url();
            if (typeof u === 'string') urls.push(u);
          } catch {}
        } else if (item && typeof (item as any).url === 'string') {
          urls.push((item as any).url);
        } else if (item && typeof item === 'object') {
          const u = (item as any).image || (item as any).href;
          if (typeof u === 'string') urls.push(u);
        }
      }
    } else if (typeof out === 'string') {
      urls.push(out);
    }

    // Fallback to run() once if needed
    if (!urls.length) {
      try {
        const runOut = await replicate.run('openai/gpt-image-1', { input });
        if (Array.isArray(runOut)) {
          for (const item of runOut) {
            if (typeof item === 'string') urls.push(item);
            else if (item && typeof (item as any).url === 'function') {
              try {
                const u = (item as any).url();
                if (typeof u === 'string') urls.push(u);
              } catch {}
            } else if (item && typeof (item as any).url === 'string') {
              urls.push((item as any).url);
            } else if (item && typeof item === 'object') {
              const u = (item as any).image || (item as any).href;
              if (typeof u === 'string') urls.push(u);
            }
          }
        } else if (typeof runOut === 'string') {
          urls.push(runOut);
        }
      } catch (e) {
        console.warn('[gpt-image-designer] replicate.run fallback error:', e);
      }
    }

    console.log('[gpt-image-designer] urls:', urls);
    if (!urls.length) {
      return NextResponse.json({
        success: false,
        error: 'No images generated',
        id: pred?.id || null,
        status: pred?.status || null,
        logs: pred?.logs || null,
      }, { status: 200 });
    }

    let primaryUrl = urls[0];
    let resizedDataUrl: string | null = null;

    if (resize) {
      try {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 30000);
        const res = await fetch(primaryUrl, { signal: controller.signal });
        clearTimeout(timeout);
        if (!res.ok) {
          throw new Error(`Fetch generated image failed: ${res.status} ${res.statusText}`);
        }
        const arrayBuffer = await res.arrayBuffer();

        const sharp = await ensureSharp();
        if (!sharp) {
          throw new Error('sharp not available');
        }

        const img = sharp(Buffer.from(arrayBuffer));
        const format = output_format === 'jpeg' ? 'jpeg' : output_format === 'webp' ? 'webp' : 'png';
        const resized = await img.resize(1280, 720, { fit: 'cover' }).toFormat(format);
        const buffer = await resized.toBuffer();
        const mime = format === 'jpeg' ? 'image/jpeg' : format === 'webp' ? 'image/webp' : 'image/png';

        const base64 = buffer.toString('base64');
        resizedDataUrl = `data:${mime};base64,${base64}`;

        const id = `${Date.now()}-${Math.random().toString(36).slice(2)}`;
        memoryStore.set(id, { mime, buffer });
      } catch (e: any) {
        console.warn('Resize step skipped:', e?.message || e);
      }
    }

    try {
      if (resizedDataUrl) {
        console.log('[gpt-image-designer] Uploading resized image to Cloudinary for durable URL');
        const uploaded = await (await import('../../../lib/cloudinary')).default.uploader.upload(resizedDataUrl, {
          folder: 'ai-generated-images',
        });
        const finalUrl = uploaded.secure_url;
        console.log('[gpt-image-designer] Cloudinary upload done:', finalUrl);
        return NextResponse.json({
          success: true,
          images: [finalUrl],
          original: urls,
          resized: true,
          aspect_ratio,
          bytes_exact_1280x720: true,
        });
      }
    } catch (upErr) {
      console.warn('[gpt-image-designer] Cloudinary upload failed, falling back to existing URL:', upErr);
    }

    return NextResponse.json({
      success: true,
      images: [primaryUrl],
      original: urls,
      resized: false,
      aspect_ratio,
      bytes_exact_1280x720: false,
    });
  } catch (error: any) {
    console.error('[gpt-image-designer] error:', error);
    return NextResponse.json({ error: error?.message || 'Internal Server Error' }, { status: 500 });
  }
}