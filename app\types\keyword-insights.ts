// Types for Google Keyword Insights API

export interface KeywordInsight {
  id: number;
  text: string;
  volume: number;
  competition_level: string;
  competition_index: number;
  low_bid: number;
  high_bid: number;
  trend: number;
}

export interface KeywordInsightsResponse {
  success: boolean;
  data?: KeywordInsight[];
  error?: string;
}

export interface KeywordSearchState {
  isLoading: boolean;
  data: KeywordInsight[];
  error: string | null;
  searchQuery: string;
  activeTab: string;
}

// Helper function to format volume numbers
export function formatVolume(volume: number): string {
  if (volume >= 1000000) {
    return `${(volume / 1000000).toFixed(1)}M`;
  } else if (volume >= 1000) {
    return `${(volume / 1000).toFixed(1)}K`;
  }
  return volume.toString();
}

// Helper function to get competition level color
export function getCompetitionColor(level: string): string {
  switch (level.toLowerCase()) {
    case 'low':
      return 'text-green-400';
    case 'medium':
      return 'text-yellow-400';
    case 'high':
      return 'text-red-400';
    default:
      return 'text-gray-400';
  }
}

// Helper function to format trend percentage
export function formatTrend(trend: number): string {
  const sign = trend >= 0 ? '+' : '';
  return `${sign}${trend.toFixed(1)}%`;
}

// Helper function to get trend color
export function getTrendColor(trend: number): string {
  if (trend > 0) {
    return 'text-green-400';
  } else if (trend < 0) {
    return 'text-red-400';
  }
  return 'text-gray-400';
}

// Helper function to format keywords data for copying
export function formatKeywordsForCopy(keywords: KeywordInsight[], format: 'csv' | 'json' | 'text' = 'csv'): string {
  // Always return just the keywords, regardless of format
  return keywords.map(row => row.text).join('\n');

  }
  
