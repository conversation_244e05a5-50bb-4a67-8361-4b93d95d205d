"use client";

import { PropsWithChildren } from "react";
import { GoogleOAuthProvider } from "@react-oauth/google";

export default function GoogleOAuthClient({ children }: PropsWithChildren) {
  const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || "";
  // If clientId is missing, render children without provider to avoid server build errors.
  // You can also log a warning in dev.
  return clientId ? (
    <GoogleOAuthProvider clientId={clientId}>{children}</GoogleOAuthProvider>
  ) : (
    <>{children}</>
  );
}