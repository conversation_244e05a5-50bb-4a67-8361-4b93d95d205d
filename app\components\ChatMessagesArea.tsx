import React, { useState } from 'react';
import ImageModal from './ImageModal';
import AudioPlayer from './AudioPlayer';
import { AudioPlayerMusicGen } from './index';
import { GeneratingLoadingIcon, ImageDownloadIcon } from './icons/icons';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  images?: string[];
  videos?: string[];
  audioUrl?: string;
  mode?: string; // Add mode property
}

interface ChatMessagesAreaProps {
  chatMessages: ChatMessage[];
  isGenerating: boolean;
  generationProgress: number;
  generationPrompt: string;
  activeMode: 'text-to-image' | 'image-to-image' | 'text-to-video' | 'image-to-video' | 'text-to-speech' | 'music-generation' | 'sound-effects' | 'lip-sync';
  onRewrite: (prompt: string) => void;
}

const ChatMessagesArea: React.FC<ChatMessagesAreaProps> = ({ chatMessages, isGenerating, generationProgress, generationPrompt, activeMode, onRewrite }) => {
  const [modalImage, setModalImage] = useState<string | null>(null);
  const [modalVideo, setModalVideo] = useState<string | null>(null);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState<string | null>(null);

  const handleImageClick = (src: string) => {
    setModalImage(src);
    setIsImageModalOpen(true);
  };

  const handleVideoClick = (src: string) => {
    setModalVideo(src);
    setIsVideoModalOpen(true);
  };

  const handleDownload = (src: string) => {
    const link = document.createElement('a');
    link.href = src;
    link.download = `media-${Date.now()}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setFeedbackMessage('Prompt copied!');
      setTimeout(() => setFeedbackMessage(null), 1500);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  };

  const handleRewriteClick = (text: string) => {
    onRewrite(text);
    setFeedbackMessage('Prompt sent to input!');
    setTimeout(() => setFeedbackMessage(null), 1500);
  };

  return (
    <div className="min-h-full">
      <div className="min-h-full">
        {chatMessages.length > 0 ? (
          <div className="p-3 sm:p-4 md:p-6">
            <div className="max-w-4xl mx-auto space-y-3 sm:space-y-4">
              {chatMessages
                .filter(message => message.type === 'ai')
                .map((message) => (
                  <div key={message.id} className="flex justify-start">
                    <div style={{ width: 'webkit-fill-available' }} className="max-w-[85%] sm:max-w-[75%] md:max-w-[70%] p-3 sm:p-4 rounded-lg bg-[#1a1a2e] text-gray-300 border border-[#2a2a3e]">
                      {/* Prompt and icons area (always on top) */}
                      <div className='bg-[#030F0F] py-7 rounded-sm px-4'>
                        <div><p className="text-base mb-6">{message.content || "Result generated with reference"}</p></div>
                        <div className='justify-end pr-5 flex items-center relative'>
                          {/* rewrite icon */}
                          <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg" onClick={() => handleRewriteClick(message.content)} className="cursor-pointer">
                            <path d="M18.2677 1.73223C17.2914 0.755922 15.7085 0.755922 14.7322 1.73223L1 15.4644V19.0355H4.5L18.2677 5.26777C19.244 4.29146 19.244 2.70854 18.2677 1.73223Z" fill="#030F0F" />
                            <path d="M13.2322 3.23223L16.7677 6.76777M14.7322 1.73223C15.7085 0.755922 17.2914 0.755922 18.2677 1.73223C19.244 2.70854 19.244 4.29146 18.2677 5.26777L4.5 19.0355H1V15.4644L14.7322 1.73223Z" stroke="#888888" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                          <span className='mx-1'></span>
                          {/* copy icon */}
                          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" onClick={() => handleCopy(message.content)} className="cursor-pointer">
                            <path fillRule="evenodd" clipRule="evenodd" d="M12 0C9.79086 0 8 1.79086 8 4V6H4C1.79086 6 0 7.79086 0 10V16C0 18.2091 1.79086 20 4 20H8C10.2091 20 12 18.2091 12 16V14H16C18.2091 14 20 12.2091 20 10V4C20 1.79086 18.2091 0 16 0H12ZM12 12H16C17.1046 12 18 11.1046 18 10V4C18 2.89543 17.1046 2 16 2H12C10.8954 2 10 2.89543 10 4V6.53513C11.1956 7.22675 12 8.51943 12 10V12ZM2 10C2 8.89543 2.89543 8 4 8H8C9.10457 8 10 8.89543 10 10V16C10 17.1046 9.10457 18 8 18H4C2.89543 18 2 17.1046 2 16V10Z" fill="#888888" />
                          </svg>
                          {feedbackMessage && (
                            <span className="absolute -top-7 right-0 bg-[#222] text-xs text-white px-2 py-1 rounded shadow z-10 animate-fade-in-out">
                              {feedbackMessage}
                            </span>
                          )}
                        </div>
                      </div>
                      {/* Outputs area (images, videos, audio, etc.) */}
                      {/* Images */}
                      {message.images && message.images.length > 0 && (
                        message.images.length === 1 ? (
                          <div className="mt-3">
                            <div className="relative group">
                              <img
                                src={message.images[0]}
                                alt="Generated image"
                                className="w-full rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                                onClick={() => handleImageClick(message.images[0])}
                              />
                              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDownload(message.images[0]);
                                  }}
                                  className="bg-black bg-opacity-50 text-white p-2 rounded-lg hover:bg-opacity-70 transition-colors"
                                  title="Download image"
                                >
                                  <ImageDownloadIcon />
                                </button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="mt-3 grid grid-cols-1 sm:grid-cols-2 gap-2">
                            {message.images.map((imageUrl, index) => (
                              <div key={index} className="relative group">
                                <img
                                  src={imageUrl}
                                  alt={`Generated image ${index + 1}`}
                                  className="w-full rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                                  onClick={() => handleImageClick(imageUrl)}
                                />
                                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDownload(imageUrl);
                                    }}
                                    className="bg-black bg-opacity-50 text-white p-2 rounded-lg hover:bg-opacity-70 transition-colors"
                                    title="Download image"
                                  >
                                    <ImageDownloadIcon />
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )
                      )}
                      {/* Videos */}
                      {message.videos && message.videos.length > 0 && (
                        message.videos.length === 1 ? (
                          <div className="mt-3">
                            <div className="relative group">
                              <video
                                src={message.videos[0]}
                                controls
                                className="w-full rounded-lg cursor-pointer"
                                onClick={() => handleVideoClick(message.videos[0])}
                              />
                              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDownload(message.videos[0]);
                                  }}
                                  className="bg-black bg-opacity-50 text-white p-2 rounded-lg hover:bg-opacity-70 transition-colors"
                                  title="Download video"
                                >
                                  <ImageDownloadIcon />
                                </button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="mt-3 grid grid-cols-1 sm:grid-cols-2 gap-2">
                            {message.videos.map((videoUrl, index) => (
                              <div key={index} className="relative group">
                                <video
                                  src={videoUrl}
                                  controls
                                  className="w-full rounded-lg cursor-pointer"
                                  onClick={() => handleVideoClick(videoUrl)}
                                />
                                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDownload(videoUrl);
                                    }}
                                    className="bg-black bg-opacity-50 text-white p-2 rounded-lg hover:bg-opacity-70 transition-colors"
                                    title="Download video"
                                  >
                                    <ImageDownloadIcon />
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        ))}
                      {/* Audio */}
                      {message.audioUrl && (
                        <div className="mt-3">
                          {/* Use AudioPlayerMusicGen if message.mode is music-generation, else fallback to AudioPlayer */}
                          {"mode" in message && (message as any).mode === "music-generation" ? (
                            <AudioPlayerMusicGen src={message.audioUrl} />
                          ) : (
                            <AudioPlayer src={message.audioUrl} />
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center p-6">
            {/* Show generating UI if isGenerating is true and no messages */}
            {isGenerating && (
              <div className="bg-[#10101a] border border-[#2a2a3e] rounded-md p-4 h-[300px] w-[540px]">
                <div className="bg-[#1a1a2e] text-white text-sm p-6 rounded-md mb-6 leading-relaxed tracking-wide">
                  {generationPrompt}
                </div>
                <div className="flex justify-center">
                  <div className="relative top-20 text-white text-sm flex items-center justify-center gap-2">
                    <GeneratingLoadingIcon />
                    {activeMode === 'music-generation'
                      ? `Generating music... ${Math.round(generationProgress)}%`
                      : activeMode === 'lip-sync'
                      ? `Generating lip sync video (may take up to 11 minutes)... ${Math.round(generationProgress)}%`
                      : `Generating... ${Math.round(generationProgress)}%`}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      {/* Image Modal */}
      <ImageModal
        image={modalImage ? { src: modalImage, height: 'h-96' } : null}
        isOpen={isImageModalOpen}
        onClose={() => setIsImageModalOpen(false)}
        onDownload={handleDownload}
      />
      {/* Video Modal */}
      {isVideoModalOpen && modalVideo && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 backdrop-blur-sm p-2 sm:p-4">
          <div className="relative bg-[#0f1419] rounded-2xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-y-auto border border-[#2a3441]">
            <button
              className="absolute top-4 right-4 text-[#9ca3af] hover:text-white text-2xl transition-colors z-20 bg-black bg-opacity-50 rounded-full w-8 h-8 flex items-center justify-center"
              onClick={() => setIsVideoModalOpen(false)}
              aria-label="Close"
            >
              &times;
            </button>
            <div className="relative flex flex-col items-center justify-center p-6">
              <video
                src={modalVideo}
                controls
                autoPlay
                className="w-full max-w-3xl h-[400px] sm:h-[500px] md:h-[600px] rounded-2xl bg-black"
                style={{ background: '#000' }}
              />
              <button
                className="mt-4 bg-black bg-opacity-60 backdrop-blur-sm rounded-lg p-2.5 transition-all duration-200 hover:bg-opacity-80 text-white"
                onClick={() => handleDownload(modalVideo)}
                title="Download"
              >

                <span className="ml-2">Download Video</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatMessagesArea;
