"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { DashboardLayout, Header, Sidebar } from "../components";
import { useChatHistory } from "../hooks/useChatHistory";
import { ClippingProject } from "../components/types";
import { formatDistanceToNow } from "date-fns";
import {
  Accounticon,
  Clippingicon,
  IdeaLabicon,
  Inspirationicon,
  Keywordicon,
  SocialPosticon,
  Storyboardicon,
  Youtubeicon,
} from "../components/icons/icons";
import ClippingHeader from "../components/clipping/ClippingHeader";
import ClippingInputBar from "../components/clipping/ClippingInputBar";
import ClippingTabs from "../components/clipping/ClippingTabs";
import AnalyzeModal from "../components/clipping/AnalyzeModal";

const sidebarMenu = [
  { label: "Inspiration", icon: <Inspirationicon />, active: false },
  { label: "Idea Lab", icon: <IdeaLabicon />, active: false },
  { label: "Social Post", icon: <SocialPosticon />, active: false },
  { label: "Clipping", icon: <Clippingicon />, active: true },
  { label: "Storyboard Editor", icon: <Storyboardicon />, active: false },
  { label: "YouTube Tools", icon: <Youtubeicon />, active: false },
  { label: "Keyword Insights", icon: <Keywordicon />, active: false },
  { label: "Account", icon: <Accounticon />, active: false },
];

export default function Clipping() {
  const router = useRouter();
  const [url, setUrl] = useState("");
  const [activeTab, setActiveTab] = useState<"recent" | "exported">("recent");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAnalyzeOpen, setIsAnalyzeOpen] = useState(false);
  const [recentProjects, setRecentProjects] = useState<ClippingProject[]>([]);
  const [isProjectsLoading, setIsProjectsLoading] = useState(false);

  // Chat history management for sidebar
  const {
    historySections,
    isLoading,
    clearAllHistory,
  } = useChatHistory();

  const handleMenuItemClick = (item: any) => {
    const routes: { [key: string]: string } = {
      Inspiration: "/dashboard",
      "Idea Lab": "/idealab",
      "Social Post": "/social-post",
      Clipping: "/clipping",
      "Storyboard Editor": "/storyboard",
      "YouTube Tools": "/youtube-tools",
      "Keyword Insights": "/keyword-insights",
      Account: "/account",
    };
    const route = routes[item.label];
    if (route) router.push(route);
  };

  const handleHistoryItemClick = (historyItem: any) => {
    // If a history item is clicked, check if it has a projectId in its metadata
    // or try to retrieve the last visited projectId from localStorage
    const targetProjectId = historyItem.projectId || localStorage.getItem("lastVisitedProjectId");

    if (targetProjectId) {
      router.push(`/clipping/results?projectId=${targetProjectId}`);
    } else if (historyItem.sessionId) {
      // Fallback to idealab if no projectId is found but sessionId exists
      router.push("/idealab");
    }
  };

  useEffect(() => {
    const fetchRecentProjects = async () => {
      if (activeTab === "recent") {
        console.log("Fetching recent clipping projects...");
        setIsProjectsLoading(true);
        try {
          const response = await fetch("/api/clipping/recent");
          if (response.ok) {
            const data = await response.json();
            console.log("Fetched recent projects:", data.projects);
            setRecentProjects(data.projects || []);
          } else {
            console.error("Failed to fetch recent clipping projects:", response.status, response.statusText);
            setRecentProjects([]);
          }
        } catch (error) {
          console.error("Error fetching recent clipping projects:", error);
          setRecentProjects([]);
        } finally {
          setIsProjectsLoading(false);
        }
      }
    };

    fetchRecentProjects();
  }, [activeTab]);

  const handleClearHistory = () => {
    clearAllHistory();
  };

  const handleSubmit = async () => {
    if (!url.trim()) return;
    setIsSubmitting(true);
    try {
      await new Promise((r) => setTimeout(r, 250));
      setIsAnalyzeOpen(true); // open analyze modal
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DashboardLayout
      header={<Header />}
      sidebar={
        <Sidebar
          menuItems={sidebarMenu}
          histories={historySections}
          isLoading={isLoading}
          onMenuItemClick={handleMenuItemClick}
          onHistoryItemClick={handleHistoryItemClick}
          onClearHistory={handleClearHistory}
        />
      }
    >
      <div
        className="min-h-screen w-full flex flex-col px-0 pt-10"
        style={{ fontFamily: "inherit", backgroundColor: "transparent" }}
      >
        <div
          className="w-full max-w-[95vw] flex flex-col items-start mx-auto"
          style={{ maxWidth: "1200px" }}
        >
          <ClippingHeader />

          <ClippingInputBar
            value={url}
            onChange={setUrl}
            onSubmit={handleSubmit}
            isLoading={isSubmitting}
          />

          <ClippingTabs active={activeTab} onChange={setActiveTab} />

          {activeTab === "recent" && (
            <div className="w-full mt-6">
              {isProjectsLoading ? (
                <div className="text-white/70 text-sm">Loading recent projects...</div>
              ) : recentProjects.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {recentProjects.map((project) => (
                    <div
                      key={project.id}
                      className="rounded-xl border border-[#2e3a52] bg-[#0d1715] p-4 cursor-pointer hover:border-[#4a427e] transition-colors"
                      onClick={() => router.push(`/clipping/results?projectId=${project.id}`)}
                    >
                      <div className="text-white font-medium mb-2">{project.title}</div>
                      <div className="text-white/70 text-sm">
                        Created {formatDistanceToNow(new Date(project.createdAt), { addSuffix: true })}
                      </div>
                      {project.thumbnailUrl && (
                        <img src={project.url} alt={project.title} className="w-full h-32 object-cover rounded-md mt-2" />
                      )}
                      {project.url && (
                        <a href={project.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline mt-2">
                          Open Project
                        </a>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-white/70 text-sm text-center py-10">No recent clipping projects found.</div>
              )}
            </div>
          )}

          <AnalyzeModal open={isAnalyzeOpen} onClose={() => setIsAnalyzeOpen(false)} />
        </div>
      </div>
    </DashboardLayout>
  );
}
