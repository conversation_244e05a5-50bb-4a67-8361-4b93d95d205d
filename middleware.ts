import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";

// Public paths (no auth required)
const PUBLIC_PATHS: RegExp[] = [
  /^\/$/,                        // Home
  /^\/login(?:\/.*)?$/,          // /login and subpaths
  /^\/signup(?:\/.*)?$/,         // /signup and subpaths
  /^\/forgot-password(?:\/.*)?$/, // /forgot-password and subpaths
  /^\/reset-password(?:\/.*)?$/, // /reset-password and subpaths
  /^\/api\/auth(?:\/.*)?$/,      // NextAuth endpoints
  /^\/api\/debug\/token$/,       // TEMP: debug token endpoint
  /^\/api\/debug\/cookies$/,     // TEMP: debug cookies endpoint
  /^\/_next(?:\/.*)?$/,          // Next.js assets
  /^\/favicon\.ico$/,            // Favicon
  /^\/public(?:\/.*)?$/,         // Static files served by public (note: typically served at root)
];

// Helper: does the path match any public rule?
function isPublicPath(pathname: string): boolean {
  return PUBLIC_PATHS.some((re) => re.test(pathname));
}

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Debug logs
  // Note: console.log here logs to the server (Edge runtime)
  console.log("[middleware] incoming:", pathname);

  // Skip auth checks for public routes
  if (isPublicPath(pathname)) {
    console.log("[middleware] public path allowed:", pathname);
    return NextResponse.next();
  }

  // Retrieve token (uses NEXTAUTH_SECRET)
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

  console.log("[middleware] token exists?", Boolean(token), "pathname:", pathname);

  // If no token and path is protected, redirect to /login
  if (!token) {
    const url = req.nextUrl.clone();
    url.pathname = "/login";
    url.searchParams.set("from", pathname); // carry original path for potential post-login redirect
    console.log("[middleware] unauthenticated, redirecting to /login from:", pathname);
    return NextResponse.redirect(url);
  }

  // Authenticated, allow request
  console.log("[middleware] authenticated, allowing:", pathname);
  return NextResponse.next();
}

// Configure matcher: run for all paths except Next.js internals we already guard
export const config = {
  // Match all paths so we can decide via code which are public
  matcher: "/:path*",
};