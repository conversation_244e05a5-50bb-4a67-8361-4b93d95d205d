# Lip Sync Feature Setup Guide

This guide explains how to set up the lip sync feature using Replicate APIs with Cloudinary for file storage.

## Prerequisites

1. **Replicate Account**: Sign up at [replicate.com](https://replicate.com)
2. **Replicate API Token**: Get your API token from your Replicate dashboard
3. **Cloudinary Account**: Sign up at [cloudinary.com](https://cloudinary.com)
4. **Cloudinary Credentials**: Get your cloud name, API key, and API secret

## Environment Variables

Create a `.env.local` file in your project root and add:

```env
# Replicate API Token (required)
REPLICATE_API_TOKEN=your_replicate_api_token_here

# Cloudinary Configuration (required)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Base URL for file uploads (update for production)
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

## Supported Models

### 1. Kling Lip Sync Model
- **Model**: [kwaivgi/kling-lip-sync](https://replicate.com/kwaivgi/kling-lip-sync)
- **Input**: Video file + Audio file
- **Output**: Video with synchronized lip movements
- **Use Case**: Syncs lip movements in existing videos
- **Cost**: ~$0.21 per run

### 2. Sonic Talking Face Model
- **Model**: [zsxkib/sonic](https://replicate.com/zsxkib/sonic)
- **Input**: Portrait image + Audio file
- **Output**: Talking face animation video
- **Use Case**: Creates animated avatars from static images
- **Features**: 
  - Expressive facial animations
  - Subtle head movements
  - High-quality lip sync
  - Global audio perception
- **Cost**: ~$0.21 per run

## How to Get Your Replicate API Token

1. Go to [replicate.com](https://replicate.com) and sign in
2. Navigate to your account settings
3. Find the "API Tokens" section
4. Create a new API token
5. Copy the token and paste it in your `.env.local` file

## How to Get Your Cloudinary Credentials

1. Go to [cloudinary.com](https://cloudinary.com) and sign up
2. Navigate to your dashboard
3. Find your Cloud Name, API Key, and API Secret
4. Copy these values to your `.env.local` file

## Features

The lip sync feature includes:

- **Cloudinary File Storage**: Reliable, globally accessible file URLs
- **File Upload**: Supports video (MP4, AVI, MOV, WMV) and audio (MP3, WAV) files
- **File Validation**: Size limits (100MB) and format validation
- **Progress Tracking**: Real-time progress updates during generation
- **Error Handling**: Comprehensive error messages for various failure scenarios
- **Chat Integration**: Generated videos appear in chat with download functionality

## Usage

### Kling Model (Video + Audio)
1. Select "Kling (Video + Audio)" model type
2. Upload a video file (MP4, AVI, MOV, WMV)
3. Upload an audio file (MP3, WAV)
4. Click "Generate Lip Sync"

### Sonic Model (Image + Audio)
1. Select "Sonic (Image + Audio)" model type
2. Upload a portrait image (JPEG, PNG, WebP)
3. Upload an audio file (MP3, WAV)
4. Optionally enable "Keep Original Resolution"
5. Click "Generate Talking Face"

## File Requirements

### Video Files (Kling Model)
- **Formats**: MP4, AVI, MOV, WMV
- **Max Size**: 100MB
- **Recommendation**: Clear, well-lit videos with visible faces

### Image Files (Sonic Model)
- **Formats**: JPEG, PNG, WebP
- **Max Size**: 10MB
- **Recommendation**: High-quality portrait images with frontal or near-frontal views

### Audio Files (Both Models)
- **Formats**: MP3, WAV
- **Max Size**: 50MB
- **Recommendation**: Clear speech with minimal background noise

## Technical Details

### API Endpoints
- **Upload**: `/api/upload` - Handles file uploads to Cloudinary
- **Lip Sync**: `/api/generate-lipsync` - Processes lip sync generation

### Processing Time
- **Kling Model**: Typically 2-5 minutes
- **Sonic Model**: Typically 3-5 minutes
- **Note**: Processing time varies based on input file sizes and complexity

### Error Handling
The system includes comprehensive error handling for:
- Missing API tokens
- Invalid file types
- File size limits
- Network errors
- API quota exceeded
- Processing failures

## Troubleshooting

### Common Issues

1. **"API token not configured"**
   - Check that `REPLICATE_API_TOKEN` is set in your `.env.local` file
   - Restart your development server after adding the token

2. **"Invalid file type"**
   - Ensure you're uploading supported file formats
   - Check file extensions match the expected types

3. **"File size too large"**
   - Compress your files to meet size requirements
   - Use lower resolution for faster processing

4. **"Processing failed"**
   - Check the console for detailed error messages
   - Try with different input files
   - Ensure audio quality is good for better results

### Performance Tips

1. **For Kling Model**: Use videos with clear, well-lit faces
2. **For Sonic Model**: Use high-quality portrait images with frontal views
3. **Audio Quality**: Use clear speech with minimal background noise
4. **File Sizes**: Smaller files process faster and cost less

## License Information

- **Kling Model**: Check the original model page for licensing details
- **Sonic Model**: Licensed under CC BY-NC-SA 4.0 (non-commercial research use only)

For commercial use, please check the respective model pages for licensing requirements.

## Pricing

- **Lip Sync Generation**: Uses the [Kling lip-sync model](https://replicate.com/kwaivgi/kling-lip-sync) on Replicate
- **File Storage**: Uses Cloudinary's free tier (25GB storage, 25GB bandwidth)
- Check both services for current pricing information

## File Storage

Files are uploaded to Cloudinary in the `lipsync-uploads` folder with the following structure:
- **Videos**: Stored as MP4 format
- **Audio**: Stored in original format (MP3, WAV)
- **Naming**: Timestamp-based unique identifiers
- **Access**: Public URLs for Replicate API access

## Error Handling

The system handles various error scenarios:

- **Missing API Tokens**: Clear error messages about configuration
- **Invalid Files**: Validation for file types and sizes
- **Cloudinary Upload Errors**: Specific upload error handling
- **Network Errors**: User-friendly network error messages
- **API Quota Exceeded**: Informative quota limit messages
- **Replicate API Errors**: Comprehensive API error handling

## Support

For issues with the APIs, refer to:
- [Replicate Documentation](https://replicate.com/docs)
- [Kling lip-sync Model Page](https://replicate.com/kwaivgi/kling-lip-sync)
- [Cloudinary Documentation](https://cloudinary.com/documentation) 