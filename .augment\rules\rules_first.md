---
type: "agent_requested"
description: "new component creation, new ui creation or any other new task"
---

You are an expert in TypeScript, Next.js 14 App Router, React, Tailwind CSS, NextAuth.js, and modern web app architecture.

Key Principles

- Write concise, technically accurate TypeScript code with reusable patterns.
- Use functional and declarative programming patterns; avoid classes.
- Always implement error handling and edge case checks early with guard clauses.
- Use descriptive variable names with auxiliary verbs (e.g., isAuthenticated, hasUploaded).
- Use lowercase with dashes for directory names (e.g., components/auth-form).
- Favor named exports and organize components into reusable, modular pieces.
- Place static content, constants, and interfaces at the bottom of the file.
- For each new task, refer to relevant documentation provided. If documentation is missing or unclear, suggest thoughtful and technically sound solutions instead.

Project Architecture

- Structure all new features using the existing `app/` directory and Next.js App Router conventions.
- Reuse and colocate components inside `/app/components` or `/src/components` for clarity and accessibility.
- Use `/lib` for shared utilities, client wrappers (e.g., Cloudinary, Replicate API), and helper functions.
- Use `/hooks` for all custom hooks and colocate hook-specific types.
- Maintain clean separation of concerns across API routes (e.g., auth, generation endpoints, chat history).
- Maintain reusable folder patterns for feature-based organization (e.g., `generate-image/`, `generate-video/`).

API Routes and Backend Logic

- Use Next.js API routes in `app/api/` for server-side operations.
- Prefer RESTful conventions with semantic naming (e.g., `POST /api/generate-music`).
- Validate request bodies early using Zod or custom logic; fail fast.
- Sanitize and normalize inputs before calling AI APIs (Replicate, Cloudinary).
- Handle MongoDB operations in reusable services or helper functions within `/lib`.

Authentication

- Use NextAuth.js with JWT strategy configured in `app/api/auth/[...nextauth]/route.ts`.
- Extract reusable logic for session checks and user guards in `/lib/auth.ts`.
- Use `getServerSession()` in Server Components for authentication logic.

UI and Styling

- Use Tailwind CSS for all styling. Avoid writing custom CSS unless necessary.
- Design all pages and components to be pixel-perfect, matching mockups and design specs precisely.
- Use consistent spacing, font sizing, and layout alignment based on Tailwind utility tokens.
- Build accessible, keyboard-navigable interfaces with aria tags and focus management.
- Use `class:` conditionals instead of ternaries when toggling Tailwind classes.
- Ensure components are responsive with mobile-first design principles.
- Prefer semantic HTML and descriptive Tailwind class combinations.
- Align design system tokens (e.g., border radius, padding, line height) to achieve consistent layout rhythm.
- Leverage design inspection tools (e.g., Figma or DevTools grid overlays) to ensure pixel alignment.

Reusability and Componentization

- Componentize all UI pieces into shared parts: buttons, icons, forms, modals, etc.
- Use `/app/components` for local components and `/src/components` for app-wide components.
- Colocate icons under `/icons` and keep them isolated and memoized if SVG-heavy.
- Use shared input components across pages to standardize form behavior.

Media Handling and Cloudinary

- Use `next/cloudinary` or Cloudinary REST SDK for media uploads.
- Normalize uploaded media metadata (e.g., file size, duration) and persist to MongoDB.
- Store public URLs only; avoid storing base64 in the database.
- Use Cloudinary transformations (e.g., resizing, quality) for optimization.

AI Model Integration (Replicate)

- Wrap model interactions in a reusable client inside `/lib/replicate.ts`.
- Ensure all model requests are type-safe and extensible.
- Validate inputs before calling the models and handle response validation and formatting.

State Management

- Prefer local state with React hooks over global state.
- For cross-component UI state (e.g., modals), use context API with custom hooks inside `/hooks`.

Performance and Optimization

- Use dynamic imports for non-critical components.
- Lazy load media-heavy UI sections.
- Optimize image usage with `next/image` and ensure width/height are always defined.
- Debounce input when querying AI models or keyword tools.

Security and Validation

- Sanitize all inputs server-side, especially those reaching third-party APIs.
- Use secure headers via Next.js middleware if needed.
- Ensure user input/output paths are validated (e.g., upload, chat-history).

Deployment and Tooling

- Use ESLint and Prettier for formatting and linting. Treat warnings as errors in CI.
- Add reusable CLI or Taskfile commands for generating new API endpoints, components, and pages.
- Ensure all API keys and secrets are accessed via environment variables.

Testing and QA

- Ensure form components and pages have basic unit tests with validation logic.

Naming Conventions

- Booleans: Prefix with is, has, should, can (e.g., `isVerified`, `hasSession`).
- Functions: Use verbs to describe action (e.g., `generateMusic`, `fetchUserData`).
- Components: PascalCase filenames and named exports.
- Files/Folders: lowercase with dashes (e.g., `forgot-password.tsx`, `generate-sound-effects/`).

Output Expectations

- Write complete code with all imports and dependencies.
- Avoid TODOs or unfinished examples.
- Suggest refactors if logic can be reused elsewhere.
- Give inline comments for critical decisions, especially around model use or media handling.

Refer to Next.js, Tailwind CSS, NextAuth.js, and Cloudinary documentation for best practices and API usage. If no documentation is available, apply best judgment and explain reasoning.
