import { Suspense } from "react";
import { GeistSans } from "geist/font/sans";
import "./globals.css";

// Import client providers directly inside a Suspense boundary.
// Note: app/providers.tsx is a Client Component ("use client") and safe to render in layout.
import Providers from "./providers";
import GoogleOAuthClient from "../app/providers-google-wrapper";

export const metadata = {
  title: "Keoo AI",
  description: "Your AI-powered assistant",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={GeistSans.className} suppressHydrationWarning>
        <Suspense fallback={null}>
          <Providers>
            <GoogleOAuthClient>{children}</GoogleOAuthClient>
          </Providers>
        </Suspense>
      </body>
    </html>
  );
}
