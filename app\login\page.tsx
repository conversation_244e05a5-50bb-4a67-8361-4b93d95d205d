"use client";
import { Suspense } from "react";
import Image from "next/image";
import LoginContent from "./login-content";

export default function Login() {
  return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-[#18182a] overflow-auto">
      {/* Left image section */}
      <div className="hidden lg:flex w-full lg:w-2/5 xl:w-1/2 min-h-[40vh] lg:min-h-screen items-center justify-center relative overflow-hidden">
        <Image
          src="/auth-img.gif"
          alt="Keoo Art"
          fill
          className="object-cover opacity-80 scale-105 hover:scale-100 transition-transform duration-700"
        />
        {/* Dark overlay for better shading */}
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="absolute inline-flex items-center top-3 sm:top-4 lg:top-6 xl:top-8 left-3 sm:left-4 lg:left-6 xl:left-8 z-10">
          <Image
            src="/logo_main2.png"
            alt="Keoo Logo"
            width={120}
            height={33}
            className=" drop-shadow-lg hover:scale-105 transition-transform duration-300"
          />
          {/* <span className=" text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-white align-middle">Keoo</span> */}

        </div>
      </div>
      {/* Right form section */}
      <Suspense fallback={<div className="flex w-full md:w-1/2 min-h-screen items-center justify-center text-[#b3b3c6]">Loading...</div>}>
        <LoginContent />
      </Suspense>
    </div>
  );
}