import React, { RefObject } from 'react';

interface MusicUploadModalProps {
  show: boolean;
  onClose: () => void;
  musicReferenceFiles: { song: string | null; voice: string | null; instrumental: string | null };
  setMusicReferenceFiles: (files: { song: string | null; voice: string | null; instrumental: string | null }) => void;
  musicReferenceType: 'song' | 'voice' | 'instrumental';
  setMusicReferenceType: (type: 'song' | 'voice' | 'instrumental') => void;
  musicModalLyrics: string;
  setMusicModalLyrics: (lyrics: string) => void;
  handleMusicModalSubmit: () => void;
  musicFileInputRef: RefObject<HTMLInputElement>;
  handleMusicFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  musicBitrate: number;
  setMusicBitrate: (bitrate: number) => void;
  musicSampleRate: number;
  setMusicSampleRate: (rate: number) => void;
}

const MusicUploadModal: React.FC<MusicUploadModalProps> = ({
  show,
  onClose,
  musicReferenceFiles,
  setMusicReferenceFiles,
  musicReferenceType,
  setMusicReferenceType,
  musicModalLyrics,
  setMusicModalLyrics,
  handleMusicModalSubmit,
  musicFileInputRef,
  handleMusicFileUpload,
  musicBitrate,
  setMusicBitrate,
  musicSampleRate,
  setMusicSampleRate,
}) => {
  if (!show) return null;

  const getReferenceTypeIcon = () => {
    switch (musicReferenceType) {
      case 'song':
        return (
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 mb-2">
            <path d="M9 18V5l12-2v13M9 18c0 1.657-1.343 3-3 3s-3-1.343-3-3 1.343-3 3-3 3 1.343 3 3zM21 16c0 1.657-1.343 3-3 3s-3-1.343-3-3 1.343-3 3-3 3 1.343 3 3z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
      case 'voice':
        return (
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 mb-2">
            <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M19 10v2a7 7 0 0 1-14 0v-2M12 19v4M8 23h8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
      case 'instrumental':
        return (
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 mb-2">
            <path d="M2 10v3M6 6v11M10 3v18M14 8v7M18 5v13M22 10v3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
    }
  };

  const getAcceptedFileTypes = () => {
    return ".mp3,.wav";
  };

  const getPlaceholderText = () => {
    switch (musicReferenceType) {
      case 'song':
        return "Upload a reference song to guide the music generation...";
      case 'voice':
        return "Upload a voice sample to guide the vocal style...";
      case 'instrumental':
        return "Upload an instrumental track to guide the music style...";
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1a1a2e] border border-[#2a2a3e] rounded-lg p-6 w-full max-w-lg max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">
            Music Generation Settings
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>

        {/* Reference Type Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">Reference Type</label>
          <div className="grid grid-cols-3 gap-2">
            {(['song', 'voice', 'instrumental'] as const).map((type) => (
              <button
                key={type}
                onClick={() => {
                  setMusicReferenceType(type);
                  // Preserve the uploaded files when switching tabs
                }}
                className={`p-3 rounded-lg border text-sm font-medium transition-colors relative ${
                  musicReferenceType === type
                    ? 'bg-[#6366f1] border-[#6366f1] text-white'
                    : 'bg-[#0a0f0d] border-[#2a2a3e] text-gray-300 hover:border-[#3a3a4e]'
                }`}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
                {musicReferenceFiles[type] && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-[#1a1a2e]"></span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Audio File Upload Area */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Upload {musicReferenceType.charAt(0).toUpperCase() + musicReferenceType.slice(1)} Reference
          </label>
          <div
            className="border-2 border-dashed border-[#2a2a3e] rounded-lg p-6 text-center cursor-pointer hover:border-[#3a3a4e] transition-colors"
            onClick={() => musicFileInputRef.current?.click()}
          >
            {musicReferenceFiles[musicReferenceType] ? (
              <div className="flex flex-col items-center">
                <div className="flex items-center justify-center w-16 h-16 bg-[#6366f1] rounded-lg mb-2">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                    <path d="M9 18V5l12-2v13M9 18c0 1.657-1.343 3-3 3s-3-1.343-3-3 1.343-3 3-3 3 1.343 3 3zM21 16c0 1.657-1.343 3-3 3s-3-1.343-3-3 1.343-3 3-3 3 1.343 3 3z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <span className="text-white text-sm font-medium">Audio file uploaded</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setMusicReferenceFiles({
                      ...musicReferenceFiles,
                      [musicReferenceType]: null
                    });
                  }}
                  className="mt-2 text-xs text-gray-400 hover:text-white transition-colors"
                >
                  Remove file
                </button>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                {getReferenceTypeIcon()}
                <span className="text-gray-400 text-sm">{getPlaceholderText()}</span>
                <span className="text-gray-500 text-xs mt-1">Supports MP3 and WAV files (15+ seconds required)</span>
              </div>
            )}
          </div>
          <input
            ref={musicFileInputRef}
            type="file"
            accept={getAcceptedFileTypes()}
            onChange={handleMusicFileUpload}
            className="hidden"
          />
        </div>

        {/* Lyrics Input */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">Lyrics (Optional)</label>
          <textarea
            value={musicModalLyrics}
            onChange={(e) => setMusicModalLyrics(e.target.value)}
            placeholder="Enter lyrics for the music generation..."
            className="w-full h-24 bg-[#0a0f0d] border border-[#2a2a3e] rounded-lg p-3 text-white text-sm resize-none focus:border-[#4a4a6e] focus:outline-none transition-colors"
          />
        </div>

        {/* Audio Quality Settings */}
        <div className="mb-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Bitrate</label>
              <select
                value={musicBitrate}
                onChange={(e) => setMusicBitrate(Number(e.target.value))}
                className="w-full bg-[#0a0f0d] border border-[#2a2a3e] rounded-lg p-2 text-white text-sm focus:border-[#4a4a6e] focus:outline-none transition-colors"
              >
                <option value={128000}>128 kbps</option>
                <option value={192000}>192 kbps</option>
                <option value={256000}>256 kbps</option>
                <option value={320000}>320 kbps</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Sample Rate</label>
              <select
                value={musicSampleRate}
                onChange={(e) => setMusicSampleRate(Number(e.target.value))}
                className="w-full bg-[#0a0f0d] border border-[#2a2a3e] rounded-lg p-2 text-white text-sm focus:border-[#4a4a6e] focus:outline-none transition-colors"
              >
                <option value={22050}>22.05 kHz</option>
                <option value={44100}>44.1 kHz</option>
                <option value={48000}>48 kHz</option>
              </select>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 bg-[#2a2a3e] text-white rounded-lg hover:bg-[#3a3a4e] transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleMusicModalSubmit}
            disabled={!musicReferenceFiles[musicReferenceType]}
            className="flex-1 px-4 py-2 bg-[#6366f1] text-white rounded-lg hover:bg-[#5855eb] disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
          >
            Generate Music
          </button>
        </div>
      </div>
    </div>
  );
};

export default MusicUploadModal;
