import { NextRequest, NextResponse } from 'next/server';
import { Supadata } from '@supadata/js';

const supadata = new Supadata({
  apiKey: process.env.SUPADATA_API_KEY,
});

export async function POST(req: NextRequest) {
  const { videoId } = await req.json();

  if (!videoId) {
    return NextResponse.json({ error: 'videoId is required' }, { status: 400 });
  }

  try {
    const response = await supadata.youtube.transcript({ videoId });
    
    let transcript = '';
    if (response && response.content) {
      if (Array.isArray(response.content)) {
        transcript = response.content.map((item: any) => item.text).join(' ');
      } else if (typeof response.content === 'string') {
        transcript = response.content;
      }
    }

    if (transcript) {
      return NextResponse.json({ transcript });
    } else {
      return NextResponse.json({ error: 'Failed to fetch or process transcript' }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Supadata API error:', error);
    return NextResponse.json({ error: error.message || 'Internal Server Error' }, { status: 500 });
  }}