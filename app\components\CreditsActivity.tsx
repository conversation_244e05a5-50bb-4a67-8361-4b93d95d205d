import React from "react";

interface CreditsActivityProps {
  remainingCredit: number;
  membershipCredit: number;
  topUpCredit: number;
  subscription: string;
}

const CreditsActivity: React.FC<CreditsActivityProps> = ({
  remainingCredit,
  membershipCredit,
  topUpCredit,
  subscription,
}) => {
  return (
    <div className="w-full flex flex-col gap-4">
      {/* Header Card */}
      <div className="rounded-xl overflow-hidden border border-[#a6a6c8] bg-gradient-to-r from-[#181c2b] to-[#887dff33] shadow-lg flex flex-col" style={{ minHeight: 80 }}>
        <div className="flex flex-row items-center px-4 py-3 gap-4 relative">
          <div className="flex-1">
            <div className="text-lg md:text-xl font-semibold text-white mb-1">Credits Activity</div>
            <div className="text-[15px] text-[#e5e7eb] font-medium leading-tight">
              Track how your credits are used across features—see generation history, usage breakdowns, and remaining balance at a glance.
            </div>
          </div>
          <div className="hidden md:block absolute right-0 top-0 h-full w-[220px] bg-gradient-to-tr from-[#887dff80] to-[#b3b3ff80] opacity-80 blur-xl pointer-events-none" />
        </div>
      </div>
      {/* Stats Row */}
      <div className="flex flex-row flex-wrap gap-8 px-2 py-2">
        <div className="flex flex-col min-w-[140px]">
          <span className="text-[#b3b3ff] text-base font-medium mb-1">Remaining Credit</span>
          <span className="text-white text-lg font-semibold tracking-wide">{remainingCredit}</span>
        </div>
        <div className="flex flex-col min-w-[140px]">
          <span className="text-[#b3b3ff] text-base font-medium mb-1">Membership Credit</span>
          <span className="text-white text-lg font-semibold tracking-wide">{membershipCredit}</span>
        </div>
        <div className="flex flex-col min-w-[140px]">
          <span className="text-[#b3b3ff] text-base font-medium mb-1">Top up  Credit</span>
          <span className="text-white text-lg font-semibold tracking-wide">{topUpCredit}</span>
        </div>
        <div className="flex flex-col min-w-[140px]">
          <span className="text-[#b3b3ff] text-base font-medium mb-1">Subscription</span>
          <span className="text-white text-lg font-semibold tracking-wide">{subscription}</span>
        </div>
      </div>
    </div>
  );
};

export default CreditsActivity;
