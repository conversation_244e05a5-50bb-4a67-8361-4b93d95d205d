"use client";
import Image from 'next/image';
import { HeaderProps } from './types';
import { useMobile } from './DashboardLayout';
import { Crediticon, Hamburger, NotificationIcon } from './icons/icons';

interface ExtendedHeaderProps extends HeaderProps {
  logoSrc?: string;
  logoAlt?: string;
  logoWidth?: number;
  logoHeight?: number;
}

export default function Header({
  // title = "create faster",
  // subtitle = "create smarter",
  credits = 1029,
  hasNotifications = true,
  userAvatar,
  logoSrc = "/logo_main2.png",
  logoAlt = "Keoo Logo",
  logoWidth = 120,
  logoHeight = 120
}: ExtendedHeaderProps) {
  const { toggleSidebar } = useMobile();

  return (
    <header className="fixed top-0 left-0 right-0 z-50 flex items-center justify-between px-4 sm:px-6 py-3 sm:py-4 bg-[#0a0f0d] border-b border-[#1a1a1a]">
      {/* Left side - Mobile menu + Logo */}
      <div className="flex items-center gap-3 sm:gap-8">
        {/* Mobile hamburger menu */}
        <button
          onClick={toggleSidebar}
          className="md:hidden p-2 hover:bg-[#1a1a1a] rounded-lg transition-colors"
          aria-label="Toggle sidebar"
        >
          <Hamburger />
        </button>

        <div className="flex items-center gap-2 sm:gap-3">
          <Image src={logoSrc} alt={logoAlt} width={logoWidth} height={logoHeight} />
          {/* <span className="text-lg sm:text-xl font-bold text-white">Keoo</span> */}
        </div>
      </div>

      {/* Right side - Credits, Notifications, Avatar */}
      <div className="flex items-center gap-2 sm:gap-4">
        <div className="hidden sm:flex items-center gap-2">
          <span className="text-[#6366f1] text-lg"><Crediticon />
          </span>
          <span className="text-[#9ca3af] text-sm sm:text-base font-medium">{credits.toLocaleString()}</span>
        </div>
        <button className="relative p-1.5 sm:p-2 hover:bg-[#1a1a1a] rounded-lg transition-colors">
          {hasNotifications && (
            <span className="absolute -top-0.5 -right-0.5 w-2 h-2 sm:w-2.5 sm:h-2.5 bg-[#ef4444] rounded-full"></span>
          )}
          <NotificationIcon />
        </button>
        <div className="w-7 h-7 sm:w-8 sm:h-8 bg-[#1a1a1a] rounded-full border border-[#2a2a2a] flex items-center justify-center">
          {userAvatar ? (
            <img src={userAvatar} alt="User" className="w-full h-full rounded-full object-cover" />
          ) : (
            <span className="text-[#9ca3af] text-xs sm:text-sm">👤</span>
          )}
        </div>
      </div>
    </header>
  );
}
