import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function POST(request: NextRequest) {
  try {
    const {
      lyrics,
      bitrate = 256000,
      sample_rate = 44100,
      song_file,
      voice_file,
      instrumental_file,
      voice_id,
      instrumental_id,
      reference_file,
      reference_type
    } = await request.json();

    console.log('Music generation request:', { lyrics, bitrate, sample_rate, reference_type });

    // Check if at least one reference is provided
    const hasReference = reference_file || song_file || voice_file || instrumental_file;

    if (!hasReference) {
      return NextResponse.json({
        success: false,
        error: 'At least one reference song, voice or instrumental is required'
      }, { status: 400 });
    }

    // Validate lyrics length if provided (350-400 characters max as per API docs)
    if (lyrics && lyrics.length > 400) {
      return NextResponse.json({
        success: false,
        error: 'Lyrics must be 400 characters or less'
      }, { status: 400 });
    }

    // Validate bitrate
    const validBitrates = [32000, 64000, 128000, 256000];
    if (!validBitrates.includes(bitrate)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid bitrate. Must be one of: 32000, 64000, 128000, 256000' 
      }, { status: 400 });
    }

    // Validate sample rate
    const validSampleRates = [16000, 24000, 32000, 44100];
    if (!validSampleRates.includes(sample_rate)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid sample rate. Must be one of: 16000, 24000, 32000, 44100' 
      }, { status: 400 });
    }

    // Prepare input for the Minimax Music-01 model
    const input: any = {
      bitrate,
      sample_rate
    };

    // Add lyrics if provided
    if (lyrics && lyrics.trim()) {
      input.lyrics = lyrics.trim();
    }

    // Handle reference file from modal
    if (reference_file && reference_type) {
      // Validate that it's a data URI for audio
      if (!reference_file.startsWith('data:audio/')) {
        return NextResponse.json({
          success: false,
          error: 'Invalid audio file format. Please upload a MP3 or WAV file.'
        }, { status: 400 });
      }

      switch (reference_type) {
        case 'song':
          input.song_file = reference_file;
          break;
        case 'voice':
          input.voice_file = reference_file;
          break;
        case 'instrumental':
          input.instrumental_file = reference_file;
          break;
      }
    }

    // Add optional parameters if provided (legacy support)
    if (song_file) input.song_file = song_file;
    if (voice_file) input.voice_file = voice_file;
    if (instrumental_file) input.instrumental_file = instrumental_file;
    if (voice_id) input.voice_id = voice_id;
    if (instrumental_id) input.instrumental_id = instrumental_id;

    console.log('Calling Minimax Music-01 with input:', input);

    // Call the Minimax Music-01 model
    const output = await replicate.run("minimax/music-01", { input });

    console.log('Minimax Music-01 response:', output);

    if (!output) {
      return NextResponse.json({ 
        success: false, 
        error: 'No output received from music generation model' 
      }, { status: 500 });
    }

    // The output should be a URI string
    const audioUrl = typeof output === 'string' ? output : output.toString();

    return NextResponse.json({ 
      success: true, 
      audioUrl,
      bitrate,
      sample_rate
    });

  } catch (error: any) {
    console.error('Music generation error:', error);
    
    // Handle specific Replicate errors
    if (error.message?.includes('prediction failed')) {
      // Check for specific audio format errors
      if (error.message?.includes('audio format') && error.message?.includes('not supported')) {
        return NextResponse.json({
          success: false,
          error: 'Audio format not supported. Please upload a MP3 or WAV file that is at least 15 seconds long.'
        }, { status: 400 });
      }

      return NextResponse.json({
        success: false,
        error: 'Music generation failed. Please try again with different lyrics or settings.'
      }, { status: 500 });
    }
    
    if (error.message?.includes('rate limit')) {
      return NextResponse.json({ 
        success: false, 
        error: 'Rate limit exceeded. Please try again later.' 
      }, { status: 429 });
    }

    if (error.message?.includes('authentication')) {
      return NextResponse.json({ 
        success: false, 
        error: 'Authentication failed. Please check API configuration.' 
      }, { status: 401 });
    }

    return NextResponse.json({ 
      success: false, 
      error: error.message || 'Failed to generate music' 
    }, { status: 500 });
  }
}
