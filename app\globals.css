@tailwind base;
@tailwind components;
@tailwind utilities;
:root {
  --background: #ffffff;
  --foreground: #030F0F;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* Custom animations for input fields and form elements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(124, 92, 255, 0.1),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce-slow {
  animation: bounce 2s infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* Loading spinner for buttons */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #ffffff;
  animation: spin 1s ease-in-out infinite;
}

/* Enhanced focus states for better accessibility */
input:focus,
button:focus {
  outline: 2px solid #6D6D6D;
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
input,
button,
a {
  transition: all 0.3s ease-in-out;
}

/* Floating label styles */
.peer:not(:placeholder-shown) ~ label,
.peer:focus ~ label {
  transform: translateY(-0.75rem) translateX(-0.25rem) scale(0.75);
  color: #6D6D6D;
  background-color: #18182a;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  z-index: 10;
  box-shadow: 0 0 0 2px #18182a;
}

.peer:focus ~ label {
  color: #6D6D6D;
  background-color: #18182a;
}

/* Enhanced input focus states to match the image */
.peer:focus {
  border-color: #6D6D6D !important;
  box-shadow: 0 0 0 3px rgba(124, 92, 255, 0.15) !important;
  outline: none;
}

/* Ensure label positioning is perfect */
.peer ~ label {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: left center;
  pointer-events: none;
}

/* Default label position */
.peer:placeholder-shown ~ label {
  transform: translateY(0) translateX(0) scale(1);
  color: #6b6b8a;
  background-color: transparent;
  padding: 0;
  box-shadow: none;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #18182a;
}

::-webkit-scrollbar-thumb {
  background: #35355a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a4a6a;
}

/* Custom height classes for image grid */
.h-68 {
  height: 17rem;
}

.h-76 {
  height: 19rem;
}

.h-84 {
  height: 21rem;
}

.h-88 {
  height: 22rem;
}

/* Custom styles for select dropdown options */
select option {
  background-color: #030F0F;
  color: #FFFFFF;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(117, 105, 247, 0.5);
  
  
}

select option:hover {
  background-color: #887DFF;

  color: #FFFFFF;
}

select option:checked {
  background-color: #887DFF;
  color: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(117, 105, 247, 0.5);
}

/* Chatbox collapse/expand animations */
@keyframes slideUp {
  from {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-in-out forwards;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-in-out forwards;
}

.animate-fade-in-out {
  animation: fadeInOut 0.3s ease-in-out;
}

/* Smooth chatbox transitions */
.chatbox-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chatbox-collapsed {
  transform: translateY(0);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.chatbox-expanded {
  transform: translateY(0);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Hide scrollbar for collapsed tool selection */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Responsive collapsed chatbox */
@media (max-width: 640px) {
  .chatbox-collapsed .tool-button {
    padding: 6px;
  }

  .chatbox-collapsed .tool-button .icon {
    width: 14px;
    height: 14px;
  }
}
