"use client";
import { ReactNode, useState, createContext, useContext } from 'react';

interface DashboardLayoutProps {
  children: ReactNode;
  sidebar: ReactNode;
  header: ReactNode;
  className?: string;
}

interface MobileContextType {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
}

const MobileContext = createContext<MobileContextType | undefined>(undefined);

export const useMobile = () => {
  const context = useContext(MobileContext);
  if (!context) {
    throw new Error('useMobile must be used within a DashboardLayout');
  }
  return context;
};

export default function DashboardLayout({
  children,
  sidebar,
  header,
  className = "min-h-screen flex flex-col bg-[#0a0f0d] text-white"
}: DashboardLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);
  const closeSidebar = () => setIsSidebarOpen(false);

  return (
    <MobileContext.Provider value={{ isSidebarOpen, toggleSidebar, closeSidebar }}>
      <div className={className}>
        {/* Fixed Header spans full width */}
        {header}

        {/* Content area with sidebar and main content - add top padding for fixed header */}
        <div className="flex flex-1 relative pt-16 sm:pt-20">
          {/* Mobile overlay */}
          {isSidebarOpen && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
              onClick={closeSidebar}
            />
          )}

          {/* Sidebar - responsive behavior */}
          <div className={`
            fixed top-16 sm:top-20 z-40 transition-transform duration-300 ease-in-out
            ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
            md:translate-x-0 md:z-auto
          `}>
            <div className={`${isSidebarOpen ? 'block' : 'hidden'} md:block`}>
              {sidebar}
            </div>
          </div>

          {/* Main content - add margins to account for fixed sidebars */}
          <main className="flex-1 flex flex-col w-full ml-0 md:ml-[250px] lg:ml-[280px] xl:ml-[300px] 2xl:ml-[320px] mr-0 xl:mr-[40px] 2xl:mr-[40px]">
            {children}
          </main>

          {/* Right Sidebar Panel - Fixed Position */}
          <div className="fixed right-0 top-16 sm:top-20 w-[50px] xl:w-[50px] 2xl:w-[50px] h-screen bg-[#0a0f0d] border-l border-[#1a1a1a] flex-shrink-0 hidden xl:flex flex-col z-30">
            {/* Right sidebar content */}
            <div className="p-4 pt-4">
             
            </div>
          </div>
        </div>
      </div>
    </MobileContext.Provider>
  );
}
