import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const audioUrl = searchParams.get('url');

  if (!audioUrl) {
    return new NextResponse('Missing audio URL', { status: 400 });
  }

  try {
    const response = await fetch(audioUrl);

    if (!response.ok) {
      return new NextResponse('Failed to fetch audio', { status: response.status });
    }

    const audioData = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'audio/mpeg';

    return new NextResponse(audioData, {
      headers: {
        'Content-Type': contentType,
        'Content-Length': String(audioData.byteLength),
      },
    });
  } catch (error) {
    console.error('Proxy error:', error);
    return new NextResponse('Error proxying audio', { status: 500 });
  }
}
