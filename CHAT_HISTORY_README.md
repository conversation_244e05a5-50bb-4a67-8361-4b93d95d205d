# Chat History Functionality

## Overview
The Idea Lab now includes a comprehensive chat history system that allows users to:
- Save and persist chat sessions across browser sessions
- Organize chat history by date (Today, Yesterday, Previous 7 Days)
- Load previous chat sessions
- Create new chat sessions
- Delete individual chat sessions
- Clear all chat history

## Features

### 1. Chat Session Management
- **Automatic Session Creation**: New sessions are created automatically when switching modes or starting fresh
- **Session Persistence**: All chat sessions are saved to localStorage and persist across browser sessions
- **Session Titles**: Sessions are automatically titled based on the first user message
- **Mode Tracking**: Each session tracks which generation mode was used (text-to-image, text-to-video, etc.)

### 2. Sidebar History Display
- **Organized by Date**: History is automatically organized into sections:
  - Today (blue color)
  - Yesterday (gray color)
  - Previous 7 Days (gray color)
- **Click to Load**: Click any history item to load that specific chat session
- **Clear History**: "Clear" button removes all chat history
- **Real-time Updates**: History updates automatically as new messages are added

### 3. Session Controls
- **New Chat Button**: Easily start a new chat session
- **Delete Session**: Delete the current chat session
- **Session Title Display**: Shows the current session title in the header

### 4. Message Display
- **User Messages**: Displayed on the right with blue background
- **AI Messages**: Displayed on the left with dark background
- **Media Support**: Images, videos, and audio are properly displayed
- **Download Options**: Hover over media to see download buttons

## Technical Implementation

### Files Modified/Created:
1. **`app/hooks/useChatHistory.ts`** - Main chat history management hook
2. **`app/components/types.ts`** - Added new types for chat history
3. **`app/components/Sidebar.tsx`** - Updated to use real chat history
4. **`app/idealab/page.tsx`** - Integrated chat history functionality
5. **`app/components/ChatMessagesArea.tsx`** - Updated to show both user and AI messages

### Key Functions:
- `createNewSession(mode)` - Create a new chat session
- `loadSession(sessionId)` - Load a specific chat session
- `addMessage(message)` - Add a message to the current session
- `deleteSession(sessionId)` - Delete a specific session
- `clearAllHistory()` - Clear all chat history
- `getHistorySections()` - Get organized history for sidebar display

### Data Structure:
```typescript
interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  mode: 'text-to-image' | 'image-to-image' | 'text-to-video' | 'image-to-video' | 'text-to-speech' | 'music-generation' | 'sound-effects';
}

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  images?: string[];
  videos?: string[];
  audioUrl?: string;
  uploadedImage?: string;
}
```

## Usage

### Starting a New Chat:
1. Click the "New Chat" button in the header
2. Or switch between different generation modes (this creates a new session)

### Loading Previous Chats:
1. Look at the sidebar under "Idea Lab Histories"
2. Click on any history item to load that session
3. The chat will load with all previous messages and the correct mode

### Managing Sessions:
1. **Delete Current Session**: Click the "Delete" button in the header
2. **Clear All History**: Click "Clear" in the sidebar history section
3. **View Session Info**: The current session title is displayed in the header

## Storage
- All chat history is stored in localStorage under the key `'keoo-ai-chat-history'`
- Data persists across browser sessions
- No server-side storage required (client-side only)

## Sample Data
For testing purposes, the system includes sample chat sessions when no history exists:
- "Futuristic workspace design" (text-to-image session)
- "Social media content ideas" (text-to-video session)

These samples help demonstrate the functionality and provide a starting point for testing. 
 