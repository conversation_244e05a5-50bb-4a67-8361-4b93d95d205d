"use client";
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  DashboardLayout,
  Header,
  Sidebar,
  ImageGrid,
  ImageModal,
  HistoryModal
} from '../components';
import { useChatHistory } from '../hooks/useChatHistory';
import type { ImageData } from '../components/types';
import { Inspirationicon, IdeaLabicon, SocialPosticon, Clippingicon, Storyboardicon, Youtubeicon, Keywordicon, Accounticon } from '../components/icons/icons';

// Icon components for better consistency
const sidebarMenu = [
  { label: "Inspiration", icon: <Inspirationicon />, active: true },
  { label: "Idea Lab", icon: <IdeaLabicon />, active: false },
  { label: "Social Post", icon: <SocialPosticon />, active: false },
  { label: "Clipping", icon: <Clippingicon />, active: false },
  { label: "Storyboard Editor", icon: <Storyboardicon />, active: false },
  { label: "YouTube Tools", icon: <Youtubeicon />, active: false },
  { label: "Keyword Insights", icon: <Keywordicon />, active: false },
  { label: "Account", icon: <Accounticon />, active: false },
];


const inspirationImages = [
  { src: "/inspiration/1.jpg", height: "h-80" },
  { src: "/inspiration/2.jpg", height: "h-56" },
  { src: "/inspiration/3.jpg", height: "h-64" },
  { src: "/inspiration/4.jpg", height: "h-96" },
  { src: "/inspiration/5.png", height: "h-48" },
  { src: "/inspiration/6.png", height: "h-72" },
  { src: "/inspiration/7.png", height: "h-60" },
  { src: "/inspiration/8.jpg", height: "h-52" },
  { src: "/inspiration/9.jpg", height: "h-88" },
  { src: "/inspiration/10.png", height: "h-64" },
  { src: "/inspiration/11.png", height: "h-76" },
  { src: "/inspiration/12.jpg", height: "h-84" },
  { src: "/inspiration/13.png", height: "h-68" },
  { src: "/inspiration/14.png", height: "h-56" },
  { src: "/inspiration/6.png", height: "h-72" },
  { src: "/inspiration/3.jpg", height: "h-60" },
];

export default function Dashboard() {
  const router = useRouter();
  const [modalImage, setModalImage] = useState<ImageData | null>(null);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);

  // Chat history management for sidebar
  const {
    historySections,
    isLoading,
    clearAllHistory
  } = useChatHistory();

  // Download handler
  const handleDownload = (src: string) => {
    const link = document.createElement('a');
    link.href = src;
    link.download = src.split('/').pop() || 'image';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle menu item clicks with navigation
  const handleMenuItemClick = (item: any, index: number) => {
    const routes: { [key: string]: string } = {
      "Inspiration": "/dashboard",
      "Idea Lab": "/idealab",
      "Social Post": "/social-post",
      "Clipping": "/clipping",
      "Storyboard Editor": "/storyboard",
      "YouTube Tools": "/youtube-tools",
      "Keyword Insights": "/keyword-insights",
      "Account": "/account"
    };

    const route = routes[item.label];
    if (route) {
      router.push(route);
    }
  };

  // Handle history item click to navigate to Idea Lab
  const handleHistoryItemClick = (historyItem: any, sectionIndex: number, itemIndex: number) => {
    if (historyItem.sessionId) {
      // Navigate to Idea Lab with the session
      router.push('/idealab');
    }
  };

  // Handle clear history
  const handleClearHistory = () => {
    clearAllHistory();
  };

  return (
    <DashboardLayout
      header={<Header />}
      sidebar={
        <Sidebar
          menuItems={sidebarMenu}
          histories={historySections}
          isLoading={isLoading}
          onMenuItemClick={handleMenuItemClick}
          onHistoryItemClick={handleHistoryItemClick}
          onClearHistory={handleClearHistory}
          onSeeMoreClick={() => setIsHistoryModalOpen(true)}
        />
      }
    >
      <ImageGrid
        images={inspirationImages}
        onImageClick={setModalImage}
        onDownload={handleDownload}
      />
      <ImageModal
        image={modalImage}
        isOpen={!!modalImage}
        onClose={() => setModalImage(null)}
        onDownload={handleDownload}
      />
      <HistoryModal
        open={isHistoryModalOpen}
        onClose={() => setIsHistoryModalOpen(false)}
        histories={historySections}
        onHistoryItemClick={handleHistoryItemClick}
      />
    </DashboardLayout>
  );
}
