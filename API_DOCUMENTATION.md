# KEOO-AI Project - Complete API Documentation

## Overview
The KEOO-AI project is a comprehensive AI-powered creative platform that provides various content generation capabilities including images, videos, music, speech, sound effects, and lip-sync generation. The application uses multiple AI models and services to deliver these features.

## Project Structure
- **Framework**: Next.js 14 with TypeScript
- **Authentication**: NextAuth.js with custom JWT implementation
- **Database**: MongoDB for user data and chat history
- **Media Storage**: Cloudinary for image, video, and audio storage
- **AI Models**: Replicate API for various AI model integrations

---

## API Endpoints

### 1. Image Generation API
**Endpoint**: `/api/generate-image`
**Method**: POST
**Purpose**: Generate images from text prompts or transform existing images

#### AI Models Used:
- **Flux 1.1 Pro Ultra** (`black-forest-labs/flux-1.1-pro-ultra`)
  - Primary model for high-quality text-to-image generation
  - Supports aspect ratios: 1:1, 16:9, 9:16, 4:3, 3:2, 21:9
  - Features: Raw mode for photorealistic style, safety tolerance settings
  
- **Flux Dev** (`black-forest-labs/flux-dev`)
  - Used for image-to-image transformations
  - Supports prompt strength parameter (0-1)
  
- **Flux 1.1 Pro** (`black-forest-labs/flux-1.1-pro`)
  - Alternative model for text-to-image generation

#### Features:
- Multiple generation modes: text-to-image, image-to-image
- Style enhancement (Photorealistic, Artistic styles)
- Configurable aspect ratios
- Multiple image outputs (1-4 images)
- Automatic upload to Cloudinary
- Binary data handling for streaming responses

#### Parameters:
- `prompt`: Text description for image generation
- `model`: Model selection (flux-1.1-pro-ultra, flux-dev-lora, flux-kontext-max)
- `style`: Style preference (Photorealistic, etc.)
- `ratio`: Aspect ratio (1:1, 16:9, 9:16, 4:3, 3:2, 21:9)
- `mode`: Generation mode (text-to-image, image-to-image)
- `imageUrl`: Input image URL for image-to-image mode
- `num_outputs`: Number of images to generate (1-4)

---

### 2. Video Generation API
**Endpoint**: `/api/generate-video`
**Method**: POST
**Purpose**: Generate videos from text prompts or images

#### AI Models Used:
- **Kling v1.6 Standard** (`kwaivgi/kling-v1.6-standard`)
  - Supports: text-to-video, image-to-video
  - Duration: 5-10 seconds
  - Aspect ratios: 16:9, 9:16, 1:1

- **Google Veo-3** (`google/veo-3`)
  - Supports: text-to-video, image-to-video
  - Duration: 5-10 seconds
  - Aspect ratios: 16:9, 9:16, 1:1

- **Wan 2.1 Models** (WaveSpeed AI):
  - `wavespeedai/wan-2.1-i2v-480p` - Image-to-video (480p)
  - `wavespeedai/wan-2.1-i2v-720p` - Image-to-video (720p)
  - `wavespeedai/wan-2.1-t2v-480p` - Text-to-video (480p)
  - `wavespeedai/wan-2.1-t2v-720p` - Text-to-video (720p)

- **MiniMax Video-01** (`minimax/video-01`)
  - Supports: text-to-video, image-to-video
  - Fixed duration: 6 seconds
  - Aspect ratios: 16:9, 9:16, 1:1

#### Features:
- Multiple generation modes: text-to-video, image-to-video
- Various quality options (480p, 720p)
- Configurable duration and aspect ratios
- Automatic upload to Cloudinary
- Model-specific parameter handling

#### Parameters:
- `prompt`: Text description for video generation
- `mode`: Generation mode (text-to-video, image-to-video)
- `imageUrl`: Input image URL for image-to-video mode
- `duration`: Video duration in seconds (5, 6, 10)
- `aspectRatio`: Video aspect ratio (16:9, 9:16, 1:1)
- `model`: Model selection (kling-v1.6-standard, google-veo-3, wan-2.1-*, minimax-video-01)

---

### 3. Music Generation API
**Endpoint**: `/api/generate-music`
**Method**: POST
**Purpose**: Generate music from lyrics and reference audio files

#### AI Models Used:
- **MiniMax Music-01** (`minimax/music-01`)
  - Supports lyrics-based music generation
  - Reference file support (song, voice, instrumental)
  - Configurable bitrate and sample rate

#### Features:
- Lyrics-based music generation (max 400 characters)
- Reference file support for style guidance
- Multiple audio quality options
- Support for different reference types (song, voice, instrumental)

#### Parameters:
- `lyrics`: Text lyrics for music generation (max 400 characters)
- `bitrate`: Audio bitrate (32000, 64000, 128000, 256000)
- `sample_rate`: Audio sample rate (16000, 24000, 32000, 44100)
- `reference_file`: Reference audio file as data URI
- `reference_type`: Type of reference (song, voice, instrumental)
- `song_file`, `voice_file`, `instrumental_file`: Legacy reference file parameters

---

### 4. Speech Generation API
**Endpoint**: `/api/generate-speech`
**Method**: POST
**Purpose**: Convert text to speech with various voice options

#### AI Models Used:
- **Kokoro-82m** (`jaaari/kokoro-82m`)
  - High-quality text-to-speech model
  - Multiple voice options
  - Configurable speed settings

#### Features:
- Text-to-speech conversion (max 2000 characters)
- Multiple voice options (default: af_bella)
- Speed control (0.1 to 5.0)
- Automatic upload to Cloudinary

#### Parameters:
- `text`: Text to convert to speech (max 2000 characters)
- `voice`: Voice selection (default: af_bella)
- `speed`: Speech speed (0.1 to 5.0, default: 1.0)

---

### 5. Sound Effects Generation API
**Endpoint**: `/api/generate-sound-effects`
**Method**: POST
**Purpose**: Generate sound effects from text descriptions

#### AI Models Used:
- **Tango** (`declare-lab/tango`)
  - Text-to-audio sound effects generation
  - Configurable generation parameters

#### Features:
- Text-to-sound effects generation
- Configurable model parameters
- Quality control settings

#### Parameters:
- `prompt`: Text description of the sound effect
- `model`: Model type (default: tango2)
- `steps`: Generation steps (default: 100)
- `guidance`: Guidance scale (default: 3)

---

### 6. Lip Sync Generation API
**Endpoint**: `/api/generate-lipsync`
**Method**: POST
**Purpose**: Generate lip-synced videos by matching audio to video or images

#### AI Models Used:
- **Kling Lip-Sync** (`kwaivgi/kling-lip-sync`)
  - Video-to-video lip synchronization
  - Requires video URL and audio file

- **Sonic Talking Face** (`zsxkib/sonic`)
  - Image-to-video lip synchronization
  - Requires image URL and audio file
  - Resolution preservation option

#### Features:
- Two model options for different input types
- Video-to-video and image-to-video lip sync
- Resolution preservation for Sonic model
- Long-running prediction handling (up to 50 minutes)

#### Parameters:
- `video_url`: Input video URL (for Kling model)
- `audio_file`: Audio file URL (required for both models)
- `image_url`: Input image URL (for Sonic model)
- `model_type`: Model selection (kling, sonic)
- `keep_resolution`: Resolution preservation (Sonic model only)

### 7. Youtube Content Generation API
**Endpoint**: `/api/generate-youtube-content`
**Method**: POST
**Purpose**: Generate various YouTube content (titles, descriptions, keywords, hashtags) from text or video transcripts

#### AI Models Used:
- **GPT-4o** (`openai/gpt-4o`)
  - Generates content based on specified type (titles, description, keywords, hashtags)

#### Features:
- Generates 5 hooky, attention-grabbing titles using the Problem-Agitate-Solve (PAS) copywriting formula.
- Generates YouTube video descriptions in a raw, direct, conversational tone, under 1500 characters, and keyword-optimized.
- Generates long-tail, mid-tail, and short-tail keywords.
- Generates 20 high-impact, searchable YouTube hashtags.

#### Parameters:
- `text`: Text or video transcript for content generation
- `type`: Type of content to generate (titles, description, keywords, hashtags)

---

### 8. File Upload API
**Endpoint**: `/api/upload`
**Method**: POST
**Purpose**: Upload files to Cloudinary storage

#### Features:
- Multi-format support (video, audio, image)
- File type validation
- Size limit enforcement (100MB)
- Automatic resource type detection
- Cloudinary integration

#### Supported File Types:
- **Video**: MP4, AVI, MOV, WMV
- **Audio**: MP3, WAV, MPEG
- **Image**: JPEG, JPG, PNG, WebP

#### Parameters:
- `file`: File to upload (multipart form data)

---

### 9. Audio Proxy API
**Endpoint**: `/api/proxy-audio`
**Method**: GET
**Purpose**: Proxy audio files from external URLs

#### Features:
- Audio URL proxying
- Content type preservation
- Error handling for failed requests

#### Parameters:
- `url`: Audio URL to proxy (query parameter)

---

### 10. Vizard Create Project API
**Endpoint**: `/api/vizard/create-project`
**Method**: POST
**Purpose**: Create a new video project in Vizard AI for advanced video clipping and editing.

#### Features:
- Supports video URL input.
- Configurable language, duration, and dimension ratio.
- Options for captions, emojis, silence removal, keywords, project naming, and template selection.
- Highlight feature for key moments.

#### Parameters:
- `videoUrl`: URL of the video to process.
- `langLabel`: Language of the video content (e.g., "English").
- `autoDuration`: Boolean to automatically determine video duration.
- `durationLabel`: Preferred length of the output video (e.g., "Short", "Medium", "Long").
- `dimensionsLabel`: Desired aspect ratio for the clip (e.g., "Portrait", "Landscape", "Square").
- `hook`: Boolean to enable headline/hook generation.
- `captions`: Boolean to enable subtitle generation.
- `emojis`: Boolean to enable emoji generation.
- `removeSilence`: Boolean to remove silent parts of the video.
- `maxClipNumber`: Maximum number of clips to generate (1-100).
- `keywords`: Keywords for content analysis.
- `projectName`: Name of the Vizard project.
- `templateId`: ID of the Vizard template to use.
- `highlight`: Boolean to enable highlight generation.

---

### 11. Vizard Query Project API
**Endpoint**: `/api/vizard/query`
**Method**: GET
**Purpose**: Query the status and retrieve results of a Vizard AI project.

#### Features:
- Real-time project status retrieval.
- Normalization of query responses for consistent data handling.

#### Parameters:
- `projectId`: Unique identifier of the Vizard project to query.

---

### 12. User Registration API
**Endpoint**: `/api/auth/register`
**Method**: POST
**Purpose**: Register new users with email verification

#### Features:
- Password hashing with bcrypt
- Email verification token generation
- Duplicate user checking
- Automated verification emails

#### Parameters:
- `name`: User's full name
- `email`: User's email address
- `password`: User's password

---

### 13. User Login API
**Endpoint**: `/api/auth/login`
**Method**: POST
**Purpose**: Authenticate users and generate session tokens

#### Features:
- Password verification
- JWT token generation
- HTTP-only cookie setting
- User session management

#### Parameters:
- `email`: User's email address
- `password`: User's password

---

### 14. Google OAuth API
**Endpoint**: `/api/auth/google`
**Method**: POST
**Purpose**: Authenticate users via Google OAuth

#### Features:
- Google access token verification
- User profile retrieval
- Automatic user creation
- JWT token generation

#### Parameters:
- `token`: Google OAuth access token

---

### 15. Apple OAuth API
**Endpoint**: `/api/auth/apple`
**Method**: POST
**Purpose**: Authenticate users via Apple Sign-In

#### Features:
- Apple ID token verification
- User profile extraction
- Automatic user creation
- JWT token generation

#### Parameters:
- `token`: Apple ID token

---

### 16. Forgot Password API
**Endpoint**: `/api/auth/forgot-password`
**Method**: POST
**Purpose**: Initiate password reset process

#### Features:
- Reset token generation
- Email sending
- Token expiration handling

#### Parameters:
- `email`: User's email address

---

### 17. Other Auth APIs
- **Reset Password**: `/api/auth/reset-password` - Complete password reset
- **Verify Email**: `/api/auth/verify-email` - Email verification
- **Logout**: `/api/auth/logout` - User logout
- **Me**: `/api/auth/me` - Get current user info
- **Signup**: `/api/auth/signup` - Alternative signup endpoint

---

## Third-Party Services and APIs

### 1. Replicate API
**Purpose**: AI model hosting and inference
**Models Used**:
- Image Generation: Flux models
- Video Generation: Kling, Veo-3, Wan, MiniMax
- Music Generation: MiniMax Music-01
- Speech Generation: Kokoro-82m
- Sound Effects: Tango
- Lip Sync: Kling Lip-Sync, Sonic
- Youtube Content Generation: GPT-4o

**Authentication**: API token-based
**Environment Variable**: `REPLICATE_API_TOKEN`

### 2. Cloudinary
**Purpose**: Media storage and CDN
**Features**:
- Image, video, and audio upload
- Automatic format optimization
- CDN delivery
- Resource management

**Authentication**: API key and secret
**Environment Variables**:
- `CLOUDINARY_CLOUD_NAME`
- `CLOUDINARY_API_KEY`
- `CLOUDINARY_API_SECRET`

### 3. MongoDB
**Purpose**: Database for user data and chat history
**Collections**:
- `users`: User accounts and profiles
- `chat_history`: Chat sessions and messages

**Authentication**: Connection string
**Environment Variables**:
- `MONGODB_URI`
- `MONGODB_DB`

### 4. Google APIs
**Purpose**: OAuth authentication and user info
**Services**:
- OAuth2 authentication
- User profile retrieval

**Authentication**: Client ID and secret
**Environment Variables**:
- `GOOGLE_CLIENT_ID`
- `GOOGLE_CLIENT_SECRET`

### 5. Apple APIs
**Purpose**: Apple Sign-In authentication
**Services**:
- ID token verification
- User authentication

**Authentication**: Bundle ID and configuration
**Environment Variables**:
- `APPLE_BUNDLE_ID`
- `APPLE_NONCE`

### 6. Email Service (SMTP)
**Purpose**: Email sending for verification and password reset
**Features**:
- Email verification
- Password reset emails
- Custom email templates

**Authentication**: SMTP credentials
**Environment Variables**:
- `EMAIL_SERVER_HOST`
- `EMAIL_SERVER_PORT`
- `EMAIL_SERVER_USER`
- `EMAIL_SERVER_PASSWORD`
- `EMAIL_FROM`

### 7. Vizard AI
**Purpose**: Video clipping and editing
**Authentication**: API Key
**Environment Variable**: `VIZARD_API_KEY`

---

## Environment Variables

### Required Environment Variables:
```
# Replicate API
REPLICATE_API_TOKEN=your_replicate_token
# OpenAI API
OpenAI_API_KEY=your_openai_api_key
# Vizard AI
VIZARD_API_KEY=your_vizard_api_key

# Cloudinary
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# MongoDB
MONGODB_URI=mongodb://your_connection_string
MONGODB_DB=your_database_name

# JWT
JWT_SECRET=your_jwt_secret

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Apple Sign-In
APPLE_BUNDLE_ID=your_apple_bundle_id
APPLE_NONCE=your_apple_nonce

# Email Service
EMAIL_SERVER_HOST=your_smtp_host
EMAIL_SERVER_PORT=your_smtp_port
EMAIL_SERVER_USER=your_smtp_user
EMAIL_SERVER_PASSWORD=your_smtp_password
EMAIL_FROM=your_from_email

# NextAuth
NEXTAUTH_URL=your_app_url
NEXTAUTH_SECRET=your_nextauth_secret
```

---

## Application Features

### 1. Content Generation
- **Image Generation**: Multiple AI models for various image styles
- **Video Generation**: Text-to-video and image-to-video conversion
- **Music Generation**: Lyrics-based music creation
- **Speech Generation**: Text-to-speech with voice options
- **Sound Effects**: Text-to-sound effects generation
- **Lip Sync**: Video lip synchronization
- **Youtube Content Generation**: Generate titles, descriptions, keywords, and hashtags for YouTube videos.
- **Vizard AI Integration**: Advanced video clipping and editing capabilities.

### 2. User Management
- **Authentication**: Email/password, Google OAuth, Apple Sign-In
- **Email Verification**: Automated email verification system
- **Password Reset**: Secure password reset workflow
- **Session Management**: JWT-based session handling

### 3. Data Management
- **File Upload**: Multi-format file upload to Cloudinary
- **Chat History**: Persistent chat session storage
- **User Profiles**: User account management
- **Media Storage**: Organized media file storage

### 4. User Interface
- **Dashboard**: Main application interface
- **Tool Selection**: Multiple AI tools access
- **History Management**: Chat history visualization
- **Media Gallery**: Generated content gallery

---

## Security Features

### 1. Authentication Security
- Password hashing with bcrypt
- JWT token-based authentication
- HTTP-only cookies for session management
- OAuth integration for secure third-party authentication

### 2. Input Validation
- File type and size validation
- Content length restrictions
- Parameter validation for all APIs
- Error handling and sanitization

### 3. API Security
- Rate limiting considerations
- Error message sanitization
- Secure environment variable handling
- HTTPS enforcement in production

---

## Error Handling

### 1. Common Error Responses
- **400 Bad Request**: Invalid parameters or input
- **401 Unauthorized**: Authentication failures
- **404 Not Found**: Resource not found
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server-side errors

### 2. Specific Error Handling
- **Replicate API**: Model-specific error handling
- **Cloudinary**: Upload and storage error handling
- **MongoDB**: Database connection and query errors
- **OAuth**: Authentication provider errors
- **OpenAI API**: Content generation errors
- **Vizard AI**: Video processing and project management errors

---

## Performance Considerations

### 1. Optimization Features
- **Streaming**: Binary data streaming for large files
- **Caching**: Cloudinary CDN for media delivery
- **Compression**: Automatic image and video optimization
- **Lazy Loading**: On-demand resource loading

### 2. Scalability
- **Database**: MongoDB for horizontal scaling
- **Media Storage**: Cloudinary for global CDN
- **API Limits**: Rate limiting and quota management
- **Resource Management**: Efficient memory handling

---

## Development and Deployment

### 1. Technology Stack
- **Frontend**: Next.js 14, React, TypeScript
- **Backend**: Next.js API routes
- **Database**: MongoDB
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS
- **File Upload**: Cloudinary

### 2. Development Tools
- **TypeScript**: Type safety and development experience
- **ESLint**: Code quality and consistency
- **Prettier**: Code formatting
- **Git**: Version control

This comprehensive documentation covers all APIs, models, and services used in the KEOO-AI project, providing a complete reference for development and maintenance.
