// Shared Vizard API types and helpers

export type VizardVideoType =
  | 1 // Remote file
  | 2 // YouTube
  | 3 // Google Drive
  | 4 // Vimeo
  | 5 // StreamYard
  | 6 // TikTok
  | 7 // Twitter(X)
  | 8 // Rumble
  | 9 // Twitch
  | 10 // Loom
  | 11 // Facebook
  | 12; // LinkedIn

export type VizardRatioOfClip = 1 | 2 | 3 | 4; // 1: 9:16, 2: 1:1, 3: 4:5, 4: 16:9

export interface CreateProjectBody {
  lang: string; // required
  preferLength: number[]; // required (use [0] for auto)
  videoUrl: string; // required
  videoType: VizardVideoType; // required
  ratioOfClip?: VizardRatioOfClip; // default 1
  templateId?: number;
  removeSilenceSwitch?: 0 | 1; // default 0
  maxClipNumber?: number; // 1..100
  keywords?: string;
  subtitleSwitch?: 0 | 1; // default 1
  headlineSwitch?: 0 | 1; // default 1
  projectName?: string;
  ext?: "mp4" | "3gp" | "avi" | "mov"; // required when videoType=1
  emojiSwitch?: 0 | 1; // default 0
  highlightSwitch?: 0 | 1; // default 0
}

export interface CreateProjectResponse {
  code: number;
  msg?: string;
  data?: {
    projectId: string | number;
    [k: string]: unknown;
  };
}

export interface QueryProjectResponse {
  code: number;
  msg?: string;
  data?: {
    projectId: string | number;
    status?: string; // unknown official enum; keep as string
    clips?: Array<{
      id?: string | number;
      title?: string;
      durationSeconds?: number;
      thumbnailUrl?: string;
      downloadUrl?: string;
      viralScore?: string;
      transcript?: string;
      [k: string]: unknown;
    }>;
    [k: string]: unknown;
  };
}

// UI-facing normalized clip
export interface NormalizedClip {
  id: string;
  title: string;
  durationSeconds: number;
  thumbnailUrl?: string;
  downloadUrl?: string;
  viralScore?: string;
  transcript?: string;
}

export interface NormalizedQueryResult {
  projectId: string;
  status?: string;
  clips: NormalizedClip[];
}

const BASE_URL =
  "https://elb-api.vizard.ai/hvizard-server-front/open-api/v1";

export function assertVizardApiKey(): string {
  const key =
    process.env.VizardAI_API_KEY;
  if (!key) {
    throw new Error(
      "Missing Vizard API key. Set VizardAI_API_KEY in your environment."
    );
  }
  return key;
}

export function inferVideoTypeFromUrl(url: string): {
  videoType: VizardVideoType;
  ext?: CreateProjectBody["ext"];
} {
  try {
    const u = new URL(url);
    const host = u.host.toLowerCase();

    // YouTube
    if (
      host.includes("youtube.com") ||
      host === "youtu.be" ||
      host.includes("youtu")
    ) {
      return { videoType: 2 };
    }

    // For remote file, infer extension
    const pathname = u.pathname.toLowerCase();
    const extMatch = pathname.match(/\.(mp4|mov|avi|3gp)(\?|#|$)/i);
    if (extMatch) {
      return {
        videoType: 1,
        ext: extMatch[1].toLowerCase() as CreateProjectBody["ext"],
      };
    }

    // Default to remote file without known ext (caller should validate)
    return { videoType: 1 };
  } catch {
    // If URL constructor fails, treat as remote file and let validation catch bad URL
    return { videoType: 1 };
  }
}

export function mapUiLanguageToVizard(langLabel: string): string {
  // Basic mapping; expand as needed
  const map: Record<string, string> = {
    English: "en",
    Spanish: "es",
    French: "fr",
  };
  return map[langLabel] || "en";
}

export function mapUiDurationToPreferLength(
  auto: boolean,
  label: string
): number[] {
  if (auto) return [0];
  // Example labels: "Less than 30s", "30-60s", "60-90s", "90s-3m", "1 min"
  const normalized = (label || "").toLowerCase();

  // Try to infer from common phrases
  if (
    normalized.includes("less") ||
    normalized.includes("<30") ||
    normalized.includes("30s") ||
    normalized.includes("under 30")
  ) {
    return [1];
  }
  if (normalized.includes("30-60") || normalized.includes("30 to 60") || normalized.includes("1 min")) {
    return [2];
  }
  if (normalized.includes("60-90") || normalized.includes("90s")) {
    return [3];
  }
  if (normalized.includes("3m") || normalized.includes("3 min") || normalized.includes("90s-3m")) {
    return [4];
  }

  // Fallback: if the label contains a number "> 0" guess ranges
  const num = parseInt(normalized.replace(/\D/g, ""), 10);
  if (!Number.isNaN(num)) {
    if (num < 30) return [1];
    if (num <= 60) return [2];
    if (num <= 90) return [3];
    return [4];
  }

  // Default auto
  return [0];
}

export function mapUiDimensionsToRatio(dimensions: string): VizardRatioOfClip {
  const d = dimensions.toLowerCase();
  if (d.includes("portrait")) return 1; // 9:16
  if (d.includes("square")) return 2; // 1:1
  if (d.includes("4:5")) return 3; // optional future mapping
  if (d.includes("landscape")) return 4; // 16:9
  return 1;
}

export async function vizardCreateProject(
  body: CreateProjectBody
): Promise<CreateProjectResponse> {
  const apiKey = assertVizardApiKey();

  const reqId = `vz-create-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
  const started = Date.now();

  // Shallow sanitize for logs
  const safeBody = {
    ...body,
    keywords: body.keywords ? "[REDACTED]" : undefined,
  };

  try {
    console.log(`[VIZARD][CREATE][REQ] id=${reqId} url=${BASE_URL}/project/create body=`, safeBody);

    const res = await fetch(`${BASE_URL}/project/create`, {
      method: "POST",
      headers: {
        "content-type": "application/json",
        // IMPORTANT: send the full key value, not masked
        VIZARDAI_API_KEY: apiKey,
      } as any,
      body: JSON.stringify(body),
    });

    const duration = Date.now() - started;
    let text: string | null = null;
    try {
      text = await res.text();
    } catch (e) {
      console.error(`[VIZARD][CREATE][RES][${reqId}] failed to read body:`, (e as any)?.message);
    }

    // Attempt JSON parse but preserve original text
    let json: any = null;
    try {
      json = text ? JSON.parse(text) : null;
    } catch (e) {
      console.error(`[VIZARD][CREATE][PARSE][${reqId}] JSON parse error:`, (e as any)?.message);
    }

    console.log(
      `[VIZARD][CREATE][RES] id=${reqId} status=${res.status} durationMs=${duration} json=`,
      json ?? "[non-json]",
      "rawLen=",
      text ? text.length : 0
    );

    if (!res.ok && !json) {
      throw new Error(`Vizard create: HTTP ${res.status} with non-JSON body`);
    }

    return (json ?? { code: -1, msg: "Non-JSON response", data: undefined }) as CreateProjectResponse;
  } catch (err) {
    const duration = Date.now() - started;
    console.error(`[VIZARD][CREATE][ERR] id=${reqId} durationMs=${duration} error=`, (err as any)?.message || err);
    throw err;
  }
}

export async function vizardQueryProject(
  projectId: string | number
): Promise<QueryProjectResponse> {
  const apiKey = assertVizardApiKey();

  const reqId = `vz-query-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
  const started = Date.now();

  try {
    const url = `${BASE_URL}/project/query/${projectId}`;
    console.log(`[VIZARD][QUERY][REQ] id=${reqId} url=${url}`);

    const res = await fetch(url, {
      method: "GET",
      headers: {
        // IMPORTANT: send the full key value, not masked
        VIZARDAI_API_KEY: apiKey,
      } as any,
    });

    const duration = Date.now() - started;
    let text: string | null = null;
    try {
      text = await res.text();
    } catch (e) {
      console.error(`[VIZARD][QUERY][RES][${reqId}] failed to read body:`, (e as any)?.message);
    }

    let json: any = null;
    try {
      json = text ? JSON.parse(text) : null;
    } catch (e) {
      console.error(`[VIZARD][QUERY][PARSE][${reqId}] JSON parse error:`, (e as any)?.message);
    }

    console.log(
      `[VIZARD][QUERY][RES] id=${reqId} status=${res.status} durationMs=${duration} json=`,
      json ?? "[non-json]",
      "rawLen=",
      text ? text.length : 0
    );

    if (!res.ok && !json) {
      throw new Error(`Vizard query: HTTP ${res.status} with non-JSON body`);
    }

    return (json ?? { code: -1, msg: "Non-JSON response", data: undefined }) as QueryProjectResponse;
  } catch (err) {
    const duration = Date.now() - started;
    console.error(`[VIZARD][QUERY][ERR] id=${reqId} durationMs=${duration} error=`, (err as any)?.message || err);
    throw err;
  }
}

export function normalizeQueryResponse(
  resp: any
): NormalizedQueryResult {
  // Support both documented top-level shape and previous data.* shape
  const topLevelProjectId = resp?.projectId ?? resp?.data?.projectId ?? "";
  const projectId = String(topLevelProjectId ?? "");

  // Determine status: treat code 1000 as processing
  const status =
    resp?.data?.status ??
    (resp?.code === 1000 ? "processing" : resp?.status) ??
    undefined;

  // Prefer "videos" per Vizard docs; fallback to data.clips (legacy)
  const videos = Array.isArray(resp?.videos) ? resp.videos : null;
  const clipsRaw = videos ?? ((resp?.data as any)?.clips ?? []);

  const clips: NormalizedClip[] = Array.isArray(clipsRaw)
    ? clipsRaw.map((c: any, idx: number) => {
        // Map Vizard fields -> our UI
        const id = String(c?.videoId ?? c?.id ?? idx + 1);
        const title = String(c?.title ?? `Clip ${idx + 1}`);
        const durationMs = Number(c?.videoMsDuration ?? (c?.durationSeconds ? c.durationSeconds * 1000 : 0));
        const durationSeconds = Math.max(0, Math.round(durationMs / 1000));
        const downloadUrl = c?.videoUrl ?? c?.downloadUrl;
        //add viralScore?: string;
        const viralScore =String(c?.viralScore);
        const transcript = c?.transcript;
        // thumbnail not provided in docs response; keep undefined if absent
        const thumbnailUrl = c?.thumbnailUrl;

        return {
          id,
          title,
          durationSeconds,
          thumbnailUrl,
          downloadUrl,
          viralScore,
          transcript,
        };
      })
    : [];

  return {
    projectId,
    status,
    clips,
  };
}