import React, { useState } from "react";
import { CheckboxSubIcon, CheckmarkIcon } from "./icons/icons";
import CreditsPlans from "./CreditsPlans";

/**
 * Pixel-perfect subscription plans screen with SVG background.
 * Matches the design provided by user.
 */
const SubscriptionPlans: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'plans' | 'credits'>('plans');
  const [isYearly, setIsYearly] = useState(true);

  return (
    <div className="relative w-full min-h-[100vh] flex flex-col items-center justify-start bg-[#0a0f0d] py-10 overflow-x-hidden">
      {/* SVG Background */}
      <div className="absolute left-1/2 -translate-x-1/2 top-0 z-0 pointer-events-none select-none">
        {/* <svg width="253" height="304" viewBox="0 0 253 304" fill="none" xmlns="http://www.w3.org/2000/svg" className="hidden md:block" style={{ filter: 'blur(0.5px)' }}>
          <path fillRule="evenodd" clipRule="evenodd" d="M310.854 273.799C290.467 285.965 269.109 298.157 259.55 302.679C256.754 304.245 253.151 303.658 251.017 301.11C176.305 212.104 109.643 239.608 35.6699 270.132C25.6902 274.248 15.5807 278.419 5.10504 282.458C1.94339 283.96 -1.89639 282.815 -3.69438 279.737L-51.3998 198.076L-51.3747 198.061C-53.2648 194.826 -52.1728 190.667 -48.9374 188.777C-31.7016 178.624 -13.1731 166.009 3.26601 151.56C16.4409 139.98 28.1818 127.279 36.669 113.836C36.7985 113.61 36.937 113.39 37.0952 113.175C84.5436 48.3144 61.6455 -4.18676 53.5543 -22.7323C51.322 -27.8486 49.8923 -31.1244 50.6459 -34.263C50.8204 -34.9547 51.101 -35.6364 51.5048 -36.2784C53.5191 -39.497 117.316 -76.3823 145.699 -92.7961C148.618 -94.4847 151.06 -95.8953 157.842 -99.8572C160.515 -101.414 164.009 -100.995 166.224 -98.6506C190.209 -73.3168 199.534 -0.384945 188.979 55.0128C185.764 71.8901 180.714 87.2923 173.69 99.4296C166.24 112.305 156.524 121.625 144.346 125.474C131.999 129.376 117.571 127.545 101.008 117.915L107.615 142.235C108.152 144.23 107.784 146.448 106.413 148.206L88.9363 170.65L116.535 166.456C118.678 166.049 120.98 166.674 122.629 168.336L139.724 185.484C138.594 179.178 137.767 173.525 137.562 168.337C136.661 145.185 146.528 134.457 189.39 132.762L189.633 132.749C221.949 131.774 254.722 140.256 283.438 155.994C311.925 171.605 336.47 194.363 352.686 222.122C353.803 224.034 355.036 226.268 356.381 228.809C357.721 231.331 358.868 233.61 359.836 235.637L359.823 235.645C361.118 238.337 360.508 241.665 358.14 243.716C353.769 247.527 332.773 260.71 310.846 273.793L310.854 273.799Z" fill="#887DFF" fillOpacity="0.05" />
        </svg> */}
      </div>

      {/* Subscription Header */}
      <div className="relative z-10 w-full h-60 max-w-7xl rounded-2xl bg-gradient-to-r from-[#17172b] to-[#c7bfff1a] p-6 flex flex-col md:flex-row items-center gap-6 mb-8 mt-2 overflow-hidden border border-[#2e2e3e] shadow-xl">
        <div className="flex-1">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">Manage Your Subscription</h2>
          <p className="text-[#e5e7eb] text-sm md:text-base font-medium leading-relaxed">View your current plan, upgrade or downgrade, and keep track of your billing cycle and usage limits—all in one place.</p>
        </div>
        <div className="hidden md:block w-1/3 h-full">
          <div className="relative w-full h-full">
            <div className="absolute right-0 top-1/2 -translate-y-1/2 w-64 h-64 bg-[#887dff] opacity-10 rounded-full blur-xl"></div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex items-center gap-4 mb-8 relative z-10">
        <button
          className={`px-6 py-2 rounded-full font-semibold transition shadow border ${activeTab === 'plans' ? 'bg-[#887dff] text-white border-[#887dff]' : 'bg-transparent text-[#e5e7eb] border-transparent hover:border-[#887dff]'}`}
          onClick={() => setActiveTab('plans')}
        >
          Pricing Plans
        </button>
        <button
          className={`px-6 py-2 rounded-full font-semibold transition shadow border ${activeTab === 'credits' ? 'bg-[#887dff] text-white border-[#887dff]' : 'bg-transparent text-[#e5e7eb] border-transparent hover:border-[#887dff]'}`}
          onClick={() => setActiveTab('credits')}
        >
          Credits
        </button>
      </div>
      {activeTab === 'plans' ? (
        <>
          <div className="relative z-10 flex items-center gap-2 mb-8">
            <button
              className={`px-4 py-1 rounded-full text-xs font-semibold ${isYearly ? 'bg-[#887dff] text-white' : 'bg-transparent text-[#b0b0b7] border border-[#232337]'} cursor-pointer hover:border-[#887dff] transition-colors`}
              onClick={() => setIsYearly(true)}
            >
              Yearly <span className="text-[#b0b0b7]">-34%</span>
            </button>
            <button
              className={`px-4 py-1 rounded-full text-xs font-semibold ${!isYearly ? 'bg-[#887dff] text-white' : 'bg-transparent text-[#b0b0b7] border border-[#232337]'} cursor-pointer hover:border-[#887dff] transition-colors`}
              onClick={() => setIsYearly(false)}
            >
              Monthly <span className="text-[#b0b0b7]">+52%</span>
            </button>
          </div>

          {/* Pricing Plans */}
          <div className="relative z-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full max-w-7xl px-4 md:px-0">
            {/* Basic Plan */}
            <div className="bg-gradient-to-b from-[#191c23] to-[#181c2b] border border-[#232337] rounded-2xl p-6 flex flex-col items-center shadow-xl min-h-[430px]">
              <h3 className="text-white font-semibold text-lg mb-2">Basic</h3>
              <div className="text-[2rem] font-bold text-white mb-1">$ <span className="text-4xl">{isYearly ? '0' : '0'}</span></div>
              <div className="text-xs text-[#b0b0b7] mb-2">{isYearly ? 'per year' : 'per month'}</div>
              <button className="w-full bg-[#232337] text-[#b0b0b7] rounded-full py-2 mb-4 mt-2 font-semibold cursor-default border border-[#232337]">Current</button>
              <ul className="text-sm text-[#b0b0b7] space-y-2 mt-2 w-full">
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />                  <span>Free monthly login credits</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Limited feature access</span>
                </li>
              </ul>
            </div>
            {/* Standard Plan */}
            <div className="bg-gradient-to-b from-[#232337] to-[#191c23] border border-[#887dff] rounded-2xl p-6 flex flex-col items-center shadow-xl min-h-[430px]">
              <h3 className="text-white font-semibold text-lg mb-2">Standard</h3>
              <div className="text-[2rem] font-bold text-white mb-1">$ <span className="text-4xl">{isYearly ? '29.0' : '2.9'}</span></div>
              <div className="text-xs text-[#b0b0b7] mb-2">{isYearly ? 'per year' : 'per month'} <span className="line-through text-[#6d6d6d] ml-1">{isYearly ? '$49.0' : '$4.9'}</span></div>
              <div className="text-xs text-[#b0b0b7] mb-1 w-full text-left">50% off next year | Cancel Anytime</div>
              <button className="w-full bg-[#bcb7ff33] text-[#d1cfff] rounded-full py-2 mb-4 mt-2 font-semibold border border-[#887dff] hover:bg-[#887dff] hover:text-white transition">Subscribe Standard Yearly</button>
              <div className="text-xs text-[#b0b0b7] mb-4">230 <span className="font-bold text-white">Credits per month</span> <span className="ml-1 text-[#6d6d6d]">50% off</span><br /><span className="text-[#6d6d6d]">$1.00 per 100 Credits</span></div>
              <ul className="text-sm text-[#b0b0b7] space-y-2 mt-2 text-left w-full">
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Fast content generation</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Pro mode for videos</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>No watermark</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Idea lab tools</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Post scheduling</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Video clipping</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>YouTube tools</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Keyword insights</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Storyboard editor</span>
                </li>
              </ul>
            </div>
            {/* Pro Plan */}
            <div className="bg-gradient-to-b from-[#232337] to-[#191c23] border border-[#232337] rounded-2xl p-6 flex flex-col items-center shadow-xl min-h-[430px]">
              <h3 className="text-white font-semibold text-lg mb-2">Pro</h3>
              <div className="text-[2rem] font-bold text-white mb-1">$ <span className="text-4xl">{isYearly ? '129.0' : '12.9'}</span></div>
              <div className="text-xs text-[#b0b0b7] mb-2">{isYearly ? 'per year' : 'per month'} <span className="line-through text-[#6d6d6d] ml-1">{isYearly ? '$249.0' : '$24.9'}</span></div>
              <div className="text-xs text-[#b0b0b7] mb-1 w-full text-left">50% off next year | Cancel Anytime</div>
              <button className="w-full bg-[#232337] text-[#b0b0b7] rounded-full py-2 mb-4 mt-2 font-semibold border border-[#232337] hover:bg-[#887dff] hover:text-white transition">Subscribe Pro Yearly</button>
              <div className="text-xs text-[#b0b0b7] mb-4">230 <span className="font-bold text-white">Credits per month</span> <span className="ml-1 text-[#6d6d6d]">50% off</span><br /><span className="text-[#6d6d6d]">$1.00 per 100 Credits</span></div>
              <ul className="text-sm text-[#b0b0b7] space-y-2 mt-2 text-left w-full">
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Fast content generation</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Pro mode for videos</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>No watermark</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Idea lab tools</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Post scheduling</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Video clipping</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>YouTube tools</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Keyword insights</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Storyboard editor</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Early access to new features</span>
                </li>
              </ul>
            </div>
            {/* Premier Plan */}
            <div className="bg-gradient-to-b from-[#232337] to-[#191c23] border-2 border-[#19e3e6] rounded-2xl p-6 flex flex-col items-center shadow-xl min-h-[430px] relative">
              <span className="absolute top-4 right-4 bg-[#19e3e6] text-xs text-[#191c23] font-bold px-3 py-1 rounded-full">Best offer</span>
              <h3 className="text-white font-semibold text-lg mb-2">Premier</h3>
              <div className="text-[2rem] font-bold text-white mb-1">$ <span className="text-4xl">{isYearly ? '229.0' : '22.9'}</span></div>
              <div className="text-xs text-[#b0b0b7] mb-2">{isYearly ? 'per year' : 'per month'} <span className="line-through text-[#6d6d6d] ml-1">{isYearly ? '$449.0' : '$44.9'}</span></div>
              <div className="text-xs text-[#b0b0b7] mb-1 w-full text-left">50% off next year | Cancel Anytime</div>
              <button className="w-full bg-[#19e3e6] text-[#191c23] rounded-full py-2 mb-4 mt-2 font-semibold border border-[#19e3e6] hover:bg-[#16c4c6] hover:text-white transition">Subscribe Premier Yearly</button>
              <div className="text-xs text-[#b0b0b7] mb-4">230 <span className="font-bold text-white">Credits per month</span> <span className="ml-1 text-[#6d6d6d]">50% off</span><br /><span className="text-[#6d6d6d]">$1.00 per 100 Credits</span></div>
              <ul className="text-sm text-[#b0b0b7] space-y-2 mt-2 text-left w-full">
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Fast content generation</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Pro mode for videos</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>No watermark</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Idea lab tools</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Post scheduling</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Video clipping</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>YouTube tools</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Keyword insights</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Storyboard editor</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckboxSubIcon />
                  <span>Early access to new features</span>
                </li>
              </ul>
            </div>
          </div>
        </>
      ) : (
        <CreditsPlans />
      )}
    </div>

  );
};

export default SubscriptionPlans;
