import React, { useRef, useState, useEffect } from "react";
import { DownloadIconaudio, ShareIcon } from "./icons/icons";

interface AudioPlayerProps {
    src: string;
}

const formatTime = (sec: number) => {
    const m = Math.floor(sec / 60);
    const s = Math.floor(sec % 60);
    return `${m}:${s.toString().padStart(2, "0")}`;
};

const AudioPlayer: React.FC<AudioPlayerProps> = ({ src }) => {
    const audioRef = useRef<HTMLAudioElement>(null);
    const [playing, setPlaying] = useState(false);
    const [current, setCurrent] = useState(0);
    const [duration, setDuration] = useState(0);
    const [volume, setVolume] = useState(1);

    useEffect(() => {
        if (audioRef.current && src) {
            audioRef.current.src = src;
        }
    }, [src]);

    const togglePlay = () => {
        if (!audioRef.current) return;
        if (playing) {
            audioRef.current.pause();
        } else {
            audioRef.current.play();
        }
        setPlaying(!playing);
    };

    const onTimeUpdate = () => {
        if (!audioRef.current) return;
        setCurrent(audioRef.current.currentTime);
    };

    const onLoadedMetadata = () => {
        if (!audioRef.current) return;
        setDuration(audioRef.current.duration);
    };

    const onSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!audioRef.current) return;
        const time = Number(e.target.value);
        audioRef.current.currentTime = time;
        setCurrent(time);
    };

    const onVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!audioRef.current) return;
        const v = Number(e.target.value);
        audioRef.current.volume = v;
        setVolume(v);
    };

    const handleDownload = () => {
        const link = document.createElement("a");
        link.href = src;
        link.download = `audio-${Date.now()}.mp3`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleShare = async () => {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: "Audio File",
                    url: src,
                });
                console.log("Shared successfully!");
            } catch (error) {
                console.error("Error sharing:", error);
                // Fallback: Copy to clipboard
                await navigator.clipboard.writeText(src);
                alert("Copied audio URL to clipboard!");
            }
        } else {
            // Fallback: Copy to clipboard
            await navigator.clipboard.writeText(src);
            alert("Web Share API not supported. Copied audio URL to clipboard!");
        }
    };

    return (
        <div className="w-full bg-[#121212]  border-[#887DFF] border-[0.5px]  rounded-full flex items-center px-3 py-1.5 gap-1" style={{ height: 76 }}>
            <audio
                ref={audioRef}
                src={src}
                onTimeUpdate={onTimeUpdate}
                onLoadedMetadata={onLoadedMetadata}
                onEnded={() => setPlaying(false)}
                style={{ display: "none" }}
            />
            {/* Play/Pause */}
            <button onClick={togglePlay} className="focus:outline-none mr-1 flex items-center justify-center" style={{ width: 20, height: 20 }}>
                {playing ? (
                    <svg width="14" height="14" fill="none" viewBox="0 0 24 24"><rect x="6" y="4" width="4" height="16" rx="2" fill="#fff" /><rect x="14" y="4" width="4" height="16" rx="2" fill="#fff" /></svg>
                ) : (
                    <svg width="14" height="14" fill="none" viewBox="0 0 24 24"><polygon points="6,4 20,12 6,20" fill="#fff" /></svg>
                )}
            </button>
            {/* Time */}
            <span className="text-[10px] text-white whitespace-nowrap opacity-80" style={{ fontSize: "9px" }}>{formatTime(current)} / {formatTime(duration)}</span>
            {/* Progress bar */}
            <div className="flex-1 flex items-center mx-2 relative">
                <input
                    type="range"
                    min={0}
                    max={duration || 1}
                    value={current}
                    onChange={onSeek}
                    className="w-full h-1"
                    style={{
                        background: `linear-gradient(to right, #887DFF ${(current / (duration || 1)) * 100}%, #333 ${(current / (duration || 1)) * 100}%)`,
                        borderRadius: 4,
                        height: 2,
                        appearance: "none",
                        WebkitAppearance: "none",
                        outline: "none",
                        border: "none",
                        cursor: "pointer",
                        position: "relative"
                    }}
                />
            </div>
            {/* Volume icon */}
            <button className="focus:outline-none ml-1 flex items-center justify-center" style={{ width: 20, height: 20 }}>
                <svg width="14" height="14" fill="none" viewBox="0 0 24 24">
                    <path d="M11 5L6 9H2v6h4l5 4V5z" fill="#fff" opacity="0.8" />
                    <path d="M15 8a5 5 0 0 1 0 8" stroke="#fff" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" opacity="0.8" />
                </svg>
            </button>
            {/* Volume slider */}
            <input
                type="range"
                min={0}
                max={1}
                step={0.01}
                value={volume}
                onChange={onVolumeChange}
                className="w-10 h-1"
                style={{
                    background: `linear-gradient(to right, #887DFF ${volume * 100}%, #333 ${volume * 100}%)`,
                    borderRadius: 4,
                    height: 2,
                    appearance: "none",
                    WebkitAppearance: "none",
                    outline: "none",
                    border: "none",
                    cursor: "pointer",
                    position: "relative"
                }}
            />
            {/* Download icon */}
            <button className="focus:outline-none ml-1 flex items-center justify-center" style={{ width: 20, height: 20 }} title="Download" onClick={handleDownload}>
                <DownloadIconaudio />
            </button>
            {/* Share icon */}
            <button className="focus:outline-none ml-1 flex items-center justify-center" style={{ width: 20, height: 20 }} title="Share" onClick={handleShare}>
                <ShareIcon />
            </button>
        </div>
    );
};

export default AudioPlayer;
