import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "../../../../lib/mongodb";
import jwt from "jsonwebtoken";
import { verifyIdToken } from "apple-signin-auth";

const JWT_SECRET = process.env.JWT_SECRET!;

export async function POST(req: NextRequest) {
  try {
    const { token } = await req.json();

    if (!token) {
      return NextResponse.json(
        { error: "Token is required" },
        { status: 400 }
      );
    }

    const payload = await verifyIdToken(token, {
      audience: process.env.APPLE_BUNDLE_ID,
      nonce: process.env.APPLE_NONCE,
    });

    const { email, sub: appleId } = payload;
    if (!email || !appleId) {
      return NextResponse.json(
        { error: "Invalid token payload" },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const users = db.collection("users");

    // Find or create user
    let user = await users.findOne({ email });
    if (!user) {
      const result = await users.insertOne({
        email,
        name: email.split('@')[0],
        appleId,
        emailVerified: true,
        provider: 'apple',
        createdAt: new Date(),
      });
      user = await users.findOne({ _id: result.insertedId });
    }

    const jwtToken = jwt.sign(
      {
        email: user.email,
        userId: user._id,
        name: user.name,
      },
      JWT_SECRET,
      { expiresIn: "7d" }
    );

    const response = NextResponse.json({
      ok: true,
      user: {
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
      },
    });

    response.cookies.set("token", jwtToken, {
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24 * 7,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    });

    return response;
  } catch (error) {
    console.error("Apple auth error:", error);
    return NextResponse.json(
      { error: "Error during Apple authentication" },
      { status: 500 }
    );
  }
} 