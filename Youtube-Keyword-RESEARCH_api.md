youtube keyword api

const url = 'https://keyword-research-for-youtube.p.rapidapi.com/yttags.php?keyword=Web%20Development';
const options = {
	method: 'GET',
	headers: {
		'x-rapidapi-key': '**************************************************',
		'x-rapidapi-host': 'keyword-research-for-youtube.p.rapidapi.com'
	}
};

try {
	const response = await fetch(url, options);
	const result = await response.text();
	console.log(result);
} catch (error) {
	console.error(error);
}



//change in keyword

Response:
{
  "exact_keyword": [
    {
      "keyword": "Web Development",
      "monthlysearch": 246917,
      "competition_score": 68,
      "difficulty": "High",
      "overallscore": 61
    }
  ],
  "related_keywords": [
    {
      "keyword": "software developer vs web developer",
      "monthlysearch": 3301,
      "competition_score": 55,
      "difficulty": "High",
      "overallscore": 49,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "artificial intelligence vs web development",
      "monthlysearch": 998,
      "competition_score": 51,
      "difficulty": "High",
      "overallscore": 47,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "front end",
      "monthlysearch": 36140,
      "competition_score": 51,
      "difficulty": "High",
      "overallscore": 60,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web development projects",
      "monthlysearch": 33097,
      "competition_score": 52,
      "difficulty": "High",
      "overallscore": 60,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learn css from scratch",
      "monthlysearch": 2346,
      "competition_score": 72,
      "difficulty": "High",
      "overallscore": 41,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "frontend developer",
      "monthlysearch": 26789,
      "competition_score": 57,
      "difficulty": "High",
      "overallscore": 57,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "developer roles",
      "monthlysearch": 1079,
      "competition_score": 56,
      "difficulty": "High",
      "overallscore": 45,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "front end web development",
      "monthlysearch": 2281,
      "competition_score": 69,
      "difficulty": "High",
      "overallscore": 42,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "software engineering",
      "monthlysearch": 285980,
      "competition_score": 68,
      "difficulty": "High",
      "overallscore": 62,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "beginner web development",
      "monthlysearch": 2503,
      "competition_score": 76,
      "difficulty": "High",
      "overallscore": 40,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "html",
      "monthlysearch": 817827,
      "competition_score": 75,
      "difficulty": "High",
      "overallscore": 63,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learn css",
      "monthlysearch": 29370,
      "competition_score": 68,
      "difficulty": "High",
      "overallscore": 53,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "write code",
      "monthlysearch": 2797,
      "competition_score": 69,
      "difficulty": "High",
      "overallscore": 43,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "the odin project",
      "monthlysearch": 39551,
      "competition_score": 15,
      "difficulty": "Low",
      "overallscore": 75,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "frontend",
      "monthlysearch": 47868,
      "competition_score": 52,
      "difficulty": "High",
      "overallscore": 61,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "backend web development",
      "monthlysearch": 144820,
      "competition_score": 56,
      "difficulty": "High",
      "overallscore": 64,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "programming",
      "monthlysearch": 130102,
      "competition_score": 86,
      "difficulty": "High",
      "overallscore": 51,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web development tutorial",
      "monthlysearch": 2060,
      "competition_score": 72,
      "difficulty": "High",
      "overallscore": 41,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web development roadmap",
      "monthlysearch": 55232,
      "competition_score": 56,
      "difficulty": "High",
      "overallscore": 60,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learning front end development",
      "monthlysearch": 3338,
      "competition_score": 75,
      "difficulty": "High",
      "overallscore": 42,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web developer",
      "monthlysearch": 18895,
      "competition_score": 63,
      "difficulty": "High",
      "overallscore": 53,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "cloud computing",
      "monthlysearch": 144546,
      "competition_score": 51,
      "difficulty": "High",
      "overallscore": 66,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "artificial intelligence in web development",
      "monthlysearch": 1600,
      "competition_score": 51,
      "difficulty": "High",
      "overallscore": 48,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "become a software developer",
      "monthlysearch": 2373,
      "competition_score": 89,
      "difficulty": "High",
      "overallscore": 35,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "full stack",
      "monthlysearch": 25214,
      "competition_score": 52,
      "difficulty": "High",
      "overallscore": 59,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "free web development resources",
      "monthlysearch": 2863,
      "competition_score": 50,
      "difficulty": "High",
      "overallscore": 51,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "backend",
      "monthlysearch": 80963,
      "competition_score": 89,
      "difficulty": "High",
      "overallscore": 48,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "coding",
      "monthlysearch": 607839,
      "competition_score": 87,
      "difficulty": "High",
      "overallscore": 57,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "software engineer",
      "monthlysearch": 101330,
      "competition_score": 80,
      "difficulty": "High",
      "overallscore": 53,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web design",
      "monthlysearch": 184110,
      "competition_score": 66,
      "difficulty": "High",
      "overallscore": 61,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "javascript",
      "monthlysearch": 1194175,
      "competition_score": 77,
      "difficulty": "High",
      "overallscore": 64,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "fullstack developer",
      "monthlysearch": 1411,
      "competition_score": 59,
      "difficulty": "High",
      "overallscore": 45,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "1 year of learning web development",
      "monthlysearch": 1225,
      "competition_score": 82,
      "difficulty": "High",
      "overallscore": 35,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web developer vs data science",
      "monthlysearch": 2867,
      "competition_score": 40,
      "difficulty": "Medium",
      "overallscore": 55,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "data analyst vs web developer",
      "monthlysearch": 2734,
      "competition_score": 47,
      "difficulty": "Medium",
      "overallscore": 52,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "reactjs for beginners",
      "monthlysearch": 2116,
      "competition_score": 68,
      "difficulty": "High",
      "overallscore": 43,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "backend developer",
      "monthlysearch": 33226,
      "competition_score": 53,
      "difficulty": "High",
      "overallscore": 59,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "back end",
      "monthlysearch": 12447,
      "competition_score": 93,
      "difficulty": "High",
      "overallscore": 40,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learn coding",
      "monthlysearch": 46676,
      "competition_score": 85,
      "difficulty": "High",
      "overallscore": 48,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "ai vs web development",
      "monthlysearch": 1774,
      "competition_score": 49,
      "difficulty": "Medium",
      "overallscore": 50,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "tech stack",
      "monthlysearch": 4957,
      "competition_score": 36,
      "difficulty": "Medium",
      "overallscore": 59,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "data science tutorial",
      "monthlysearch": 2951,
      "competition_score": 65,
      "difficulty": "High",
      "overallscore": 45,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "software development",
      "monthlysearch": 22305,
      "competition_score": 74,
      "difficulty": "High",
      "overallscore": 49,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "front-end expert",
      "monthlysearch": 823,
      "competition_score": 67,
      "difficulty": "High",
      "overallscore": 39,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "programming with mosh",
      "monthlysearch": 75830,
      "competition_score": 87,
      "difficulty": "High",
      "overallscore": 49,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "python",
      "monthlysearch": 2696403,
      "competition_score": 90,
      "difficulty": "High",
      "overallscore": 62,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "how to code",
      "monthlysearch": 63522,
      "competition_score": 86,
      "difficulty": "High",
      "overallscore": 48,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "front end developer",
      "monthlysearch": 44845,
      "competition_score": 52,
      "difficulty": "High",
      "overallscore": 61,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "build a portfolio website",
      "monthlysearch": 4737,
      "competition_score": 39,
      "difficulty": "Medium",
      "overallscore": 57,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "data scientist vs. web developer",
      "monthlysearch": 1835,
      "competition_score": 46,
      "difficulty": "Medium",
      "overallscore": 51,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "tailwind css",
      "monthlysearch": 326469,
      "competition_score": 43,
      "difficulty": "Medium",
      "overallscore": 72,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web",
      "monthlysearch": 43895,
      "competition_score": 95,
      "difficulty": "High",
      "overallscore": 44,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "embedded systems developer",
      "monthlysearch": 2089,
      "competition_score": 25,
      "difficulty": "Low",
      "overallscore": 60,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "data science vs web development",
      "monthlysearch": 2362,
      "competition_score": 43,
      "difficulty": "Medium",
      "overallscore": 53,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "full stack development",
      "monthlysearch": 26350,
      "competition_score": 52,
      "difficulty": "High",
      "overallscore": 59,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "artificial intelligence",
      "monthlysearch": 567539,
      "competition_score": 86,
      "difficulty": "High",
      "overallscore": 57,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "webdev",
      "monthlysearch": 9893,
      "competition_score": 74,
      "difficulty": "High",
      "overallscore": 46,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "css",
      "monthlysearch": 386152,
      "competition_score": 75,
      "difficulty": "High",
      "overallscore": 60,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "front-end developer",
      "monthlysearch": 2138,
      "competition_score": 54,
      "difficulty": "High",
      "overallscore": 48,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "full stack developer",
      "monthlysearch": 53776,
      "competition_score": 54,
      "difficulty": "High",
      "overallscore": 61,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web frameworks",
      "monthlysearch": 1647,
      "competition_score": 21,
      "difficulty": "Low",
      "overallscore": 60,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "developer ecosystem",
      "monthlysearch": 2827,
      "competition_score": 1,
      "difficulty": "Low",
      "overallscore": 71,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learning code",
      "monthlysearch": 1630,
      "competition_score": 83,
      "difficulty": "High",
      "overallscore": 36,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "data science vs software engineering",
      "monthlysearch": 2011,
      "competition_score": 30,
      "difficulty": "Medium",
      "overallscore": 58,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "developer",
      "monthlysearch": 32346,
      "competition_score": 84,
      "difficulty": "High",
      "overallscore": 47,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "developer salaries",
      "monthlysearch": 2294,
      "competition_score": 60,
      "difficulty": "High",
      "overallscore": 46,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "career opportunities",
      "monthlysearch": 32072,
      "competition_score": 92,
      "difficulty": "High",
      "overallscore": 43,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learn web development",
      "monthlysearch": 13758,
      "competition_score": 74,
      "difficulty": "High",
      "overallscore": 48,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "full stack roadmap",
      "monthlysearch": 5279,
      "competition_score": 50,
      "difficulty": "Medium",
      "overallscore": 54,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web developer vs software developer",
      "monthlysearch": 2542,
      "competition_score": 57,
      "difficulty": "High",
      "overallscore": 48,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learn html",
      "monthlysearch": 40660,
      "competition_score": 74,
      "difficulty": "High",
      "overallscore": 51,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "frontend development",
      "monthlysearch": 8275,
      "competition_score": 57,
      "difficulty": "High",
      "overallscore": 52,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "security engineer",
      "monthlysearch": 1813,
      "competition_score": 39,
      "difficulty": "Medium",
      "overallscore": 53,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "website design",
      "monthlysearch": 103743,
      "competition_score": 52,
      "difficulty": "High",
      "overallscore": 64,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "github co-pilot",
      "monthlysearch": 1074,
      "competition_score": 33,
      "difficulty": "Medium",
      "overallscore": 54,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "front-end",
      "monthlysearch": 0,
      "competition_score": 57,
      "difficulty": "High",
      "overallscore": 17,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "fullstack",
      "monthlysearch": 7750,
      "competition_score": 50,
      "difficulty": "High",
      "overallscore": 55,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "what is web development",
      "monthlysearch": 13321,
      "competition_score": 65,
      "difficulty": "High",
      "overallscore": 51,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "html css javascript",
      "monthlysearch": 44087,
      "competition_score": 70,
      "difficulty": "High",
      "overallscore": 54,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learn to code",
      "monthlysearch": 12661,
      "competition_score": 85,
      "difficulty": "High",
      "overallscore": 43,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "php",
      "monthlysearch": 280564,
      "competition_score": 52,
      "difficulty": "High",
      "overallscore": 68,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "mongodb react",
      "monthlysearch": 4080,
      "competition_score": 36,
      "difficulty": "Medium",
      "overallscore": 58,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "ui design",
      "monthlysearch": 79896,
      "competition_score": 68,
      "difficulty": "High",
      "overallscore": 57,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learn programming",
      "monthlysearch": 2030,
      "competition_score": 83,
      "difficulty": "High",
      "overallscore": 36,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web developer blog vs software developer",
      "monthlysearch": 3459,
      "competition_score": 68,
      "difficulty": "High",
      "overallscore": 45,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "front-end development",
      "monthlysearch": 1787,
      "competition_score": 50,
      "difficulty": "Medium",
      "overallscore": 49,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "data scientist vs app developer",
      "monthlysearch": 3196,
      "competition_score": 54,
      "difficulty": "High",
      "overallscore": 50,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "js",
      "monthlysearch": 160902,
      "competition_score": 92,
      "difficulty": "High",
      "overallscore": 50,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "code",
      "monthlysearch": 218960,
      "competition_score": 85,
      "difficulty": "High",
      "overallscore": 54,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "tech career",
      "monthlysearch": 1934,
      "competition_score": 32,
      "difficulty": "Medium",
      "overallscore": 56,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "web dev",
      "monthlysearch": 40877,
      "competition_score": 58,
      "difficulty": "High",
      "overallscore": 58,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "data science",
      "monthlysearch": 246228,
      "competition_score": 59,
      "difficulty": "High",
      "overallscore": 65,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "mobile app developer",
      "monthlysearch": 1381,
      "competition_score": 50,
      "difficulty": "Medium",
      "overallscore": 48,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "what is data science",
      "monthlysearch": 37061,
      "competition_score": 57,
      "difficulty": "High",
      "overallscore": 58,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "system developer",
      "monthlysearch": 2421,
      "competition_score": 68,
      "difficulty": "High",
      "overallscore": 43,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "data science for beginners",
      "monthlysearch": 2608,
      "competition_score": 66,
      "difficulty": "High",
      "overallscore": 44,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learn javascript from scratch",
      "monthlysearch": 2230,
      "competition_score": 74,
      "difficulty": "High",
      "overallscore": 41,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "software developer",
      "monthlysearch": 66752,
      "competition_score": 70,
      "difficulty": "High",
      "overallscore": 55,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "data science career",
      "monthlysearch": 2249,
      "competition_score": 42,
      "difficulty": "Medium",
      "overallscore": 53,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "developer hierarchy",
      "monthlysearch": 3234,
      "competition_score": 26,
      "difficulty": "Medium",
      "overallscore": 61,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "learn html from scratch",
      "monthlysearch": 1429,
      "competition_score": 79,
      "difficulty": "High",
      "overallscore": 37,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "developer responsibilities",
      "monthlysearch": 2975,
      "competition_score": 34,
      "difficulty": "Medium",
      "overallscore": 58,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "developer skills",
      "monthlysearch": 3185,
      "competition_score": 26,
      "difficulty": "Medium",
      "overallscore": 61,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "react",
      "monthlysearch": 1759337,
      "competition_score": 94,
      "difficulty": "High",
      "overallscore": 58,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "html css",
      "monthlysearch": 215859,
      "competition_score": 72,
      "difficulty": "High",
      "overallscore": 59,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "how to become a front-end developer",
      "monthlysearch": 1558,
      "competition_score": 54,
      "difficulty": "High",
      "overallscore": 47,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "coding life",
      "monthlysearch": 2079,
      "competition_score": 90,
      "difficulty": "High",
      "overallscore": 34,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "how to learn coding",
      "monthlysearch": 55133,
      "competition_score": 83,
      "difficulty": "High",
      "overallscore": 49,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "responsive design",
      "monthlysearch": 2796,
      "competition_score": 33,
      "difficulty": "Medium",
      "overallscore": 58,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "code with mosh",
      "monthlysearch": 32698,
      "competition_score": 87,
      "difficulty": "High",
      "overallscore": 46,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "introduction to data science",
      "monthlysearch": 3622,
      "competition_score": 55,
      "difficulty": "High",
      "overallscore": 50,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "front end development",
      "monthlysearch": 13111,
      "competition_score": 48,
      "difficulty": "Medium",
      "overallscore": 58,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "full-stack developer",
      "monthlysearch": 1825,
      "competition_score": 54,
      "difficulty": "High",
      "overallscore": 48,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "how to be a web developer",
      "monthlysearch": 1271,
      "competition_score": 62,
      "difficulty": "High",
      "overallscore": 43,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "frontend vs backend",
      "monthlysearch": 8829,
      "competition_score": 36,
      "difficulty": "Medium",
      "overallscore": 61,
      "last_update": "2025-07-14 19:47:47"
    },
    {
      "keyword": "mosh hamedani",
      "monthlysearch": 33958,
      "competition_score": 87,
      "difficulty": "High",
      "overallscore": 46,
      "last_update": "2025-07-14 19:47:47"
    }
  ]
}