"use client";
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  DashboardLayout,
  Header,
  Sidebar,
} from '../components';
import { useChatHistory } from '../hooks/useChatHistory';
import { Inspirationicon, IdeaLabicon, SocialPosticon, Clippingicon, Storyboardicon, Youtubeicon, Keywordicon, Accounticon } from '../components/icons/icons';
import {
  VideoAnalyzerIcon,
  TitleGeneratorIcon,
  DescriptionGeneratorIcon,
  KeywordInsightsIcon,
  HashtagInsightsIcon,
  ThumbnailDesignerIcon,
} from '../components/icons/YoutubeIcons';

import ToolSwitcher from '../components/youtube-tools/ToolSwitcher';
import InputCard from '../components/youtube-tools/InputCard';
import LoadingIndicator from '../components/youtube-tools/LoadingIndicator';
import ErrorDisplay from '../components/youtube-tools/ErrorDisplay';
import TitlesSection from '../components/youtube-tools/TitlesSection';
import DescriptionSection from '../components/youtube-tools/DescriptionSection';
import KeywordsSection from '../components/youtube-tools/KeywordsSection';
import HashtagsSection from '../components/youtube-tools/HashtagsSection';
import ThumbnailDesigner from '../components/youtube-tools/ThumbnailDesigner';

const sidebarMenu = [
  { label: "Inspiration", icon: <Inspirationicon />, active: false },
  { label: "Idea Lab", icon: <IdeaLabicon />, active: false },
  { label: "Social Post", icon: <SocialPosticon />, active: false },
  { label: "Clipping", icon: <Clippingicon />, active: false },
  { label: "Storyboard Editor", icon: <Storyboardicon />, active: false },
  { label: "YouTube Tools", icon: <Youtubeicon />, active: true },
  { label: "Keyword Insights", icon: <Keywordicon />, active: false },
  { label: "Account", icon: <Accounticon />, active: false },
];

const tools = [
  { name: 'Video Analyzer', icon: <VideoAnalyzerIcon /> },
  { name: 'Title Generator', icon: <TitleGeneratorIcon /> },
  { name: 'Description Generator', icon: <DescriptionGeneratorIcon /> },
  { name: 'Keyword Insights', icon: <KeywordInsightsIcon /> },
  { name: 'Hashtag Insights', icon: <HashtagInsightsIcon /> },
  { name: 'Thumbnail Designer', icon: <ThumbnailDesignerIcon /> },
];

const styles = [
    { id: 'one',   label: 'Structure One',   badge: null, image: '/Youtube-Tools/thumbnails/Thumbnail Design Structure One.png' },
    { id: 'two',   label: 'Structure Two',   badge: null, image: '/Youtube-Tools/thumbnails/Thumbnail Design Structure Two.png' },
    { id: 'three', label: 'Structure Three', badge: null, image: '/Youtube-Tools/thumbnails/Thumbnail Design Structure Three.png' },
    { id: 'four',  label: 'Structure Four',  badge: null, image: '/Youtube-Tools/thumbnails/Thumbnail Design Structure Four.png' },
    { id: 'none',  label: 'Structure None',  badge: null, image: '/Youtube-Tools/thumbnails/Thumbnail Design Structure None.png' },
  ];

  const styles_to_be_shown = [
    { id: 'one',   label: 'Structure One',   badge: null, image: '/Youtube-Tools/Thubnailtobeshown/Thumbnail Design Structure One.png' },
    { id: 'two',   label: 'Structure Two',   badge: null, image: '/Youtube-Tools/Thubnailtobeshown/Thumbnail Design Structure Two.png' },
    { id: 'three', label: 'Structure Three', badge: null, image: '/Youtube-Tools/Thubnailtobeshown/Thumbnail Design Structure Three.png' },
    { id: 'four',  label: 'Structure Four',  badge: null, image: '/Youtube-Tools/Thubnailtobeshown/Thumbnail Design Structure Four.png' },
    { id: 'none',  label: 'Structure None',  badge: null, image: '/Youtube-Tools/Thubnailtobeshown/Thumbnail Design Structure None.png' },
  ];

export default function YouTubeTools() {
  const router = useRouter();
  const {
    historySections,
    isLoading,
    clearAllHistory
  } = useChatHistory();

  const [activeTool, setActiveTool] = useState('Video Analyzer');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [inputValue, setInputValue] = useState('https://www.youtube.com/...');
  const [imageIdea, setImageIdea] = useState('');
  const [generatedTitles, setGeneratedTitles] = useState<string[]>([]);
  const [generatedDescription, setGeneratedDescription] = useState('');
  const [generatedKeywords, setGeneratedKeywords] = useState<any>({});
  const [generatedHashtags, setGeneratedHashtags] = useState<string[]>([]);
  const [gptThumbUrl, setGptThumbUrl] = useState<string | null>(null);
  const [isGeneratingGptThumb, setIsGeneratingGptThumb] = useState(false);
  const [gptThumbError, setGptThumbError] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [promptPreview, setPromptPreview] = useState('');
  
  const [thumbTitle, setThumbTitle] = useState('');
  const [styleIndex, setStyleIndex] = useState(0);
  const [mainImage, setMainImage] = useState<File | null>(null);
  const [referenceImage, setReferenceImage] = useState<File | null>(null);
  const [backgroundAssets, setBackgroundAssets] = useState<string>('');

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (e) {
      console.error('Copy failed', e);
    }
  };

  const shareContent = async (title: string, text: string) => {
    const nav: any = navigator as any;
    if (nav && typeof nav.share === 'function') {
      try {
        await nav.share({ title, text });
      } catch {
        // ignore cancel
      }
    } else {
      await copyToClipboard(text);
    }
  };

  const regenerateSection = async (type: 'titles' | 'description' | 'keywords' | 'hashtags' | 'all') => {
    setIsAnalyzing(true);
    setError(null);
    try {
      let textForProcessing = "";

      if (isYoutubeUrl(inputValue)) {
        const videoId = new URL(inputValue).searchParams.get('v');
        if (videoId) {
          const transcriptRes = await fetch('/api/youtube-transcript', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ videoId }),
          });

          if (!transcriptRes.ok) {
            throw new Error('Failed to fetch transcript from the video.');
          }

          const { transcript } = await transcriptRes.json();
          if (!transcript) {
            throw new Error('Transcript is not available for this video.');
          }
          textForProcessing = transcript;
        } else {
          throw new Error('Invalid YouTube URL: Video ID not found.');
        }
      } else {
        textForProcessing = inputValue;
      }

      if (!textForProcessing.trim()) {
        throw new Error('The text to be processed is empty.');
      }

      const generate = async (t: string) => {
        const res = await fetch('/api/generate-youtube-content', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text: textForProcessing, type: t }),
        });
        if (!res.ok) throw new Error(`Failed to generate ${t}`);
        return res.json();
      };

      if (type === 'titles' || type === 'all') {
        const { content: titles } = await generate('titles');
        setGeneratedTitles(titles.split('\n').filter((t: string) => t.trim() !== ''));
      }
      if (type === 'description' || type === 'all') {
        const { content: description } = await generate('description');
        setGeneratedDescription(description);
      }
      if (type === 'keywords' || type === 'all') {
        const { content: keywordsJson } = await generate('keywords');
        const cleanedJson = keywordsJson.replace(/```json\n|```/g, '');
        const keywords = JSON.parse(cleanedJson);
        setGeneratedKeywords(keywords);
      }
      if (type === 'hashtags' || type === 'all') {
        const { content: hashtags } = await generate('hashtags');
        setGeneratedHashtags(hashtags.split(' ').filter((h: string) => h.startsWith('#')));
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const isYoutubeUrl = (url: string) => {
    const regex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$/;
    return regex.test(url);
  };

  const handleAnalyze = async () => {
    setIsAnalyzing(true);
    setError(null);
    setGeneratedTitles([]);
    setGeneratedDescription('');
    setGeneratedKeywords({});
    setGeneratedHashtags([]);

    try {
      let textForProcessing = "";

      if (isYoutubeUrl(inputValue)) {
        const videoId = new URL(inputValue).searchParams.get('v');
        if (videoId) {
          const transcriptRes = await fetch('/api/youtube-transcript', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ videoId }),
          });

          if (!transcriptRes.ok) {
            throw new Error('Failed to fetch transcript from the video.');
          }

          const { transcript } = await transcriptRes.json();
          if (!transcript) {
            throw new Error('Transcript is not available for this video.');
          }
          textForProcessing = transcript;
        } else {
          throw new Error('Invalid YouTube URL: Video ID not found.');
        }
      } else {
        textForProcessing = inputValue;
      }

      if (!textForProcessing.trim()) {
        throw new Error('The text to be processed is empty.');
      }

      const generate = async (type: string, body: any) => {
        const res = await fetch('/api/generate-youtube-content', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(body),
        });
        if (!res.ok) throw new Error(`Failed to generate ${type}`);
        return res.json();
      };

      if (activeTool === 'Title Generator' || activeTool === 'Video Analyzer') {
        const { content: titles } = await generate('titles', { text: textForProcessing, type: 'titles' });
        setGeneratedTitles(titles.split('\n').filter((t: string) => t.trim() !== ''));
      }

      if (activeTool === 'Description Generator' || activeTool === 'Video Analyzer') {
        const { content: description } = await generate('description', { text: textForProcessing, type: 'description' });
        setGeneratedDescription(description);
      }

      if (activeTool === 'Keyword Insights' || activeTool === 'Video Analyzer') {
        const { content: keywordsJson } = await generate('keywords', { text: textForProcessing, type: 'keywords' });
        const cleanedJson = keywordsJson.replace(/```json\n|```/g, '');
        const keywords = JSON.parse(cleanedJson);
        setGeneratedKeywords(keywords);
      }

      if (activeTool === 'Hashtag Insights' || activeTool === 'Video Analyzer') {
        const { content: hashtags } = await generate('hashtags', { text: textForProcessing, type: 'hashtags' });
        setGeneratedHashtags(hashtags.split(' ').filter((h: string) => h.startsWith('#')));
      }

      if (activeTool === 'Thumbnail Designer' || activeTool === 'Video Analyzer' || activeTool === 'Hashtag Insights') {
        handleGenerateThumbnail();
      }

    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleGenerateThumbnail = async () => {
    try {
      setIsGeneratingGptThumb(true);
      setGptThumbError(null);
      setGptThumbUrl(null);

      let textForProcessing = '';
      if (isYoutubeUrl(inputValue)) {
        const videoId = new URL(inputValue).searchParams.get('v');
        if (videoId) {
          const transcriptRes = await fetch('/api/youtube-transcript', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ videoId }),
          });
          if (transcriptRes.ok) {
            const { transcript } = await transcriptRes.json();
            textForProcessing = transcript || '';
          }
        }
      } else {
        textForProcessing = inputValue;
      }

      const thumbnailPrompt =
        `Create a compelling YouTube thumbnail image, landscape, vibrant, high-contrast composition with bold focal elements and room for text. Theme is based on this video transcription/context:\n\n${textForProcessing}\n\n` +
        `Design cues: cinematic lighting, crisp details, modern style, no watermarks, no logos, safe for work, have paddings on the side. Make sure that the text is not touching the sides of the image.`;

      const imgRes = await fetch('/api/gpt-image-1', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: thumbnailPrompt,
          aspect_ratio: '3:2',
          output_format: 'png',
          number_of_images: 1,
          resize: true
        }),
      });

      if (!imgRes.ok) {
        const errJson = await imgRes.json().catch(() => ({}));
        throw new Error(errJson?.error || `Failed to generate thumbnail`);
      }
      const data = await imgRes.json();
      const first = Array.isArray(data?.images) ? data.images[0] : null;
      if (!first) throw new Error('No thumbnail image returned');
      setGptThumbUrl(first);
    } catch (e: any) {
      setGptThumbError(e?.message || 'Thumbnail generation failed');
    } finally {
      setIsGeneratingGptThumb(false);
    }
  };
  
  // Import prompts from JSON
  const prompts = require('./prompts/prompts.json');

  const handleThumbnailDesignerGenerate = async () => {
    try {
      setIsGeneratingGptThumb(true);
      setGptThumbError(null);
      setGptThumbUrl(null);

      // Determine context: transcript if URL else free text
      let transcriptText = '';
      let imageIdea = '';
      if (!isYoutubeUrl(inputValue)) {
          imageIdea = inputValue?.trim() || '';
      }
      try {
        if (isYoutubeUrl(inputValue)) {
          const vid = new URL(inputValue).searchParams.get('v');
          if (vid) {
            const tr = await fetch('/api/youtube-transcript', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ videoId: vid }),
            });
            if (tr.ok) {
              const j = await tr.json();
              transcriptText = (j?.transcript || '').trim();

              // If transcript too large, summarize with LLM before embedding
              // Always attempt to summarize if transcriptText is available
              if (transcriptText) {
                try {
                  const summarizePrompt = `${prompts.summarizePrompt} ${transcriptText}`;
                  const sumRes = await fetch('/api/generate-youtube-content', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: summarizePrompt + ' ' + transcriptText, type: 'description' }),
                  });
                  const su = await sumRes.text();
                  if (sumRes.ok) {
                      if (su && su.trim().length > 0) {
                      imageIdea = su; // Only assign imageIdea if summarization is successful
                      console.log('Summarized imageIdea:', imageIdea);
                    } else {
                      console.log('Summarization returned empty for transcript.');
                    }
                  } else {
                    console.log('Summarization API call failed.');
                  }
                } catch (e) {
                  console.error('Error during summarization:', e);
                }
              } // Added missing closing brace for 'if (transcriptText)'
            }
          }
        }
      } catch (e) {
        console.warn('[ThumbnailDesigner] transcript fetch skipped', e);
      }

      // Resolve selected style
      const selected = styles[styleIndex];
      const selectedLabel = selected?.label || 'Structure One';

      // Convert uploads to data URLs
      const fileToDataUrl = (f: File | null) =>
        new Promise<string | null>((resolve) => {
          if (!f) return resolve(null);
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = () => resolve(null);
          reader.readAsDataURL(f);
        });

      const [mainDataUrl, referenceDataUrl] = await Promise.all([
        fileToDataUrl(mainImage),
        fileToDataUrl(referenceImage),
      ]);

      // Fetch style preview as data URL
      const fetchImageAsDataUrl = async (url: string | null) => {
        try {
          if (!url) return null;
          const absolute = url.startsWith('http')
            ? url
            : `${window.location.origin}${url}`;
          const res = await fetch(absolute, { cache: 'no-cache' });
          if (!res.ok) throw new Error(`fetch failed: ${res.status}`);
          const blob = await res.blob();
          return await new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.readAsDataURL(blob);
          });
        } catch (e) {
          console.error('[ThumbnailDesigner] style fetch->dataURL failed', url, e);
          return null;
        }
      };
      const styleDataUrl = await fetchImageAsDataUrl(selected?.image || null);

      const MAX_CONTEXT_CHARS = 12000;
      let scenario: '2a' | '2b' | '2c' | '2d' | '3' = '2a';
      if (referenceDataUrl) {
        scenario = '3';
      } else {
        if (selectedLabel.includes('Two')) scenario = '2b';
        else if (selectedLabel.includes('Three')) scenario = '2c';
        else if (selectedLabel.includes('Four')) scenario = '2d';
        else if (selectedLabel.includes('None')) scenario = '3';
        else scenario = '2a';
      }

      const contextSourceRaw = imageIdea;
      const contextSource = contextSourceRaw?.length > 12000 ? contextSourceRaw.slice(0, 12000) : contextSourceRaw;
      
      // Select the appropriate scenario template from JSON
      let templateKey = '2a';
      if (scenario === '2a') templateKey = '2a';
      else if (scenario === '2b') templateKey = '2b';
      else if (scenario === '2c') templateKey = '2c';
      else if (scenario === '2d') templateKey = '2d';
      else if (scenario === '3') templateKey = '3';

      const template = prompts.scenarios[templateKey].prompt;

      // Replace placeholders with actual values
      const thumbnailPrompt = template
        .replace(/{{ Insert Thumbnail Text Title }}/g, thumbTitle.trim() || '')
        .replace(/{{ Insert Image Idea & Visual Description }}/g, contextSource || '')
        .replace(/{{ Insert Uploaded “Main Image” }}/g, mainDataUrl ? 'Main Image Provided' : 'No Main Image')
        .replace(/{{ Insert Uploaded “Reference Thumbnail” }}/g, referenceDataUrl ? 'Reference Image Provided' : 'No Reference Image')
        .replace(/{{ Insert Thumbnail Background Assets }}/g, backgroundAssets.trim() || 'Background Assets Not Provided')
        .replace(/{{ Insert Uploaded “Thumbnail Design Structure One” }}/g, 'Design Structure One')
        .replace(/{{ Insert Uploaded “Thumbnail Design Structure Two” }}/g, 'Design Structure Two')
        .replace(/{{ Insert Uploaded “Thumbnail Design Structure Three” }}/g, 'Design Structure Three')
        .replace(/{{ Insert Uploaded “Thumbnail Design Structure Four” }}/g, 'Design Structure Four')
        .replace(/{{ Insert Uploaded “Thumbnail Design Structure None” }}/g, 'Design Structure None')
        .replace(/{{ Insert Image Idea & Visual Description }}/g, contextSource)
        .replace(/{{ Insert Image Idea }}/g, contextSource.trim().split('\n')[0]);

      setPromptPreview(thumbnailPrompt);

      const input_images = [
        styleDataUrl,
        referenceDataUrl,
        mainDataUrl,
      ].filter(Boolean) as string[];

      const resp = await fetch('/api/gpt-image-designer', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: thumbnailPrompt,
          aspect_ratio: '16:9',
          output_format: 'png',
          number_of_images: 1,
          input_images,
          input_fidelity: 'high',
          quality: 'high',
          moderation: 'auto',
          user_id: null
        }),
      });

      if (!resp.ok) {
        const errJson = await resp.json().catch(() => ({} as any));
        throw new Error(errJson?.error || 'Failed to generate GPT-Image-1 thumbnail (designer)');
      }
      const data = await resp.json();
      const first = Array.isArray(data?.images) ? data.images[0] : null;
      if (!first) throw new Error('No thumbnail image returned');
      setGptThumbUrl(first);
    } catch (e: any) {
      setGptThumbError(e?.message || 'Thumbnail generation failed');
    } finally {
      setIsGeneratingGptThumb(false);
    }
  };


  const handleMenuItemClick = (item: any, index: number) => {
    const routes: { [key: string]: string } = {
      "Inspiration": "/dashboard",
      "Idea Lab": "/idealab",
      "Social Post": "/social-post",
      "Clipping": "/clipping",
      "Storyboard Editor": "/storyboard",
      "YouTube Tools": "/youtube-tools",
      "Keyword Insights": "/keyword-insights",
      "Account": "/account"
    };

    const route = routes[item.label];
    if (route) {
      router.push(route);
    }
  };

  const handleHistoryItemClick = (historyItem: any, sectionIndex: number, itemIndex: number) => {
    if (historyItem.sessionId) {
      router.push('/idealab');
    }
  };

  const handleClearHistory = () => {
    clearAllHistory();
  };

  const handleClearBackgroundAssets = () => {
    setBackgroundAssets('');
  };

  return (
    <DashboardLayout
      header={<Header />}
      sidebar={
        <Sidebar
          menuItems={sidebarMenu}
          histories={historySections}
          isLoading={isLoading}
          onMenuItemClick={handleMenuItemClick}
          onHistoryItemClick={handleHistoryItemClick}
          onClearHistory={handleClearHistory}
        />
      }
    >
      <div className="min-h-full w-full text-white bg-[#030F0F]">
        <div className="mx-auto max-w-6xl px-6 py-8">
          <ToolSwitcher tools={tools} activeTool={activeTool} setActiveTool={setActiveTool} />
          <InputCard
            activeTool={activeTool}
            inputValue={inputValue}
            setInputValue={setInputValue}
            isAnalyzing={isAnalyzing}
            isGeneratingGptThumb={isGeneratingGptThumb}
            handleAnalyze={handleAnalyze}
            onThumbnailGenerate={activeTool === 'Thumbnail Designer' ? handleThumbnailDesignerGenerate : handleGenerateThumbnail}
          />

          {isAnalyzing && activeTool !== 'Thumbnail Designer' && <LoadingIndicator />}
          {error && activeTool !== 'Thumbnail Designer' && <ErrorDisplay error={error} />}

          {activeTool !== 'Thumbnail Designer' && (
            <>
              <TitlesSection
                titles={generatedTitles}
                copyToClipboard={copyToClipboard}
                shareContent={shareContent}
                regenerateSection={regenerateSection}
              />
              <DescriptionSection
                description={generatedDescription}
                copyToClipboard={copyToClipboard}
                shareContent={shareContent}
                regenerateSection={regenerateSection}
              />
              <KeywordsSection
                keywords={generatedKeywords}
                copyToClipboard={copyToClipboard}
                shareContent={shareContent}
                regenerateSection={regenerateSection}
              />
              <HashtagsSection
                hashtags={generatedHashtags}
                copyToClipboard={copyToClipboard}
                shareContent={shareContent}
                regenerateSection={regenerateSection}
                gptThumbUrl={gptThumbUrl}
                isGeneratingGptThumb={isGeneratingGptThumb}
                gptThumbError={gptThumbError}
                onGenerateThumbnail={handleGenerateThumbnail}
              />
            </>
          )}

          {activeTool === 'Thumbnail Designer' && (
            <ThumbnailDesigner
              generatedTitles={generatedTitles}
              gptThumbUrl={gptThumbUrl}
              isGeneratingGptThumb={isGeneratingGptThumb}
              gptThumbError={gptThumbError}
              onGenerate={() => {}}
              setThumbTitle={setThumbTitle}
              thumbTitle={thumbTitle}
              styles={styles_to_be_shown}
              styleIndex={styleIndex}
              setStyleIndex={setStyleIndex}
              mainImage={mainImage}
              setMainImage={setMainImage}
              referenceImage={referenceImage}
              setReferenceImage={setReferenceImage}
              backgroundAssets={backgroundAssets}
              setBackgroundAssets={setBackgroundAssets}
              onClearBackgroundAssets={handleClearBackgroundAssets}
              promptPreview={promptPreview}
              setPromptPreview={setPromptPreview}
            />
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
