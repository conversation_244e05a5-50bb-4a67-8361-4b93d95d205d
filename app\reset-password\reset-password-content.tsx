"use client";
import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export default function ResetPasswordContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const [password, setPassword] = useState("");
  const [confirm, setConfirm] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    if (!password || !confirm) {
      setError("Please fill in all fields");
      return;
    }
    if (password !== confirm) {
      setError("Passwords do not match");
      return;
    }
    setLoading(true);
    const res = await fetch("/api/auth/reset-password", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ token, password }),
    });
    setLoading(false);
    const data = await res.json();
    if (res.ok) {
      setSuccess("Password reset successful! Redirecting to login...");
      setTimeout(() => router.push("/login"), 2000);
    } else {
      setError(data.error || "Something went wrong");
    }
  };

  return (
    <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
      <input
        type="password"
        className="w-full px-4 py-3 rounded-xl bg-[#18182a] border border-[#35355a] text-white placeholder-[#b3b3c6] focus:outline-none focus:ring-2 focus:ring-[#7c5cff] transition"
        placeholder="New password"
        value={password}
        onChange={e => setPassword(e.target.value)}
        required
      />
      <input
        type="password"
        className="w-full px-4 py-3 rounded-xl bg-[#18182a] border border-[#35355a] text-white placeholder-[#b3b3c6] focus:outline-none focus:ring-2 focus:ring-[#7c5cff] transition"
        placeholder="Confirm new password"
        value={confirm}
        onChange={e => setConfirm(e.target.value)}
        required
      />
      {error && <div className="text-red-400 text-sm text-center">{error}</div>}
      {success && <div className="text-green-400 text-sm text-center">{success}</div>}
      <button
        type="submit"
        className="w-full mt-2 py-3 rounded-xl bg-[#7c5cff] hover:bg-[#6a4eea] transition text-white font-semibold text-lg shadow-md disabled:opacity-60"
        disabled={loading}
      >
        {loading ? "Please wait..." : "Reset Password"}
      </button>
    </form>
  );
} 