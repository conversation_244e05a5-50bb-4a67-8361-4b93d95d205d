/**
 * This route has been intentionally disabled in favor of NextAuth's built-in Google OAuth flow.
 * Use signIn("google") from next-auth/react on the client. The flow will go through:
 *   /api/auth/signin/google  ->  /api/auth/callback/google
 * which will set the NextAuth session cookie read by middleware/getToken.
 *
 * If you need custom logic, consider NextAuth callbacks in:
 *   app/api/auth/[...nextauth]/auth.config.ts
 */

import { NextResponse } from "next/server";

export async function GET() {
  return NextResponse.json(
    { error: "Disabled. Use NextAuth signIn('google') instead." },
    { status: 410 }
  );
}

export async function POST() {
  return NextResponse.json(
    { error: "Disabled. Use NextAuth signIn('google') instead." },
    { status: 410 }
  );
}