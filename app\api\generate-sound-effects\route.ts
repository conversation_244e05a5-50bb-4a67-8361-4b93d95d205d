import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';
import cloudinary from '../../../lib/cloudinary';

// Helper function to upload audio to Cloudinary
async function uploadToCloudinary(dataUrl: string): Promise<string> {
  try {
    const result = await cloudinary.uploader.upload(dataUrl, {
      resource_type: 'video', // Audio is treated as video in Cloudinary
      folder: 'ai-generated-sound-effects', // Optional: organize in a folder
    });
    return result.secure_url;
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw new Error('Failed to upload audio to Cloudinary');
  }
}

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function POST(request: NextRequest) {
  try {
    const { prompt, model = 'tango2', steps = 100, guidance = 3 } = await request.json();

    if (!prompt) {
      return NextResponse.json({ 
        success: false, 
        error: 'Prompt is required' 
      }, { status: 400 });
    }

    if (!process.env.REPLICATE_API_TOKEN) {
      return NextResponse.json({ 
        success: false, 
        error: 'Replicate API token not configured' 
      }, { status: 500 });
    }

    console.log('Generating sound effect with:', { prompt, model, steps, guidance });

    // Call Replicate API for sound effects generation using Tango model
    const output = await replicate.run(
      "declare-lab/tango:740e4f5e59bd3b871c9e5b4efbff7ded516d40aa6abf4e95fd5e8dd149b7bc3f",
      {
        input: {
          prompt: prompt,
          model: model,
          steps: steps,
          guidance: guidance
        }
      }
    );

    console.log('Replicate sound effects response:', output);
    console.log('Output type:', typeof output);

    let audioUrl: string | null = null;

    // Handle different output formats from Replicate
    if (typeof output === 'string') {
      // Direct URL string
      audioUrl = output;
    } else if (output && typeof output === 'object' && Symbol.asyncIterator in output) {
      // Handle ReadableStream (binary data)
      console.log('Output is a ReadableStream, collecting binary data...');
      const chunks: Uint8Array[] = [];

      try {
        for await (const chunk of output as AsyncIterable<Uint8Array>) {
          if (chunk instanceof Uint8Array) {
            chunks.push(chunk);
          }
        }

        console.log(`Collected ${chunks.length} chunks of binary data`);

        if (chunks.length > 0) {
          // Combine all chunks into a single buffer
          const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
          const combinedBuffer = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of chunks) {
            combinedBuffer.set(chunk, offset);
            offset += chunk.length;
          }

          // Convert to base64 data URL for audio
          const base64String = Buffer.from(combinedBuffer).toString('base64');
          const dataUrl = `data:audio/wav;base64,${base64String}`;
          const cloudinaryUrl = await uploadToCloudinary(dataUrl);
          audioUrl = cloudinaryUrl;

          console.log('Successfully created data URL from binary data');
        }

      } catch (e) {
        console.error('Error reading ReadableStream:', e);
        return NextResponse.json({
          success: false,
          error: "Failed to process audio stream",
          details: "Error reading binary audio data from the model"
        }, { status: 500 });
      }
    } else if (output && typeof output === 'object') {
      // Handle object response (try to extract URL)
      const obj = output as any;
      if (obj.url) {
        audioUrl = obj.url;
      } else if (obj.audio) {
        audioUrl = obj.audio;
      } else if (obj.output) {
        audioUrl = obj.output;
      } else {
        console.error('Unexpected object format:', obj);
        return NextResponse.json({
          success: false,
          error: "Unexpected output format",
          details: "The model returned an unexpected object format"
        }, { status: 500 });
      }
    } else {
      console.error('Unexpected output format. Got:', typeof output, output);
      return NextResponse.json({
        success: false,
        error: "Unexpected output format",
        details: "The model returned an unexpected output format"
      }, { status: 500 });
    }

    console.log('Processed audio URL/data:', audioUrl ? 'Generated successfully' : 'Failed');

    // Validate that we have valid audio data
    if (!audioUrl) {
      console.error('No valid audio data found in output:', output);
      return NextResponse.json({
        success: false,
        error: "No audio generated",
        details: "The model did not return valid audio data"
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      audioUrl: audioUrl,
      model: model,
      steps: steps,
      guidance: guidance
    });

  } catch (error: any) {
    console.error('Sound effects generation error:', error);

    // Handle specific Replicate errors
    if (error.message?.includes('authentication')) {
      return NextResponse.json({
        success: false,
        error: "Authentication failed. Please check API token."
      }, { status: 401 });
    }

    if (error.message?.includes('rate limit')) {
      return NextResponse.json({
        success: false,
        error: "Rate limit exceeded. Please try again later."
      }, { status: 429 });
    }

    return NextResponse.json({
      success: false,
      error: "Failed to generate sound effect",
      details: error.message || "Unknown error occurred"
    }, { status: 500 });
  }
}
