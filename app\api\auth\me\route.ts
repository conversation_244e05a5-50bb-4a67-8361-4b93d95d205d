import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET!;

export async function GET(req: NextRequest) {
  const token = req.cookies.get("token")?.value;
  if (!token) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }
  try {
    const payload = jwt.verify(token, JWT_SECRET) as { email: string };
    return NextResponse.json({ email: payload.email });
  } catch {
    return NextResponse.json({ error: "Invalid token" }, { status: 401 });
  }
} 