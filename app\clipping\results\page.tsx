"use client";
import Image from "next/image";
import Link from "next/link";
import { Suspense, useEffect, useMemo, useRef, useState } from "react";
import { useSearchParams } from "next/navigation";
import { DashboardLayout, Header, Sidebar } from "../../components";
import {
  Accounticon,
  Clippingicon,
  GeneratebtnIcon,
  GeneratingLoadingIcon,
  IdeaLabicon,
  Inspirationicon,
  Keywordicon,
  SocialPosticon,
  Storyboardicon,
  Youtubeicon,
} from "../../components/icons/icons";
import { AnalyzingIcon } from "../../components/icons/YoutubeIcons";
import { useChatHistory } from "../../hooks/useChatHistory";

type UiClip = {
  id: string;
  title: string;
  durationSeconds: number;
  thumbnailUrl?: string;
  downloadUrl?: string;
  transcript?: string;
  viralScore?: string;
};

const sidebarMenu = [
  { label: "Inspiration", icon: <Inspirationicon />, active: false },
  { label: "Idea Lab", icon: <IdeaLabicon />, active: false },
  { label: "Social Post", icon: <SocialPosticon />, active: false },
  { label: "Clipping", icon: <Clippingicon />, active: true },
  { label: "Storyboard Editor", icon: <Storyboardicon />, active: false },
  { label: "YouTube Tools", icon: <Youtubeicon />, active: false },
  { label: "Keyword Insights", icon: <Keywordicon />, active: false },
  { label: "Account", icon: <Accounticon />, active: false },
];

function formatDuration(seconds: number) {
  if (!seconds || seconds <= 0) return "00:00";
  const m = Math.floor(seconds / 60)
    .toString()
    .padStart(2, "0");
  const s = Math.floor(seconds % 60)
    .toString()
    .padStart(2, "0");
  return `${m}:${s}`;
}

export default function ResultsPage() {
  const { historySections, isLoading, clearAllHistory } = useChatHistory();

  const handleMenuItemClick = (item: any) => {};
  const handleHistoryItemClick = () => {};
  const handleClearHistory = () => clearAllHistory();

  return (
    <DashboardLayout
      header={<Header />}
      sidebar={
        <Sidebar
          menuItems={sidebarMenu}
          histories={historySections}
          isLoading={isLoading}
          onMenuItemClick={handleMenuItemClick}
          onHistoryItemClick={handleHistoryItemClick}
          onClearHistory={handleClearHistory}
        />
      }
      className="min-h-screen flex flex-col bg-[#0a0f0d] text-white"
    >
      <Suspense fallback={
        <div className="flex-1 w-full px-0 pt-10" style={{ fontFamily: "inherit" }}>
          <div className="w-full max-w-[1100px] mx-auto px-4">
            <div className="mb-8">
              <h1 className="text-[20px] font-medium mb-1 tracking-[0.01em]" style={{ color: "#a78bfa" }}>
                Loading results...
              </h1>
              <p className="text-[14px] text-white/70">Fetching project details</p>
            </div>
          </div>
        </div>
      }>
        <ResultsInner />
      </Suspense>
    </DashboardLayout>
  );
}

// Inner component that uses useSearchParams (wrapped by Suspense)
function ResultsInner() {
  const { historySections, isLoading, clearAllHistory } = useChatHistory();

  const handleMenuItemClick = (item: any) => {};
  const handleHistoryItemClick = () => {};
  const handleClearHistory = () => clearAllHistory();

  const search = useSearchParams();
  const projectId = search.get("projectId");

  const [status, setStatus] = useState<string | undefined>("processing");
  const [clips, setClips] = useState<UiClip[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [polling, setPolling] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0); // New state for progress
  const pollAbort = useRef<AbortController | null>(null);
  const pollAttemptRef = useRef(0); // To track attempts for progress calculation

  const headerTitle = useMemo(() => {
    const count = clips.length;
    return `Your clips ${count}`;
  }, [clips.length]);

  async function fetchResults(signal?: AbortSignal) {
    if (!projectId) return;
    try {
      const res = await fetch(`/api/vizard/query?projectId=${encodeURIComponent(projectId)}`, {
        method: "GET",
        signal,
      });
      const json = await res.json();
      if (!res.ok) {
        throw new Error(json?.error || "Failed to query Vizard");
      }
      const normalized = json?.result as {
        projectId: string;
        status?: string;
        clips: UiClip[];
      };
      setStatus(normalized?.status);
      setClips(normalized?.clips || []);
      setError(null);
      // Simulate progress based on polling attempts and a 10-minute max duration
      // 10 minutes = 600 seconds. If a poll attempt is roughly every ~2 minutes (120 seconds),
      // and we have 8 attempts, total time is ~2 minutes.
      // To show progress for 10 minutes, we need a different approach.
      // Let's assume a linear progression for now, or tie it to the polling logic.
      // For simplicity, I'll update progress based on a rough estimate of time elapsed
      // or just a simple increment for demonstration.
      // A better solution would involve the backend sending progress updates.
      // For now, I'll just increment it based on polling attempts, assuming max 10 mins (600s).
      // Each poll attempt could represent (600 / 8) = 75 seconds of progress.
      // So, if attempt is 1, progress is 7.5%, attempt 2 is 15%, etc.
      const maxPollingAttempts = 18; // Based on the length of the 'delays' array
      const currentProgress = Math.min(100, Math.round((pollAttemptRef.current / maxPollingAttempts) * 100));
      setProgress(currentProgress);

      return normalized;
    } catch (e: any) {
      setError(e?.message || "Query error");
      return null;
    }
  }

  // Polling logic: up to ~2 minutes with exponential backoff
  useEffect(() => {
    if (!projectId) return;

    // Save projectId to localStorage whenever it changes and is available
    localStorage.setItem("lastVisitedProjectId", projectId);

    let mounted = true;
    let timeoutId: any;

    const controller = new AbortController();
    pollAbort.current = controller;

    const poll = async () => {
      pollAttemptRef.current++;
      const result = await fetchResults(controller.signal);
      if (!mounted) return;

      const done =
        result && result.clips && Array.isArray(result.clips) && result.clips.length > 0;

      if (done) {
        setPolling(false);
        return;
      }

      if (pollAttemptRef.current >= 18) {
        // ~ stops after attempts; then user can manual refresh
        setPolling(false);
        setProgress(95); // Mark as 100% if polling stops without clips
        return;
      }

      // exponential backoff: 2s, 3s, 5s, 8s, 13s, ...
      const delays = [2000, 3000, 5000, 8000, 13000, 21000, 34000, 55000,60000, 60000, 60000, 60000, 60000, 60000, 60000, 60000, 60000, 60000];
      const delay = delays[Math.min(pollAttemptRef.current - 1, delays.length - 1)];
      setPolling(true);
      timeoutId = setTimeout(poll, delay);
    };

    poll();

    return () => {
      mounted = false;
      if (timeoutId) clearTimeout(timeoutId);
      controller.abort();
      setPolling(false);
    };
  }, [projectId]);

  return (
    <div className="flex-1 w-full px-0 pt-10" style={{ fontFamily: "inherit" }}>
      <div className="w-full max-w-[1100px] mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1
            className="text-[20px] font-medium mb-1 tracking-[0.01em]"
            style={{ color: "#a78bfa" }}
          >
            {headerTitle}
          </h1>
          <p className="text-[14px] text-white/70">
            {projectId ? (
              <>
                Project: <span className="opacity-100">{projectId}</span>{" "}
                {status && <span className="opacity-60">({status})</span>}
              </>
            ) : (
              "Missing projectId"
            )}
          </p>

          {/* Controls */}
          <div className="mt-3 flex items-center gap-2">
            <button
              className="h-9 px-4 rounded-full border border-[#4a427e] bg-transparent text-white/90 hover:text-white"
              onClick={() => fetchResults()}
              disabled={!projectId || polling}
            >
              Refresh
            </button>
            {error && <span className="text-red-400 text-sm">Error: {error}</span>}
          </div>
        </div>

        {/* Empty / Processing states */}
        {(!clips || clips.length === 0) && polling && (
          <div className="rounded-2xl border border-white/10 bg-[#0A0F14] p-4 sm:p-6 mb-8">
            <div className="relative overflow-hidden rounded-xl border border-white/10 bg-[#0B1318]">
              <div className="relative" style={{ width: '100%', minHeight: 260 }}>
                <div className="flex h-64 w-full items-center justify-center text-[#A9B4FF]">
                  <div className="flex flex-col items-center gap-4">
                    <span className="animate-pulse"><AnalyzingIcon /></span>
                    <div className="flex items-center gap-2">
                      <span>Generating</span>
                      <span className="inline-flex">
                      <GeneratingLoadingIcon />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-white/70">
                      Processing: {progress}% (estimated 10 minutes)
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2.5 dark:bg-gray-700">
                      <div className="bg-[#A9B4FF] h-2.5 rounded-full" style={{ width: `${progress}%` }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-white/70 text-sm mt-4">
              {status ? `Status: ${status}.` : "Processing..."} Your clips will appear here when ready.
            </div>
          </div>
        )}
        {(!clips || clips.length === 0) && !polling && (
          <div className="text-white/70 text-sm mb-8">
            No clips found or processing stopped. Please refresh to try again.
          </div>
        )}

        {/* Clips */}
        <div className="space-y-12">
          {clips.map((clip, idx) => (
            <div
              key={clip.id}
              className="grid grid-cols-1 md:grid-cols-[320px_1fr] gap-6 items-start"
            >
              {/* Thumbnail */}
              <div className="rounded-xl overflow-hidden border border-[#2e3a52] bg-[#0d1715] w-full h-[220px] md:h-[260px]">
                <div className="relative w-full h-full">
                  {clip.downloadUrl ? (
                    <video
                      src={clip.downloadUrl}
                      // alt={clip.title}
                      // fill
                      className="object-cover"
                      // priority={idx === 0}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-white/40">
                      No thumbnail
                    </div>
                  )}
                </div>
              </div>

              {/* Right content */}
              <div>
                <div className="text-white mb-2">
                  {clip.title} ({formatDuration(clip.durationSeconds)})
                </div>
                <div className="mb-2">
                  <Link href="#" className="text-[#8fa2ff] hover:underline">
                    {/* Placeholder virality info; not provided by API */}
                    #{idx + 1}  Virality score ({clip.viralScore}/10)
                  </Link>
                </div>
                {/* Transcript block */}
                {clip.transcript && (
                  <div className="mb-4">
                    <div className="inline-block rounded-lg bg-[#2c2850] text-white px-3 py-1 mb-2">
                      Transcript
                    </div>
                    <div className="rounded-xl border border-[#473f7a] bg-[#1e1b31] text-white/90 p-4 text-[14px] leading-relaxed">
                      {clip.transcript}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center gap-3">
                  {clip.downloadUrl ? (
                    <a
                      href={clip.downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="h-10 px-5 rounded-full text-white text-[14px] font-medium flex items-center justify-center"
                      style={{
                        background:
                          "linear-gradient(90deg,#8b7bfa 0%,#a78bfa 100%)",
                        boxShadow: "0 0 14px 1px #a78bfa55",
                      }}
                    >
                      Download HD
                    </a>
                  ) : (
                    <button
                      disabled
                      className="h-10 px-5 rounded-full border border-[#4a427e] bg-transparent text-white/60"
                    >
                      Download unavailable
                    </button>
                  )}
                  <button className="h-10 px-5 rounded-full border border-[#4a427e] bg-transparent text-white/90 hover:text-white">
                    Edit
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Spacer bottom */}
        <div className="h-20" />
      </div>
    </div>
  );
}