import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OpenAI_API_KEY,
});

export async function POST(req: NextRequest) {
  const { text, type } = await req.json();

  if (!text || !type) {
    return NextResponse.json({ error: 'text and type are required' }, { status: 400 });
  }

  let prompt;
  switch (type) {
    case 'titles':
      prompt = ` Task: Analyze the following text or video transcript. 
Objective: Identify the main problem, how it's agitated or emotionally amplified, and the solution offered or implied. Then, craft 5 hooky, attention-grabbing titles using the Problem-Agitate-Solve (PAS) copywriting formula. 
The title should be: 
Short (preferably under 14 words) 
Emotionally charged or curiosity-driven 
Clear about the value or outcome 
Suitable for YouTube, social media, blog, or video content 
The only output should be the 5 PAS Titles. 
Do not add serial numbers to the title.
Do not include any explanation, formatting, em dash or extra text. 
Transcript/Text:

\n\n${text}`;
      break;
    case 'description':
      prompt = `
      # CONTEXT
Analyze the content of the Text/Video Transcript to identify the topic, tone, and takeaway.
---
# CAPTION STYLE
\<caption\_style>
* Write in a raw, direct, conversational tone.
* Use simple sentences (5th-grade level). 
* Include curiosity hooks or bold claims early.
* Speak to the viewer as “you” or “we.”
* Avoid em dash, emojis, asterisks, or filler words. 
- Do not use em dash.
- Do not use semicolons.
- Do not use emojis.
- Do not use asterisks.
* Never use passive voice.
* No fluff. No hype. No metaphors or cliches.
* Make the viewer want to stay and watch.
* Do not summarize the video—make them curious.
- Do NOT use these words:
""""""fluff, no fluff, delve, embark, enlightening, esteemed, shed light, craft, crafting, imagine, realm, game-changer, unlock, discover, skyrocket, abyss, you're not alone, in a world where, revolutionize, disruptive, utilize, utilizing, dive deep, tapestry, illuminate, unveil, pivotal, enrich, intricate, elucidate, hence, furthermore, realm, however, harness, exciting, groundbreaking, cutting-edge, remarkable, it. remains to be seen, glimpse into, navigating, landscape, stark, testament, in summary, in conclusion, moreover, boost, bustling, opened up, powerful, inquiries, ever-evolving""""""  
* Follow the structure and tone of these youtube video description-style examples:
<example1>
Making long videos sounds hard. But what if you didn’t need to record, edit, or narrate anything? That’s what I’m showing you in this video. I take one tool and use it to create animated stories from text in minutes. It’s the fastest way I’ve found to post high-quality content without editing stress. I’ll walk you through how I build a video from start to finish—no fancy skills or apps needed. If you want to post more videos without spending hours on them, this is the method to try. Watch and follow along.
</example1>
<example2>
You don’t need to learn video editing to make great content. I’ll show you how I turn written stories into long-form animated videos with almost no effort. No voiceover. No software installs. Just one tool that does the hard part for you. This is what I use to post more videos fast without burning out. If you’re trying to grow a channel, stay consistent, or just make content without stress, you’ll want to see this. Watch the full process, then try it today.
</example2>
<example3>
Want to make full animated story videos without editing anything? You don’t need fancy software or tech skills. In this video, I’ll show you how I create long-form animated content using one tool that handles it all. You write your script, and the rest is drag and drop. No editing timeline. No need for a voiceover. Just write, generate, and publish. If you're tired of wasting hours learning complex apps, this will save you a ton of time. Perfect for YouTubers, storytellers, or anyone who just wants to post more without stress. Watch the full breakdown now and try it for yourself today.
</example3>
\</caption\_style>
---
# PLANNING
1. Study the Text/Video Transcript carefully.
2. Infer the key insight, pain point, or revelation.
3. Write an youtube video description that:
   * Builds tension or interest.
   * Makes people stop scrolling.
   * Sounds natural and unscripted.
4. Keep it under 1500 characters. 
5. The final caption should be 1 paragraph.
6. Searchable and keyword-optimized
---
<guidelines>
You are an expert youtube marketer writing a viral youtube video description. 
</guidelines>

# OUTPUT
Generate a youtube video description following all the rules and using the Text/Video Transcript provided below:
\n\n${text}`;
      break;
    case 'keywords':
      prompt = `Task: Analyze the following text or video transcript.
- For "longtail", provide an array of 5 long-tail keywords (4 or more words).
- For "midtail", provide an array of 10 mid-tail keywords (2-3 words).
- For "shorttail", provide an array of 15 short-tail keywords (1 word).
Ensure the total character count of all keywords combined does not exceed 500 characters.

Text: "${text}"

Respond with only the JSON object.`;
      break;
    case 'hashtags':
      prompt = `Task: Analyze the following text or video transcript.
Objective: Identify the core theme, keywords, audience intent, and content niche. Then, generate 20 high-impact, searchable YouTube hashtags that increase discoverability and engagement.
Guidelines:
You are an expert YouTube marketer writing viral YouTube Hashtags. Research relevant hashtags.
Use trending, relevant hashtags
Use #
Mix broad and niche-specific tags
Keep each hashtag under 30 characters
Do not add explanations, formatting, or serial numbers
Only output the hashtags
Write in lowercase
Do not use a new line to separate the hashtags
The final output should be one paragraph:\n\n${text}`;
      break;
    default:
      return NextResponse.json({ error: 'Invalid type' }, { status: 400 });
  }

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [{ role: 'user', content: prompt }],
    });
    
    const content = response.choices[0].message?.content;
    return NextResponse.json({ content });
  } catch (error) {
    console.error('OpenAI API error:', error);
    return NextResponse.json({ error: 'Failed to generate content' }, { status: 500 });
  }
}
