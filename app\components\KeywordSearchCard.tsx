"use client";
import { useState } from "react";

interface Tab {
  label: string;
  key: string;
}

const tabs: Tab[] = [
  { label: "Related Keywords", key: "related" },
  { label: "Matching Terms", key: "matching" },
];

interface KeywordSearchCardProps {
  onTabChange?: (key: string) => void;
  onSearch?: (query: string) => void;
  isLoading?: boolean;
}

export default function KeywordSearchCard({
  onTabChange,
  onSearch,
  isLoading = false,
}: KeywordSearchCardProps) {
  const [active, setActive] = useState<string>(tabs[0].key);
  const [query, setQuery] = useState<string>("");

  const handleTabClick = (key: string) => {
    setActive(key);
    onTabChange?.(key);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim() && !isLoading) {
      onSearch?.(query.trim());
    }
  };

  return (
    <div className="border border-[#2a2a3e] rounded-xl p-6 bg-gradient-to-r from-[#030F0F] via-[#030F0F] to-[#6246ea]/20">
      <form onSubmit={handleSubmit} className="mb-6">
        <label className="block text-sm uppercase text-gray-300 mb-6 tracking-widest">
          Keyword Insights
        </label>
        <div className="relative">
          <input
            type="text"
            placeholder="Search keywords"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            disabled={isLoading}
            className="w-full px-4 py-3 pr-12 rounded-full bg-transparent border border-gray-700 focus:outline-none focus:ring-2 focus:ring-[#6246ea] text-sm placeholder-gray-500 disabled:opacity-50"
          />
          <button
            type="submit"
            disabled={isLoading || !query.trim()}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 px-3 py-1 bg-[#6246ea] text-white rounded-full text-sm hover:bg-[#5235d1] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? 'Searching...' : 'Search'}
          </button>
        </div>
      </form>
      <div className="flex space-x-4">
        {tabs.map((tab) => (
          <button
            key={tab.key}
            type="button"
            onClick={() => handleTabClick(tab.key)}
            className={`px-6 py-2 rounded-full text-lg font-medium transition ${active === tab.key ? 'bg-[#8B7CF8] bg-opacity-20 text-white' : 'text-gray-400 hover:text-white'}`}
          >
            {tab.label}
          </button>
        ))}
      </div>
    </div>
  );
}
