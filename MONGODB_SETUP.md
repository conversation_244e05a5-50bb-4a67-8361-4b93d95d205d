# MongoDB Setup for Chat History

This application now uses MongoDB to persist chat history instead of localStorage. Here's how to set it up:

## 1. Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```env
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/keoo-ai
MONGODB_DB=keoo-ai

# NextAuth Configuration (if using authentication)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
```

## 2. MongoDB Setup Options

### Option A: Local MongoDB Installation

1. Install MongoDB Community Edition on your system
2. Start the MongoDB service
3. Create a database named `keoo-ai`

### Option B: MongoDB Atlas (Cloud)

1. Sign up for MongoDB Atlas (free tier available)
2. Create a new cluster
3. Get your connection string
4. Replace `MONGODB_URI` with your Atlas connection string

### Option C: Docker MongoDB

```bash
# Run MongoDB in Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Or with Docker Compose
docker-compose up -d
```

## 3. Database Schema

The application will automatically create the following collections:

- `chat_history`: Stores chat sessions with the following structure:
  ```json
  {
    "_id": "ObjectId",
    "sessionId": "string",
    "title": "string",
    "messages": [
      {
        "id": "string",
        "role": "user|assistant",
        "content": "string",
        "timestamp": "ISO string"
      }
    ],
    "createdAt": "Date",
    "updatedAt": "Date"
  }
  ```

## 4. API Endpoints

The following API endpoints are available for chat history management:

- `GET /api/chat-history`: Fetch all chat sessions
- `POST /api/chat-history`: Save/update a chat session
- `DELETE /api/chat-history`: Delete all sessions
- `DELETE /api/chat-history?sessionId=xxx`: Delete specific session

## 5. Features

- **Persistent Storage**: Chat history is now saved in MongoDB and persists across browser sessions
- **Cross-Device Sync**: History is available on all devices when using the same database
- **Automatic Backup**: Database backups can be configured for data safety
- **Scalability**: MongoDB can handle large amounts of chat data efficiently

## 6. Migration from localStorage

If you have existing chat history in localStorage, it will be automatically migrated to MongoDB when you:
1. Start a new session
2. Add messages to any session
3. The data will be saved to MongoDB and can be accessed from the sidebar

## 7. Troubleshooting

### Connection Issues
- Ensure MongoDB is running
- Check your connection string
- Verify network connectivity (for Atlas)

### Database Permissions
- Ensure the database user has read/write permissions
- For Atlas, check IP whitelist settings

### Environment Variables
- Make sure `.env.local` is in the root directory
- Restart the development server after adding environment variables

## 8. Development vs Production

### Development
- Use local MongoDB or Atlas free tier
- Enable detailed logging for debugging

### Production
- Use MongoDB Atlas or self-hosted MongoDB with proper security
- Set up database backups
- Configure proper indexes for performance
- Use environment-specific connection strings 