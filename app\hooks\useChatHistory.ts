import { useState, useEffect, useCallback } from 'react';
import type { HistorySection, HistoryItem, ChatSession, ChatMessage } from '../components/types';

interface UseChatHistoryReturn {
  currentSession: ChatSession | null;
  historySections: HistorySection[];
  isLoading: boolean;
  startNewSession: (mode?: 'text-to-image' | 'image-to-image' | 'text-to-video' | 'image-to-video' | 'text-to-speech' | 'music-generation' | 'sound-effects' | 'lip-sync') => void;
  addMessage: (message: ChatMessage) => void;
  loadSession: (sessionId: string) => void;
  deleteSession: (sessionId: string) => void;
  clearAllHistory: () => void;
  saveSession: () => void;
}

export function useChatHistory(): UseChatHistoryReturn {
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [historySections, setHistorySections] = useState<HistorySection[]>([]);
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load sessions from MongoDB on component mount
  useEffect(() => {
    loadSessionsFromDatabase();
  }, [currentSession]);

  // Update history sections when sessions change
  useEffect(() => {
    updateHistorySections();
  }, [sessions, currentSession]);

  // Load sessions from MongoDB
  const loadSessionsFromDatabase = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/chat-history');
      if (response.ok) {
        const data = await response.json();
        // Convert string dates back to Date objects and filter out empty sessions
        const sessionsWithDates = (data.sessions || [])
          .map((session: any) => ({
            ...session,
            id: session.id || session.sessionId, // Ensure we have the id field
            createdAt: new Date(session.createdAt),
            updatedAt: new Date(session.updatedAt),
            messages: session.messages.map((msg: any) => ({
              ...msg,
              timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date()
            })),
            active: session.id === currentSession?.id
          }))
          .filter((session: ChatSession) => {
            // Filter out sessions with no messages or only empty messages
            return session.messages && 
                   session.messages.length > 0 && 
                   session.messages.some(msg => msg.content && msg.content.trim().length > 0);
          })
          .slice(0, 7); // Limit to 7 most recent sessions
        
        setSessions(sessionsWithDates);
      }
    } catch (error) {
      console.error('Error loading sessions from database:', error);
    } finally {
      setIsLoading(false);
    }
  }, [currentSession]);

  // Save session to MongoDB
  const saveSessionToDatabase = useCallback(async (session: ChatSession) => {
    try {
      const response = await fetch('/api/chat-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: session.id,
          title: session.title,
          messages: session.messages,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save session');
      }
    } catch (error) {
      console.error('Error saving session to database:', error);
    }
  }, []);

  // Delete session from MongoDB
  const deleteSessionFromDatabase = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/chat-history?sessionId=${sessionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete session');
      }
    } catch (error) {
      console.error('Error deleting session from database:', error);
    }
  }, []);

  // Clear all sessions from MongoDB
  const clearAllSessionsFromDatabase = useCallback(async () => {
    try {
      const response = await fetch('/api/chat-history', {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to clear all sessions');
      }
    } catch (error) {
      console.error('Error clearing all sessions from database:', error);
    }
  }, []);

  // Update history sections from sessions
  const updateHistorySections = useCallback(() => {
    if (sessions.length === 0) {
      setHistorySections([]);
      return;
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const todaySessions: HistoryItem[] = [];
    const yesterdaySessions: HistoryItem[] = [];
    const weekSessions: HistoryItem[] = [];

    sessions.forEach(session => {
      const sessionDate = new Date(session.createdAt);
      
      // Use messages[0].content directly as the history name
      let historyName = '';
      if (session.messages && session.messages.length > 0 && session.messages[0].content) {
        // Use the first message content directly
        historyName = session.messages[0].content;
        
        // Truncate if too long for display
        if (historyName.length > 50) {
          historyName = historyName.substring(0, 50) + '...';
        }
      } else {
        // Fallback to formatted time if no messages
        historyName = sessionDate.toLocaleString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }

      const historyItem: HistoryItem = {
        text: historyName,
        sessionId: session.id,
        active: session.active
      };

      if (sessionDate >= today) {
        todaySessions.push(historyItem);
      } else if (sessionDate >= yesterday) {
        yesterdaySessions.push(historyItem);
      } else if (sessionDate >= weekAgo) {
        weekSessions.push(historyItem);
      }
    });

    const sections: HistorySection[] = [];

    if (todaySessions.length > 0) {
      sections.push({
        title: "Today",
        titleColor: "text-[#6366f1]",
        items: todaySessions
      });
    }

    if (yesterdaySessions.length > 0) {
      sections.push({
        title: "Yesterday",
        titleColor: "text-[#6b7873]",
        items: yesterdaySessions
      });
    }

    if (weekSessions.length > 0) {
      sections.push({
        title: "Previous 7 Days",
        titleColor: "text-[#6b7873]",
        items: weekSessions
      });
    }

    setHistorySections(sections);
  }, [sessions, currentSession]);

  // Start a new chat session
  const startNewSession = useCallback((mode?: 'text-to-image' | 'image-to-image' | 'text-to-video' | 'image-to-video' | 'text-to-speech' | 'music-generation' | 'sound-effects' | 'lip-sync') => {
    const newSession: ChatSession = {
      id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: 'New Chat',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      mode: mode || 'text-to-image' // Default mode
    };

    setCurrentSession(newSession);
    
    // Don't add to sessions list or save to database until there are messages
    // This prevents empty sessions from being saved
  }, []);

  // Add a message to the current session
  const addMessage = useCallback((message: ChatMessage) => {
    if (!currentSession) return;

    const updatedSession: ChatSession = {
      ...currentSession,
      messages: [...currentSession.messages, message],
      updatedAt: new Date()
    };

    // Update title if it's the first user message
    if (message.type === 'user' && currentSession.messages.length === 0) {
      // Use the first message content directly as title
      let title = message.content.trim();
      
      // Limit length and add ellipsis if needed
      if (title.length > 60) {
        title = title.substring(0, 60) + '...';
      }
      
      // If title is empty or too short, use a default
      if (title.length < 3) {
        title = 'New Chat';
      }
      
      updatedSession.title = title;
      
      // Only add to sessions list and save to database when we have the first message
      setSessions(prev => [updatedSession, ...prev]);
      saveSessionToDatabase(updatedSession);
    } else {
      // For subsequent messages, just update the existing session
      setSessions(prev => 
        prev.map(session => 
          session.id === currentSession.id ? updatedSession : session
        )
      );
      saveSessionToDatabase(updatedSession);
    }

    setCurrentSession(updatedSession);
  }, [currentSession, saveSessionToDatabase]);

  // Load a specific session
  const loadSession = useCallback((sessionId: string) => {
    console.log('Loading session:', sessionId);
    console.log('Available sessions:', sessions.map(s => ({ id: s.id, title: s.title })));
    
    const session = sessions.find(s => s.id === sessionId);
    if (session) {
      console.log('Found session:', session);
      setCurrentSession(session);
    } else {
      console.log('Session not found in local state, trying to reload from database...');
      // If session not found in local state, try to reload from database
      loadSessionsFromDatabase().then(() => {
        // After reloading, try to find the session again
        const reloadedSessions = sessions;
        const foundSession = reloadedSessions.find(s => s.id === sessionId);
        if (foundSession) {
          console.log('Found session after reload:', foundSession);
          setCurrentSession(foundSession);
        } else {
          console.error('Session not found even after reloading from database');
        }
      });
    }
  }, [sessions, loadSessionsFromDatabase]);

  // Delete a specific session
  const deleteSession = useCallback(async (sessionId: string) => {
    // Remove from local state
    setSessions(prev => prev.filter(session => session.id !== sessionId));
    
    // If it's the current session, start a new one
    if (currentSession?.id === sessionId) {
      startNewSession();
    }

    // Delete from database
    await deleteSessionFromDatabase(sessionId);
  }, [currentSession, startNewSession, deleteSessionFromDatabase]);

  // Clear all history
  const clearAllHistory = useCallback(async () => {
    setSessions([]);
    setHistorySections([]);
    startNewSession();

    // Clear from database
    await clearAllSessionsFromDatabase();
  }, [startNewSession, clearAllSessionsFromDatabase]);

  // Save current session (explicit save)
  const saveSession = useCallback(() => {
    if (currentSession) {
      saveSessionToDatabase(currentSession);
    }
  }, [currentSession, saveSessionToDatabase]);

  return {
    currentSession,
    historySections,
    isLoading,
    startNewSession,
    addMessage,
    loadSession,
    deleteSession,
    clearAllHistory,
    saveSession
  };
}
