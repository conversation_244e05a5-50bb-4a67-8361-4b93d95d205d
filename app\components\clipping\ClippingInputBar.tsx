"use client";
import React from "react";
import { GeneratebtnIcon } from "../icons/icons";

type Props = {
  value: string;
  onChange: (v: string) => void;
  onSubmit?: () => void;
  isLoading?: boolean;
  placeholder?: string;
  label?: string;
};

export default function ClippingInputBar({
  value,
  onChange,
  onSubmit,
  isLoading = false,
  placeholder = "https://www.youtube.com/...",
  label = "Your Video",
}: Props) {
  return (
    <div
      className="w-full flex items-center mt-8 mb-8"
      style={{
        borderRadius: 14,
        background: "rgba(32,32,64,0.15)",
        boxShadow:
          "0 0 0 1px #39395c, 0 2px 32px 0 rgba(120,80,255,0.11)",
      }}
    >
      <div className="flex flex-col flex-1">
        <label className="text-[13px] text-white/80 mb-1 ml-6 mt-5">
          {label}
        </label>

        <div className="flex items-center w-full px-6 pb-6">
          {/* Left adornment capsule with upload/arrow icon */}
          <span
            className="flex items-center h-12 px-4 bg-transparent border border-[#8b7bfa] rounded-l-full text-white/70 text-[15px]"
            style={{ borderRight: "none" }}
            aria-hidden
          >
            <svg
              width="19"
              height="19"
              fill="none"
              stroke="#8b7bfa"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path d="M12 17V3m0 0L7 8m5-5l5 5" />
              <rect
                x="3"
                y="17"
                width="18"
                height="4"
                rx="2"
                fill="#161634"
              />
            </svg>
          </span>

          {/* URL input */}
          <input
            className="flex-1 h-12 px-4 bg-transparent border border-[#8b7bfa] border-l-0 rounded-none text-white text-[15px] focus:outline-none placeholder:text-white/40"
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && onSubmit && !isLoading) onSubmit();
            }}
            style={{
              borderTopRightRadius: 0,
              borderBottomRightRadius: 0,
              borderTopLeftRadius: 0,
              borderBottomLeftRadius: 0,
              minWidth: "360px",
            }}
            aria-label={label}
          />

          {/* Right gradient strip and Generate button area */}
          <div className="relative ml-2">
            {/* Gradient strip to mimic mock's grain/blur transition */}
            <div
              aria-hidden
              className="absolute -left-[148px] top-1/2 -translate-y-1/2 h-12 w-[148px] rounded-l-full"
              style={{
                background:
                  "linear-gradient(90deg, rgba(0,0,0,0.0) 0%, rgba(98,70,234,0.35) 60%, rgba(167,139,250,0.65) 100%)",
                maskImage:
                  "linear-gradient(to right, transparent 0%, black 20%, black 100%)",
                WebkitMaskImage:
                  "linear-gradient(to right, transparent 0%, black 20%, black 100%)",
              }}
            />

            <button
              type="button"
              aria-label="Generate"
              disabled={isLoading}
              onClick={onSubmit}
              className="h-12 px-8 rounded-full font-medium text-white text-[16px] flex items-center justify-center relative"
              style={{
                background:
                  "linear-gradient(90deg,#8b7bfa 0%,#a78bfa 100%)",
                boxShadow: "0 0 18px 2px #a78bfa55",
                opacity: isLoading ? 0.7 : 1,
              }}
            >
              {isLoading ? (
                <span className="relative flex items-center">
                  <span className="mr-2 inline-block h-4 w-4 rounded-full border-2 border-white/70 border-t-transparent animate-spin" />
                  Generating...
                </span>
              ) : (
                <>
                  <GeneratebtnIcon /> <span className="ml-2">Generate</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}