import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';
import cloudinary from '../../../lib/cloudinary';
import * as cheerio from 'cheerio';


const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// Helper function to upload image to Cloudinary
async function uploadToCloudinary(dataUrl: string): Promise<string> {
  try {
    const result = await cloudinary.uploader.upload(dataUrl, {
      folder: 'social-post-images',
    });
    return result.secure_url;
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw new Error('Failed to upload image to Cloudinary');
  }
}

// Helper function to extract text from URL
async function extractTextFromUrl(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    const html = await response.text();
    const $ = cheerio.load(html);
    
    // Remove script and style elements
    $('script, style').remove();
    
    // Extract text from common content areas
    const title = $('title').text();
    const metaDescription = $('meta[name="description"]').attr('content') || '';
    const h1 = $('h1').text();
    const paragraphs = $('p').map((_, el) => $(el).text()).get().join(' ');
    const articles = $('article').text();
    const main = $('main').text();
    
    const extractedText = [title, metaDescription, h1, paragraphs, articles, main]
      .filter(text => text && text.trim().length > 0)
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    return extractedText || $('body').text().replace(/\s+/g, ' ').trim();
  } catch (error) {
    console.error('Error extracting text from URL:', error);
    throw new Error('Failed to extract text from URL');
  }
}

// Helper function to extract video captions
async function extractVideoTranscript(url: string, platform: 'youtube' | 'tiktok' | 'reels'): Promise<string> {
  try {
    if (platform === 'youtube') {
      const videoIdMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
      if (!videoIdMatch) {
        throw new Error('Invalid YouTube URL');
      }
      
      const videoId = videoIdMatch[1];
      
      // Use the internal API route to fetch the transcript
      const apiBaseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const response = await fetch(`${apiBaseUrl}/api/youtube-transcript`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ videoId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch transcript via internal API');
      }

      const { transcript } = await response.json();
      if (!transcript) {
        throw new Error('No transcript returned from internal API');
      }
      
      return transcript;
    } else {
      // Placeholder for other platforms
      throw new Error(`${platform} transcript extraction not yet implemented`);
    }
  } catch (error) {
    console.error(`Error extracting ${platform} transcript:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    throw new Error(`Failed to extract ${platform} transcript: ${errorMessage}`);
  }
}

// Helper function to transcribe audio
async function transcribeAudio(audioUrl: string): Promise<string> {
  try {
    // Using Replicate's Whisper model for audio transcription
    const output = await replicate.run(
      "openai/whisper:4d50797290df275329f202e48c76360b3f22b08d28c196cbc54600319435f8d2",
      {
        input: {
          audio: audioUrl,
          model: "large-v3",
          translate: false,
          temperature: 0,
          transcription: "plain text",
          suppress_tokens: "-1",
          logprob_threshold: -1,
          no_speech_threshold: 0.6,
          condition_on_previous_text: true,
          compression_ratio_threshold: 2.4,
          temperature_increment_on_fallback: 0.2
        }
      }
    );
    
    return output as unknown as string;
  } catch (error) {
    console.error('Error transcribing audio:', error);
    throw new Error('Failed to transcribe audio');
  }
}

// Helper function to extract text from documents
async function extractTextFromDocument(fileUrl: string, fileType: string): Promise<string> {
  try {
    const response = await fetch(fileUrl);
    const buffer = await response.arrayBuffer();
    
    if (fileType.includes('pdf')) {
      const pdf = (await import('pdf-parse')).default;
      const data = await pdf(Buffer.from(buffer));
      return data.text;
    } else if (fileType.includes('word') || fileType.includes('docx')) {
      const mammoth = (await import('mammoth')).default;
      const result = await mammoth.extractRawText({ buffer: Buffer.from(buffer) });
      return result.value;
    } else {
      throw new Error('Unsupported document type');
    }
  } catch (error) {
    console.error('Error extracting text from document:', error);
    throw new Error('Failed to extract text from document');
  }
}

// Helper function to generate content using AI
async function generateContent(text: string, type: 'title' | 'caption' | 'image-prompt'): Promise<string> {
  try {
    if (typeof text !== 'string') {
      // Or handle this case as you see fit, maybe return a default value or throw an error
      text = String(text);
    }
    let prompt = '';
    
    switch (type) {
      case 'title':
        prompt = `Summarize this text into a catchy, concise, and engaging title for a social media post. The title should be attention-grabbing and under 100 characters:\n\n${text}`;
        break;
      case 'caption':
        prompt = `Summarize this text into a compelling and informative caption suitable for Instagram, LinkedIn, and other social media platforms. The caption should be engaging, informative, and include relevant hashtags:\n\n${text}`;
        break;
      case 'image-prompt':
        prompt = `Summarize this text into a visual scene description prompt suitable for generating an image. Focus on creating a vivid, detailed description that would make an engaging visual for social media:\n\n${text}`;
        break;
    }
    
    const output = await replicate.run(
      "meta/llama-2-70b-chat:02e509c789964a7ea8736978a43525956ef40397be9033abf9fd2badfe68c9e3",
      {
        input: {
          prompt,
          max_new_tokens: type === 'title' ? 100 : type === 'image-prompt' ? 200 : 500,
          temperature: 0.7,
          top_p: 0.9,
          repetition_penalty: 1.1
        }
      }
    );
    
    return Array.isArray(output) ? output.join('') : output as unknown as string;
  } catch (error) {
    console.error(`Error generating ${type}:`, error);
    throw new Error(`Failed to generate ${type}`);
  }
}

// Helper function to generate image
async function generateImage(prompt: string): Promise<string> {
  try {
    const output = await replicate.run(
      "black-forest-labs/flux-1.1-pro-ultra",
      {
        input: {
          prompt,
          aspect_ratio: "1:1",
          output_format: "jpg",
          safety_tolerance: 2,
          seed: Math.floor(Math.random() * 1000000)
        }
      }
    );
    
    // Handle the output (similar to existing generate-image logic)
    let imageUrl = '';
    
    if (output && typeof output === 'object' && Symbol.asyncIterator in output) {
      const chunks: Uint8Array[] = [];
      
      for await (const chunk of output as AsyncIterable<Uint8Array>) {
        if (chunk instanceof Uint8Array) {
          chunks.push(chunk);
        }
      }
      
      if (chunks.length > 0) {
        const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const combinedBuffer = new Uint8Array(totalLength);
        let offset = 0;
        
        for (const chunk of chunks) {
          combinedBuffer.set(chunk, offset);
          offset += chunk.length;
        }
        
        const base64String = Buffer.from(combinedBuffer).toString('base64');
        const dataUrl = `data:image/jpeg;base64,${base64String}`;
        imageUrl = await uploadToCloudinary(dataUrl);
      }
    } else if (Array.isArray(output) && output.length > 0) {
      imageUrl = output[0];
    } else if (typeof output === 'string') {
      imageUrl = output;
    }
    
    return imageUrl;
  } catch (error) {
    console.error('Error generating image:', error);
    throw new Error('Failed to generate image');
  }
}

export async function POST(req: NextRequest) {
  try {
    const { sourceType, content, url, fileUrl, fileType } = await req.json();
    
    if (!sourceType) {
      return NextResponse.json(
        { error: "Source type is required" },
        { status: 400 }
      );
    }
    
    let extractedText = '';
    
    // Extract text based on source type
    switch (sourceType) {
      case 'text':
        if (!content) {
          return NextResponse.json(
            { error: "Content is required for text source" },
            { status: 400 }
          );
        }
        extractedText = content;
        break;
        
      case 'article':
        if (!url) {
          return NextResponse.json(
            { error: "URL is required for article source" },
            { status: 400 }
          );
        }
        extractedText = await extractTextFromUrl(url);
        break;
        
      case 'youtube':
        if (!url) {
          return NextResponse.json(
            { error: "URL is required for YouTube source" },
            { status: 400 }
          );
        }
        extractedText = await extractVideoTranscript(url, 'youtube');
        break;
        
      case 'tiktok':
        if (!url) {
          return NextResponse.json(
            { error: "URL is required for TikTok source" },
            { status: 400 }
          );
        }
        extractedText = await extractVideoTranscript(url, 'tiktok');
        break;
        
      case 'reels':
        if (!url) {
          return NextResponse.json(
            { error: "URL is required for Reels source" },
            { status: 400 }
          );
        }
        extractedText = await extractVideoTranscript(url, 'reels');
        break;
        
      case 'audio':
        if (!fileUrl) {
          return NextResponse.json(
            { error: "File URL is required for audio source" },
            { status: 400 }
          );
        }
        extractedText = await transcribeAudio(fileUrl);
        break;
        
      case 'document':
        if (!fileUrl || !fileType) {
          return NextResponse.json(
            { error: "File URL and type are required for document source" },
            { status: 400 }
          );
        }
        extractedText = await extractTextFromDocument(fileUrl, fileType);
        break;
        
      default:
        return NextResponse.json(
          { error: "Invalid source type" },
          { status: 400 }
        );
    }
    
    if (typeof extractedText !== 'string' || extractedText.trim().length === 0) {
      return NextResponse.json(
        { error: "No text could be extracted from the source" },
        { status: 400 }
      );
    }
    
    // Generate all content in parallel for better performance
    const [title, caption, imagePrompt] = await Promise.all([
      generateContent(extractedText, 'title'),
      generateContent(extractedText, 'caption'),
      generateContent(extractedText, 'image-prompt')
    ]);
    
    // Generate image based on the image prompt
    const imageUrl = await generateImage(imagePrompt);
    
    return NextResponse.json({
      success: true,
      data: {
        title: title.trim(),
        caption: caption.trim(),
        imageUrl,
        imagePrompt: imagePrompt.trim(),
        extractedText: extractedText.substring(0, 500) + (extractedText.length > 500 ? '...' : '')
      }
    });
    
  } catch (error) {
    console.error('Social post automation error:', error);
    
    return NextResponse.json(
      { 
        error: "Failed to generate social post content", 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
