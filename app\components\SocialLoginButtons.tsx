"use client";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react";
import AppleSignInInit from "./AppleSignInInit";
import { AppleIcon, Googleicon } from "./icons/icons";

export default function SocialLoginButtons() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const from = searchParams.get("from") || "/dashboard";

  const handleGoogle = async () => {
    // Use next-auth's built-in Google provider; this will hit /api/auth/signin/google
    // and complete at /api/auth/callback/google, setting the next-auth session cookie.
    console.log("[login] starting next-auth Google sign-in with callbackUrl:", from);
    await signIn("google", { callbackUrl: from });
  };

  const handleApple = async () => {
    // Keep existing Apple flow (custom)
    const appleButton = document.getElementById("appleid-signin");
    if (appleButton) {
      appleButton.click();
    }
  };

  return (
    <>
      <AppleSignInInit />
      <div className="flex flex-col gap-3 w-full">
        <button
          onClick={handleGoogle}
          className="flex   gap-3 w-full px-10 py-5 rounded-full bg-[#030F0F] border border-[#35355a] text-white hover:bg-[#1f1f35] hover:border-[#4a4a6a] hover:shadow-lg hover:shadow-black/20 active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-[#7c5cff] transition-all duration-300 ease-in-out transform hover:scale-[1.02]"
        >
          <Googleicon />
          Continue with Google
        </button>

        <button
          onClick={handleApple}
          className="flex  gap-3 w-full px-10 py-5 rounded-full bg-[#030F0F] border border-[#35355a] text-[#F7F5F2] hover:bg-[#1f1f35] hover:border-[#4a4a6a] hover:shadow-lg hover:shadow-black/20 active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-[#7c5cff] transition-all duration-300 ease-in-out transform hover:scale-[1.02]"
        >
          <AppleIcon />
          Continue with Apple
        </button>
      </div>
    </>
  );
}