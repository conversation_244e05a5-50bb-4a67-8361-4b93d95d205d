import React, { RefObject } from 'react';

type Mode = 'text-to-image' | 'image-to-image' | 'text-to-video' | 'image-to-video' | 'text-to-speech' | 'music-generation' | 'sound-effects' | 'lip-sync';

interface ImageUploadModalProps {
  show: boolean;
  onClose: () => void;
  modalImage: string | null;
  setModalImage: (img: string | null) => void;
  modalPrompt: string;
  setModalPrompt: (prompt: string) => void;
  handleModalSubmit: () => void;
  fileInputRef: RefObject<HTMLInputElement>;
  activeMode: Mode;
  videoDuration: number;
  setVideoDuration: (v: number) => void;
  videoAspectRatio: string;
  setVideoAspectRatio: (v: string) => void;
  selectedVideoModel: string;
  getModelOptions: (model: string) => { durations: number[]; aspectRatios: string[] };
  handleModalImageUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const ImageUploadModal: React.FC<ImageUploadModalProps> = ({
  show,
  onClose,
  modalImage,
  setModalImage,
  modalPrompt,
  setModalPrompt,
  handleModalSubmit,
  fileInputRef,
  activeMode,
  videoDuration,
  setVideoDuration,
  videoAspectRatio,
  setVideoAspectRatio,
  selectedVideoModel,
  getModelOptions,
  handleModalImageUpload,
}) => {
  if (!show) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1a1a2e] border border-[#2a2a3e] rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">
            {activeMode === 'image-to-video' ? 'Add Image & Prompt for Video' : 'Add Image & Prompt'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
        {/* Image Upload Area */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">Upload Image</label>
          <div
            className="border-2 border-dashed border-[#2a2a3e] rounded-lg p-6 text-center cursor-pointer hover:border-[#3a3a4e] transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            {modalImage ? (
              <img src={modalImage} alt="Uploaded" className="w-full h-32 object-cover rounded-lg" />
            ) : (
              <div className="flex flex-col items-center">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 mb-2">
                  <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-gray-400 text-sm">Click to upload image</span>
              </div>
            )}
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleModalImageUpload}
            className="hidden"
          />
        </div>
        {/* Prompt Input */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">Prompt</label>
          <textarea
            value={modalPrompt}
            onChange={(e) => setModalPrompt(e.target.value)}
            placeholder={activeMode === 'image-to-video' ? "Describe the video motion you want..." : "Describe how you want to modify the image..."}
            className="w-full h-24 bg-[#0a0f0d] border border-[#2a2a3e] rounded-lg p-3 text-white text-sm resize-none focus:border-[#4a4a6e] focus:outline-none transition-colors"
          />
        </div>
        {/* Video Controls for image-to-video mode */}
        {activeMode === 'image-to-video' && (
          <div className="mb-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Duration</label>
                <select
                  value={videoDuration}
                  onChange={(e) => setVideoDuration(Number(e.target.value))}
                  className="w-full bg-[#0a0f0d] border border-[#2a2a3e] rounded-lg p-2 text-white text-sm focus:border-[#4a4a6e] focus:outline-none transition-colors"
                >
                  {getModelOptions(selectedVideoModel).durations.map(duration => (
                    <option key={duration} value={duration}>{duration} seconds</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Aspect Ratio</label>
                <select
                  value={videoAspectRatio}
                  onChange={(e) => setVideoAspectRatio(e.target.value)}
                  className="w-full bg-[#0a0f0d] border border-[#2a2a3e] rounded-lg p-2 text-white text-sm focus:border-[#4a4a6e] focus:outline-none transition-colors"
                >
                  {getModelOptions(selectedVideoModel).aspectRatios.map(ratio => (
                    <option key={ratio} value={ratio}>
                      {ratio} {ratio === "16:9" ? "(Landscape)" : ratio === "9:16" ? "(Portrait)" : "(Square)"}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 bg-[#2a2a3e] text-white rounded-lg hover:bg-[#3a3a4e] transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleModalSubmit}
            disabled={!modalImage || !modalPrompt.trim()}
            className="flex-1 px-4 py-2 bg-[#6366f1] text-white rounded-lg hover:bg-[#5855eb] disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
          >
            {activeMode === 'image-to-video' ? 'Generate Video' : 'Generate Image'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImageUploadModal; 