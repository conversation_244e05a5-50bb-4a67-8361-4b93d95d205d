# Clipping Feature — UI, Flow, Components, and Notes

This document summarizes the Clipping experience, pixel-spec UI, componentization, navigation flow, and future extension points.

## 1) User Flow

- Entry: [/clipping](app/clipping/page.tsx:1)
  - Page header and description.
  - URL input bar with Generate action.
  - Tabs: Recent | Exported Clips.

- Analyze: Modal opens on Generate (URL required)
  - Component: [app/components/clipping/AnalyzeModal.tsx](app/components/clipping/AnalyzeModal.tsx:1)
  - Sections:
    - Video preview frame
    - “Present” style carousel row
    - Controls:
      - Duration chip + Auto button
      - Language select
      - Reframe toggle
      - Dimensions select
      - Hook toggle
      - Captions toggle
      - Emojis toggle
    - Primary CTA: Generate Clips (navigates)

- Results: [/clipping/results](app/clipping/results/page.tsx:1)
  - Standard app chrome: Header + Sidebar (DashboardLayout)
  - Summary text and list of 10 generated clips (dummy data)
  - Each clip shows:
    - Thumbnail
    - Title (with duration)
    - Virality score link
    - Summary paragraph
    - Transcript label and transcript box
    - Actions: Download HD (gradient), Edit (outlined)

## 2) Components

- Page shell
  - [app/components/DashboardLayout.tsx](app/components/DashboardLayout.tsx:1)
  - [app/components/Header.tsx](app/components/Header.tsx:1)
  - [app/components/Sidebar.tsx](app/components/Sidebar.tsx:1)

- Clipping page-local components
  - [app/components/clipping/ClippingHeader.tsx](app/components/clipping/ClippingHeader.tsx:1)
    - Title “Clipping” in #a78bfa and subtitle beneath; exact tracking and spacing.
  - [app/components/clipping/ClippingInputBar.tsx](app/components/clipping/ClippingInputBar.tsx:1)
    - Labeled URL input with left adornment icon, right gradient strip, and gradient Generate button with glow; Enter submits; loading state supported.
  - [app/components/clipping/ClippingTabs.tsx](app/components/clipping/ClippingTabs.tsx:1)
    - Segmented control: Recent | Exported Clips with aria-selected.
  - [app/components/clipping/AnalyzeModal.tsx](app/components/clipping/AnalyzeModal.tsx:1)
    - Full-screen, scrollable modal, focus trap, Esc and backdrop close; CTA navigates to results.

- Icons
  - [app/components/icons/icons.tsx](app/components/icons/icons.tsx:1) — Clipping, Generate button, and app-wide icons.

## 3) Screens

- Clipping page
  - File: [app/clipping/page.tsx](app/clipping/page.tsx:1)
  - Uses: DashboardLayout, Header, Sidebar, ClippingHeader, ClippingInputBar, ClippingTabs, AnalyzeModal.
  - Behavior:
    - Generate opens AnalyzeModal when URL is non-empty.
    - Modal is scrollable; close via Esc, backdrop, or ×.

- Results page
  - File: [app/clipping/results/page.tsx](app/clipping/results/page.tsx:1)
  - Background: #0a0f0d
  - Uses: Dashboard