import React, { useRef, useState, useEffect } from "react";
import { DownloadIcon, DownloadIconaudio, ShareIcon } from "./icons/icons";

interface AudioPlayerMusicGenProps {
  src: string;
}

const formatTime = (sec: number) => {
  if (isNaN(sec)) return "00:00";
  const h = Math.floor(sec / 3600);
  const m = Math.floor((sec % 3600) / 60);
  const s = Math.floor(sec % 60);
  if (h > 0) {
    return `${h}:${m.toString().padStart(2, "0")}:${s.toString().padStart(2, "0")}`;
  }
  return `${m.toString().padStart(2, "0")}:${s.toString().padStart(2, "0")}`;
};

const AudioPlayerMusicGen: React.FC<AudioPlayerMusicGenProps> = ({ src }) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [playing, setPlaying] = useState(false);
  const [current, setCurrent] = useState(0);
  const [duration, setDuration] = useState(0);

  // Web Audio API
  const audioCtxRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const sourceNodeRef = useRef<MediaElementAudioSourceNode | null>(null);
  const animationIdRef = useRef<number | null>(null);

  // Setup analyser and connect to audio only once
  const setupAnalyser = () => {
    if (!audioRef.current) return;
    if (audioCtxRef.current && analyserRef.current && sourceNodeRef.current) return;
    if (audioCtxRef.current) {
      try {
        analyserRef.current?.disconnect();
        sourceNodeRef.current?.disconnect();
        audioCtxRef.current.close();
      } catch (e) {
        console.error("Error disconnecting audio nodes or closing context:", e);
      }
      audioCtxRef.current = null;
      analyserRef.current = null;
      sourceNodeRef.current = null;
    }
    const ctx = new (window.AudioContext || (window as any).webkitAudioContext)();
    const srcNode = ctx.createMediaElementSource(audioRef.current);
    const analyser = ctx.createAnalyser();
    analyser.fftSize = 256; // Increased for more detail
    srcNode.connect(analyser);
    analyser.connect(ctx.destination);
    audioCtxRef.current = ctx;
    analyserRef.current = analyser;
    sourceNodeRef.current = srcNode;
  };

  const drawWaveform = () => {
    if (!analyserRef.current || !canvasRef.current) return;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      if (!playing || !analyserRef.current) {
        // Stop drawing if not playing
        if (animationIdRef.current) {
          cancelAnimationFrame(animationIdRef.current);
          animationIdRef.current = null;
        }
        // Clear waveform when stopped
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        return;
      }

      analyserRef.current.getByteFrequencyData(dataArray);
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const grad = ctx.createLinearGradient(0, 0, canvas.width, 0);
      grad.addColorStop(0, "#6366f1");
      grad.addColorStop(1, "#f4e07c");
      ctx.fillStyle = grad;

      const barWidth = canvas.width / bufferLength;
      for (let i = 0; i < bufferLength; i++) {
        const barHeight = (dataArray[i] / 255) * canvas.height;
        ctx.fillRect(i * barWidth, canvas.height - barHeight, barWidth - 1, barHeight);
      }
      animationIdRef.current = requestAnimationFrame(draw);
    };
    animationIdRef.current = requestAnimationFrame(draw); // Start the animation loop
  };

  const togglePlay = async () => {
    console.log("togglePlay called");
    if (!audioRef.current) {
      console.error("Audio ref not found");
      return;
    }

    // Log the src to verify it's correct
    console.log("Audio src:", src);
    console.log("audioRef.current.src:", audioRef.current.src);

    if (!audioCtxRef.current) {
      console.log("Setting up analyser for the first time in togglePlay");
      setupAnalyser();
    }

    if (audioCtxRef.current && audioCtxRef.current.state === "suspended") {
      console.log("Resuming audio context");
      await audioCtxRef.current.resume();
    }

    if (playing) {
      console.log("Pausing audio");
      audioRef.current.pause();
    } else {
      console.log("Playing audio");
      try {
        await audioRef.current.play();
        console.log("Play command successful");
      } catch (error) {
        console.error("Error playing audio:", error);
      }
    }
    setPlaying(!playing);

    // Log the entire audio element for inspection
    console.log("Audio element:", audioRef.current);
    if (audioCtxRef.current) {
      console.log("Audio context destination:", audioCtxRef.current.destination);
    }
  };

  const onTimeUpdate = () => {
    if (!audioRef.current) return;
    setCurrent(audioRef.current.currentTime);
  };

  const onLoadedMetadata = () => {
    if (!audioRef.current) return;
    setDuration(audioRef.current.duration);
    if (audioRef.current.src) { // Only attempt to setup analyser if src is valid
      setupAnalyser();
    }
  };

  const onSeek = (e: React.MouseEvent<HTMLCanvasElement, MouseEvent>) => {
    if (!audioRef.current || !canvasRef.current || duration === 0) return;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const pct = x / rect.width;
    const time = pct * duration;
    audioRef.current.currentTime = time;
    setCurrent(time);
  };

  const handleDownload = () => {
    const link = document.createElement("a");
    link.href = src;
    link.download = `audio-${Date.now()}.mp3`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Audio File",
          url: src,
        });
      } catch (error) {
        console.error("Error sharing:", error);
        await navigator.clipboard.writeText(src);
        alert("Copied audio URL to clipboard!");
      }
    } else {
      await navigator.clipboard.writeText(src);
      alert("Web Share API not supported. Copied audio URL to clipboard!");
    }
  };

  useEffect(() => {
    const resize = () => {
      if (canvasRef.current) {
        canvasRef.current.width = canvasRef.current.offsetWidth;
        canvasRef.current.height = 32;
      }
    };
    resize();
    window.addEventListener("resize", resize);
    return () => window.removeEventListener("resize", resize);
  }, []);

  useEffect(() => {
    if (playing) {
      drawWaveform();
    } else {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }
      // Clear waveform when stopped
      if (canvasRef.current) {
        const ctx = canvasRef.current.getContext("2d");
        if (ctx) ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
      }
    }
  }, [playing, drawWaveform]); // Added drawWaveform to dependencies

  useEffect(() => {
    return () => {
      if (animationIdRef.current) cancelAnimationFrame(animationIdRef.current);
      if (audioCtxRef.current) {
        try {
          audioCtxRef.current.close();
        } catch (e) {
          console.error("Error closing audio context on unmount:", e);
        }
      }
      if (sourceNodeRef.current) {
        try {
          sourceNodeRef.current.disconnect();
        } catch (e) {
          console.error("Error disconnecting source node on unmount:", e);
        }
      }
    };
  }, []);

  useEffect(() => {
    if (audioCtxRef.current) {
      try {
        analyserRef.current?.disconnect();
        sourceNodeRef.current?.disconnect();
        audioCtxRef.current.close();
      } catch (e) {
        console.error("Error disconnecting audio nodes or closing context on src change:", e);
      }
      audioCtxRef.current = null;
      analyserRef.current = null;
      sourceNodeRef.current = null;
    }
    setPlaying(false);
    setCurrent(0);
    setDuration(0);
    // The setupAnalyser will now be called in onLoadedMetadata when the new src loads.
  }, [src]);

  const proxySrc = `/api/proxy-audio?url=${encodeURIComponent(src)}`;

  return (
    <div
      className="flex bg-[#121212] items-center rounded-full border border-[#887DFF] border-[0.5px]"
      style={{
        height: 76,
        width: "100%",
        padding: "0 16px",
        gap: 12,
        boxSizing: "border-box",
        minWidth: 0,
        boxShadow: "0 1px 4px 0 #00000022"
      }}
    >
      {/* Left animated ring */}
      <div className="relative flex items-center justify-center" style={{ width: 36, height: 36 }}>
        <span
          className={`absolute w-9 h-9 rounded-full border-2 transition-colors duration-300`}
          style={{
            borderColor: playing ? "#887dff" : "#00c8ff",
            left: 0,
            top: 0
          }}
        />
        <span
          className="block w-3 h-3 rounded-full z-10"
          style={{
            background: playing ? "#887dff" : "#00c8ff",
            border: "2px solid #23233a",
            boxShadow: playing ? "0 0 8px #887dff" : "0 0 8px #00c8ff",
            transition: "background 0.3s"
          }}
        />
      </div>
      {/* Time */}
      <span
        className="text-xs text-white opacity-80"
        style={{
          minWidth: 48,
          textAlign: "right",
          fontVariantNumeric: "tabular-nums",
          fontFamily: "inherit"
        }}
      >
        {formatTime(current)}
      </span>
      {/* Waveform */}
      <div className="flex-1 flex items-center mx-2 relative" style={{ minWidth: 0 }}>
        <canvas
          ref={canvasRef}
          className="w-full"
          height={32}
          style={{
            width: "120%",
            height:40,
            borderRadius: 4,
            // background: "#181825",
            cursor: "pointer"
          }}
          onClick={onSeek}
        />
      </div>
      {/* Duration */}
      <span
        className="text-xs text-white opacity-80"
        style={{
          minWidth: 60,
          textAlign: "left",
          fontVariantNumeric: "tabular-nums",
          fontFamily: "inherit"
        }}
      >
        {formatTime(duration)}
      </span>
      {/* Play/Pause */}
      <button
        onClick={togglePlay}
        className="flex items-center justify-center rounded-full"
        style={{
          width: 40,
          height: 40,
          background: "#887dff",
          boxShadow: playing ? "0 0 0 2px #887dff55" : undefined,
          transition: "box-shadow 0.2s"
        }}
      >
        {playing ? (
          <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
            <rect x="6" y="4" width="4" height="16" rx="2" fill="#fff" />
            <rect x="14" y="4" width="4" height="16" rx="2" fill="#fff" />
          </svg>
        ) : (
          <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" fill="#887dff" opacity="0.15" />
            <polygon points="8,6 20,12 8,18" fill="#fff" />
          </svg>
        )}
      </button>
      {/* Download */}
      <button
        className="ml-2 flex items-center justify-center opacity-80 hover:opacity-100"
        style={{ width: 28, height: 28 }}
        title="Download"
        onClick={handleDownload}
      >
        <DownloadIconaudio/>
      </button>
      {/* Share */}
      <button
        className="ml-1 flex items-center justify-center opacity-80 hover:opacity-100"
        style={{ width: 28, height: 28 }}
        title="Share"
        onClick={handleShare}
      >
        <ShareIcon/>
      </button>
      <audio
        ref={audioRef}
        src={proxySrc}
        onTimeUpdate={onTimeUpdate}
        onLoadedMetadata={onLoadedMetadata}
        onEnded={() => {
          console.log("Audio ended");
          setPlaying(false);
        }}
        onError={(e) => console.error("Audio Element Error:", e)}
        crossOrigin="anonymous"
        style={{ display: "none" }}
      />
    </div>
  );
};

export default AudioPlayerMusicGen;
