import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/auth.config";
import { connectToDatabase } from "../../../../lib/mongodb";
import {
  CreateProjectBody,
  CreateProjectResponse,
  inferVideoTypeFromUrl,
  mapUiDimensionsToRatio,
  mapUiDurationToPreferLength,
  mapUiLanguageToVizard,
  vizardCreateProject,
} from "../../../../lib/vizard";

function badRequest(message: string) {
  return NextResponse.json({ error: message }, { status: 400 });
}

export async function POST(req: NextRequest) {
  const reqId = `api-create-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
  const started = Date.now();

  // Auth required
  const session = await getServerSession(authOptions);
  if (!session || !session.user || !session.user.id) {
    console.warn(`[API][CREATE_PROJECT][${reqId}] Unauthorized request or missing user ID in session`);
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  let payload: any;
  try {
    payload = await req.json();
  } catch {
    console.error(`[API][CREATE_PROJECT][${reqId}] Invalid JSON body`);
    return badRequest("Invalid JSON body");
  }

  console.log(`[API][CREATE_PROJECT][${reqId}] Payload=`, {
    hasVideoUrl: !!payload?.videoUrl,
    langLabel: payload?.langLabel,
    autoDuration: !!payload?.autoDuration,
    durationLabel: payload?.durationLabel,
    dimensionsLabel: payload?.dimensionsLabel,
    hook: !!payload?.hook,
    captions: !!payload?.captions,
    emojis: !!payload?.emojis,
    removeSilence: !!payload?.removeSilence,
    maxClipNumber: payload?.maxClipNumber,
    hasKeywords: typeof payload?.keywords === "string" ? payload?.keywords.length > 0 : false,
    hasProjectName: typeof payload?.projectName === "string",
    templateId: payload?.templateId,
    highlight: !!payload?.highlight,
  });

  const {
    videoUrl,
    langLabel,
    autoDuration,
    durationLabel,
    dimensionsLabel,
    hook,
    captions,
    emojis,
    removeSilence,
    maxClipNumber,
    keywords,
    projectName,
    templateId,
    highlight,
  } = payload || {};

  if (!videoUrl || typeof videoUrl !== "string") {
    console.error(`[API][CREATE_PROJECT][${reqId}] Missing videoUrl`);
    return badRequest("videoUrl is required");
  }

  const { videoType, ext } = inferVideoTypeFromUrl(videoUrl);

  // Map UI fields to Vizard params
  const lang = mapUiLanguageToVizard(String(langLabel || "English"));
  const preferLength = mapUiDurationToPreferLength(
    !!autoDuration,
    String(durationLabel || "")
  );
  const ratioOfClip = mapUiDimensionsToRatio(String(dimensionsLabel || "Portrait"));

  const body: CreateProjectBody = {
    lang,
    preferLength,
    videoUrl,
    videoType,
    ratioOfClip,
    templateId: typeof templateId === "number" ? templateId : undefined,
    removeSilenceSwitch: removeSilence ? 1 : 0,
    maxClipNumber:
      typeof maxClipNumber === "number" && maxClipNumber >= 1 && maxClipNumber <= 100
        ? maxClipNumber
        : undefined,
    keywords: typeof keywords === "string" ? keywords : undefined,
    subtitleSwitch: captions ? 1 : 0,
    headlineSwitch: hook ? 1 : 0,
    projectName: typeof projectName === "string" ? projectName : undefined,
    ext: videoType === 1 ? ext : undefined,
    emojiSwitch: emojis ? 1 : 0,
    highlightSwitch: highlight ? 1 : 0,
  };

  // Validate required for videoType=1
  if (videoType === 1 && !body.ext) {
    console.error(`[API][CREATE_PROJECT][${reqId}] Missing ext for remote file url=${videoUrl}`);
    return badRequest("When submitting a remote file (videoType=1), ext must be one of mp4|mov|avi|3gp (infer from URL).");
  }

  // Log outgoing request body (sanitized)
  const safeBody = {
    ...body,
    keywords: body.keywords ? "[REDACTED]" : undefined,
    projectName: body.projectName ? "[REDACTED]" : undefined,
  };
  console.log(`[API][CREATE_PROJECT][${reqId}] Mapped body=`, safeBody);

  try {
    const resp: CreateProjectResponse & any = await vizardCreateProject(body);
    const duration = Date.now() - started;
    console.log(
      `[API][CREATE_PROJECT][${reqId}] Upstream response code=${resp?.code} msg=${resp?.msg} durationMs=${duration} data=`,
      resp?.data ? { ...resp.data } : null
    );

    // Vizard success code is 2000 per docs
    if (typeof resp?.code !== "number" || resp.code !== 2000) {
      const msg = resp?.msg || resp?.errMsg || "Vizard create project failed";
      console.error(`[API][CREATE_PROJECT][${reqId}] Upstream business error:`, {
        msg,
        code: resp?.code,
        data: resp?.data,
      });
      return NextResponse.json({ error: msg, raw: resp, reqId }, { status: 502 });
    }
    // Handle both possible shapes: top-level projectId or data.projectId
    const projectIdVal = resp?.projectId ?? resp?.data?.projectId;
    const projectId = String(projectIdVal ?? "");
    console.log(`[API][CREATE_PROJECT][${reqId}] Success projectId=${projectId}`);

    // Save project details to MongoDB
    try {
      const { db } = await connectToDatabase();
      const projectsCollection = db.collection('clipping_projects');
      const projectDoc = {
        projectId: projectId,
        userId: session.user.id,
        projectName: projectName,
        createdAt: new Date(),
        // Add other relevant fields from payload if needed, e.g., thumbnailUrl, videoUrl
      };
      await projectsCollection.insertOne(projectDoc);
      console.log(`[API][CREATE_PROJECT][${reqId}] Project ${projectId} saved to MongoDB. Doc:`, projectDoc);
    } catch (dbError) {
      console.error(`[API][CREATE_PROJECT][${reqId}] Failed to save project to MongoDB:`, dbError);
    }

    return NextResponse.json({ projectId, raw: resp, reqId });
  } catch (err: any) {
    const duration = Date.now() - started;
    console.error(
      `[API][CREATE_PROJECT][${reqId}] Exception durationMs=${duration} error=`,
      err?.message || err
    );
    return NextResponse.json(
      { error: err?.message || "Vizard create request error", reqId },
      { status: 500 }
    );
  }
}