// Shared types for dashboard components

export interface ImageData {
  src: string;
  height: string;
}

export interface MenuItem {
  label: string;
  icon: string | React.ReactElement;
  active: boolean;
  submenu?: { label: string; active: boolean }[];
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  images?: string[];
  videos?: string[];
  audioUrl?: string;
  uploadedImage?: string;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  mode: 'text-to-image' | 'image-to-image' | 'text-to-video' | 'image-to-video' | 'text-to-speech' | 'music-generation' | 'sound-effects' | 'lip-sync';
  active?: boolean;
}

export interface HistoryItem {
  text: string;
  sessionId?: string; // Link to specific chat session
  active?: boolean;
}

export interface HistorySection {
  title: string;
  items: HistoryItem[];
  titleColor?: string;
}

export interface ChatHistory {
  sessions: ChatSession[];
  lastActiveSessionId?: string;
}

export interface HeaderProps {
  title?: string;
  subtitle?: string;
  credits?: number;
  hasNotifications?: boolean;
  userAvatar?: string;
}

export interface SidebarProps {
  menuItems?: MenuItem[];
  logoSrc?: string;
  logoAlt?: string;
  logoWidth?: number;
  logoHeight?: number;
  histories?: HistorySection[];
  onMenuItemClick?: (item: MenuItem, index: number) => void;
}

export interface ImageGridProps {
  images: ImageData[];
  onImageClick: (image: ImageData) => void;
  onDownload: (src: string) => void;
  columns?: string;
}

export interface ImageModalProps {
  image: ImageData | null;
  isOpen: boolean;
  onClose: () => void;
  onDownload: (src: string) => void;
  title?: string;
  description?: string;
}

export interface DashboardLayoutProps {
  children: React.ReactNode;
  sidebar: React.ReactNode;
  header: React.ReactNode;
  className?: string;
}

export interface ClippingProject {
  id: string;
  title: string;
  createdAt: string; // ISO date string
  thumbnailUrl?: string; // Optional thumbnail for display
  url?: string;
}
