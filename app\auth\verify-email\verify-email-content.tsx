"use client";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export default function VerifyEmailContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function verify() {
      if (!token) {
        setError("No verification token provided.");
        setLoading(false);
        return;
      }
      const res = await fetch(`/api/auth/verify-email?token=${token}`);
      const data = await res.json();
      if (res.ok) {
        setMessage("Email verified! Redirecting...");
        setTimeout(() => router.push("/"), 2000);
      } else {
        setError(data.error || "Verification failed.");
      }
      setLoading(false);
    }
    verify();
  }, [token, router]);

  if (loading) {
    return <div className="text-[#b3b3c6] text-center">Verifying...</div>;
  }

  if (message) {
    return <div className="text-green-400 text-center">{message}</div>;
  }

  if (error) {
    return <div className="text-red-400 text-center">{error}</div>;
  }

  return null;
} 