"use client";
import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  DashboardLayout,
  Header,
  Sidebar,
  ImageGrid,
  ImageModal,
  HistoryModal
} from '../components';
import AudioPlayer from '../components/AudioPlayer';
import ChatMessagesArea from '../components/ChatMessagesArea';
import ChatInputControls from '../components/ChatInputControls';
import ImageUploadModal from '../components/ImageUploadModal';
import MusicUploadModal from '../components/MusicUploadModal';
import LipSyncModal from '../components/LipSyncModal';
import { useChatHistory } from '../hooks/useChatHistory';
import type { ImageData, ChatMessage } from '../components/types';
import { Accounticon, Clippingicon, IdeaLabicon, Inspirationicon, Keywordicon, SocialPosticon, Storyboardicon, Youtubeicon } from '../components/icons/icons';
import Account from '../account/page';

// Icon components for better consistency
const IconComponents = {
  inspiration: (
    <Inspirationicon />
  ),
  ideaLab: (
    <IdeaLabicon />
  ),
  socialPost: (
    <SocialPosticon />
  ),
  clipping: (
    <Clippingicon />
  ),
  storyboard: (
    <Storyboardicon />
  ),
  youtube: (
    <Youtubeicon />
  ),
  keyword: (
    <Keywordicon />
  ),
  account: (
    <Accounticon />
  )
};

export default function IdeaLab() {
  const [sidebarMenu, setSidebarMenu] = useState([
    { label: "Inspiration", icon: IconComponents.inspiration, active: false },
    {
      label: "Idea Lab",
      icon: IconComponents.ideaLab,
      active: true,
      submenu: [
        { label: "New Idea Lab", active: true },
      ]
    },
    { label: "Social Post", icon: IconComponents.socialPost, active: false },
    { label: "Clipping", icon: IconComponents.clipping, active: false },
    { label: "Storyboard Editor", icon: IconComponents.storyboard, active: false },
    { label: "YouTube Tools", icon: IconComponents.youtube, active: false },
    { label: "Keyword Insights", icon: IconComponents.keyword, active: false },
    { label: "Account", icon: IconComponents.account, active: false },
  ]);
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const musicFileInputRef = useRef<HTMLInputElement>(null);
  const videoFileInputRef = useRef<HTMLInputElement>(null);
  const audioFileInputRef = useRef<HTMLInputElement>(null);
  const imageFileInputRef = useRef<HTMLInputElement>(null);

  // Chat history management
  const {
    currentSession,
    historySections,
    isLoading,
    startNewSession,
    loadSession,
    addMessage,
    deleteSession,
    clearAllHistory
  } = useChatHistory();

  const [prompt, setPrompt] = useState("A futuristic digital workspace with a sleek, ultra-modern interface floating in mid-air, diverse designers collaborating using holographic tools, vibrant neon accents, transparent screens showing wireframes");
  const [selectedModel, setSelectedModel] = useState("flux-dev-lora");
  const [selectedStyle, setSelectedStyle] = useState("Photorealistic");
  const [selectedRatio, setSelectedRatio] = useState("16:9");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationPrompt, setGenerationPrompt] = useState("");
  const [activeMode, setActiveMode] = useState<'text-to-image' | 'image-to-image' | 'text-to-video' | 'image-to-video' | 'text-to-speech' | 'music-generation' | 'sound-effects' | 'lip-sync'>('text-to-image');
  const [showImageModal, setShowImageModal] = useState(false);
  const [modalImage, setModalImage] = useState<string | null>(null);
  const [modalPrompt, setModalPrompt] = useState("");
  const [videoDuration, setVideoDuration] = useState(5);
  const [videoAspectRatio, setVideoAspectRatio] = useState("16:9");
  const [selectedVideoModel, setSelectedVideoModel] = useState("kling-v1.6-standard");
  const [selectedVoice, setSelectedVoice] = useState("af_bella");
  const [speechSpeed, setSpeechSpeed] = useState(1.0);
  const [musicBitrate, setMusicBitrate] = useState(256000);
  const [musicSampleRate, setMusicSampleRate] = useState(44100);
  const [showMusicModal, setShowMusicModal] = useState(false);
  const [musicReferenceFiles, setMusicReferenceFiles] = useState<{ song: string | null; voice: string | null; instrumental: string | null }>({
    song: null,
    voice: null,
    instrumental: null
  });
  const [musicReferenceType, setMusicReferenceType] = useState<'song' | 'voice' | 'instrumental'>('song');
  const [musicModalLyrics, setMusicModalLyrics] = useState("");
  const [soundEffectModel, setSoundEffectModel] = useState("tango2");
  const [soundEffectSteps, setSoundEffectSteps] = useState(100);
  const [soundEffectGuidance, setSoundEffectGuidance] = useState(3);
  const [numberOfOutputs, setNumberOfOutputs] = useState(1);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [showLipSyncModal, setShowLipSyncModal] = useState(false);
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false); // Renamed from isNearBottom
  const lastScrollY = useRef(0);

  useEffect(() => {
    if (historySections) {
      setSidebarMenu(currentMenu => {
        const ideaLabIndex = currentMenu.findIndex(item => item.label === "Idea Lab");
        if (ideaLabIndex === -1) return currentMenu;

        const recentHistories = historySections.flatMap(section => section.items).slice(0, 3).map(item => ({
          label: item.text,
          active: false,
          sessionId: item.sessionId
        }));

        const newSubmenu = [
          { label: "New Idea Lab", active: true },
          ...recentHistories
        ];

        if (JSON.stringify(currentMenu[ideaLabIndex].submenu) === JSON.stringify(newSubmenu)) {
          return currentMenu;
        }

        const newMenuItems = [...currentMenu];
        newMenuItems[ideaLabIndex] = {
          ...newMenuItems[ideaLabIndex],
          submenu: newSubmenu
        };
        return newMenuItems;
      });
    }
  }, [historySections]);

  const handleRewrite = (newPrompt: string) => {
    setPrompt(newPrompt);
  };

  // Initialize session if none exists
  useEffect(() => {
    if (!currentSession) {
      startNewSession(activeMode);
    }
  }, [currentSession, startNewSession, activeMode]);

  // Get current chat messages from session
  const chatMessages = currentSession?.messages || [];

  // Get supported options for the selected video model
  const getModelOptions = (model: string) => {
    const modelConfigs = {
      "kling-v1.6-standard": {
        durations: [5, 10],
        aspectRatios: ["16:9", "9:16", "1:1"]
      },
      "google-veo-3": {
        durations: [5, 10],
        aspectRatios: ["16:9", "9:16", "1:1"]
      },
      "wan-2.1-i2v-480p": {
        durations: [5],
        aspectRatios: ["16:9", "9:16", "1:1"]
      },
      "wan-2.1-i2v-720p": {
        durations: [5],
        aspectRatios: ["16:9", "9:16", "1:1"]
      },
      "wan-2.1-t2v-480p": {
        durations: [5],
        aspectRatios: ["16:9", "9:16", "1:1"]
      },
      "wan-2.1-t2v-720p": {
        durations: [5],
        aspectRatios: ["16:9", "9:16", "1:1"]
      },
      "minimax-video-01": {
        durations: [6],
        aspectRatios: ["16:9", "9:16", "1:1"]
      }
    };
    return modelConfigs[model as keyof typeof modelConfigs] || modelConfigs["kling-v1.6-standard"];
  };

  // Progress simulation function with different timing based on media type
  const simulateProgress = (mediaType?: 'image' | 'video' | 'audio' | 'lip-sync') => {
    setGenerationProgress(0);

    // Define timing based on media type
    let totalDuration: number;
    let updateInterval: number;

    switch (mediaType) {
      case 'video':
        totalDuration = 240000; // 4 minutes (240 seconds)
        updateInterval = 1000; // Update every second
        break;
      case 'lip-sync':
        totalDuration = 660000; // 11 minutes (660 seconds) for lip sync
        updateInterval = 2000; // Update every 2 seconds
        break;
      case 'image':
        totalDuration = 30000; // 30 seconds
        updateInterval = 300; // Update every 300ms
        break;
      case 'audio':
        totalDuration = 20000; // 20 seconds
        updateInterval = 200; // Update every 200ms
        break;
      default:
        // Fallback to image timing
        totalDuration = 30000;
        updateInterval = 300;
        break;
    }

    const totalSteps = Math.floor(totalDuration / updateInterval);
    const incrementPerStep = 90 / totalSteps; // Progress to 90%

    const interval = setInterval(() => {
      setGenerationProgress(prev => {
        if (prev >= 90) {
          clearInterval(interval);
          return 90; // Stop at 90% until actual completion
        }
        return Math.min(prev + incrementPerStep + (Math.random() * 2 - 1), 90); // Small random variation
      });
    }, updateInterval);

    return interval;
  };

  // Complete progress when generation finishes
  const completeProgress = () => {
    setGenerationProgress(100);
  };

  // Upload file to server and return URL
  const uploadFileToServer = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to upload file');
    }

    const data = await response.json();
    return data.url;
  };

  // Handle menu item clicks with navigation
  const handleMenuItemClick = (item: any) => {
    // Handle submenu clicks for Idea Lab
    if (item.label === "New Idea Lab") {
      startNewSession(activeMode);
      return;
    }

    if (item.sessionId) {
      loadSession(item.sessionId);
      return;
    }

    const routes: { [key: string]: string } = {
      "Inspiration": "/dashboard",
      "Idea Lab": "/idealab",
      "Social Post": "/social-post",
      "Clipping": "/clipping",
      "Storyboard Editor": "/storyboard",
      "YouTube Tools": "/youtube-tools",
      "Keyword Insights": "/keyword-insights",
      "Account": "/account"
    };

    const route = routes[item.label];
    if (route) {
      router.push(route);
    }
  };

  // Handle history item click to load previous chat session
  const handleHistoryItemClick = (historyItem: any, sectionIndex: number, itemIndex: number) => {
    if (historyItem.sessionId) {
      loadSession(historyItem.sessionId);
      // The session will be loaded and currentSession will be updated
      // We can check the mode after the session is loaded
    }
  };

  // Update active mode when current session changes
  useEffect(() => {
    if (currentSession && currentSession.mode) {
      setActiveMode(currentSession.mode);
    }
  }, [currentSession]);

  // Track scroll direction to expand/collapse the chat bar with smooth transitions
  useEffect(() => {
    let scrollTimeout: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const isScrollingUp = currentScrollY < lastScrollY.current;
      const isAtBottom = window.innerHeight + currentScrollY >= document.body.offsetHeight - 50; // Increased threshold
      const isAtTop = currentScrollY <= 10; // Small threshold for top

      // Clear any existing timeout
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      // Always expanded at the bottom or top
      if (isAtBottom || isAtTop) {
        setIsCollapsed(false);
      } else {
        // Add a small delay to prevent rapid state changes during scrolling
        scrollTimeout = setTimeout(() => {
          if (isScrollingUp && currentScrollY > 10) {
            setIsCollapsed(true); // Collapse when scrolling up and not at top
          } else if (!isScrollingUp && currentScrollY > 10) {
            setIsCollapsed(false); // Expand when scrolling down
          }
        }, 100); // 100ms delay for smoother transitions
      }

      lastScrollY.current = currentScrollY;
    };

    window.addEventListener('scroll', handleScroll, { passive: true } as any);
    handleScroll(); // Initial check

    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, []);

  // Handle clear history
  const handleClearHistory = () => {
    clearAllHistory();
    // A new session will be created automatically by the hook
  };

  // Handle image upload for modal
  const handleModalImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setModalImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle music file upload for modal
  const handleMusicFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file format
      const allowedFormats = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/wave'];
      const fileExtension = file.name.toLowerCase().split('.').pop();
      const allowedExtensions = ['mp3', 'wav'];

      if (!allowedFormats.includes(file.type) && !allowedExtensions.includes(fileExtension || '')) {
        alert('Please upload a MP3 or WAV file only. Other audio formats are not supported.');
        return;
      }

      // Check file size (reasonable limit for audio files)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        alert('File size too large. Please upload a file smaller than 50MB.');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setMusicReferenceFiles({
          ...musicReferenceFiles,
          [musicReferenceType]: e.target?.result as string
        });
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle mode change
  const handleModeChange = (mode: 'text-to-image' | 'image-to-image' | 'text-to-video' | 'image-to-video' | 'text-to-speech' | 'music-generation' | 'sound-effects' | 'lip-sync') => {
    setActiveMode(mode);
    // Don't create a new session - keep the current chat
  };

  // Handle video model change
  const handleVideoModelChange = (model: string) => {
    setSelectedVideoModel(model);
    const options = getModelOptions(model);
    // Update duration and aspect ratio to valid values for the new model
    if (!options.durations.includes(videoDuration)) {
      setVideoDuration(options.durations[0]);
    }
    if (!options.aspectRatios.includes(videoAspectRatio)) {
      setVideoAspectRatio(options.aspectRatios[0]);
    }
  };

  // Handle plus button click in chat
  const handlePlusButtonClick = () => {
    if (activeMode === 'music-generation') {
      setShowMusicModal(true);
    } else if (activeMode === 'lip-sync') {
      setShowLipSyncModal(true);
    }
    // Note: image-to-image and image-to-video now handle upload directly in the input area
  };

  // Handle video file upload for lip sync
  const handleVideoFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file format
      const allowedFormats = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
      const fileExtension = file.name.toLowerCase().split('.').pop();
      const allowedExtensions = ['mp4', 'avi', 'mov', 'wmv'];

      if (!allowedFormats.includes(file.type) && !allowedExtensions.includes(fileExtension || '')) {
        alert('Please upload a MP4, AVI, MOV, or WMV file only. Other video formats are not supported.');
        return;
      }

      // Check file size (reasonable limit for video files)
      const maxSize = 100 * 1024 * 1024; // 100MB
      if (file.size > maxSize) {
        alert('File size too large. Please upload a file smaller than 100MB.');
        return;
      }

      setVideoFile(file);
    }
  };

  // Handle audio file upload for lip sync
  const handleAudioFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/wave'];
      if (!allowedTypes.includes(file.type)) {
        alert('Please upload a valid audio file (MP3 or WAV)');
        return;
      }
      // Validate file size (50MB max)
      if (file.size > 50 * 1024 * 1024) {
        alert('Audio file size must be less than 50MB');
        return;
      }
      setAudioFile(file);
    }
  };

  // Handle image file upload for Sonic model
  const handleImageFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        alert('Please upload a valid image file (JPEG, PNG, or WebP)');
        return;
      }
      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        alert('Image file size must be less than 10MB');
        return;
      }
      setImageFile(file);
    }
  };

  // Handle lip sync generation
  const handleLipSyncGenerate = async (params: {
    videoFile: File | null;
    audioFile: File | null;
    imageFile: File | null;
    modelType: 'kling' | 'sonic';
    keepResolution: boolean;
  }) => {
    setActiveMode('lip-sync');
    setIsGenerating(true);
    setGenerationPrompt(`Generating ${params.modelType === 'kling' ? 'lip sync' : 'talking face'} video...`);
    simulateProgress('lip-sync');

    try {
      console.log('Uploading files for lip sync generation...');

      let videoUrl, audioUrl, imageUrl;

      if (params.modelType === 'kling') {
        // Upload video and audio for Kling model
        [videoUrl, audioUrl] = await Promise.all([
          uploadFileToServer(params.videoFile!),
          uploadFileToServer(params.audioFile!)
        ]);
      } else {
        // Upload image and audio for Sonic model
        [imageUrl, audioUrl] = await Promise.all([
          uploadFileToServer(params.imageFile!),
          uploadFileToServer(params.audioFile!)
        ]);
      }

      console.log('Files uploaded successfully:', { videoUrl, audioUrl, imageUrl });

      // Call the lip sync API
      const response = await fetch('/api/generate-lipsync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          video_url: videoUrl,
          audio_file: audioUrl,
          image_url: imageUrl,
          model_type: params.modelType,
          keep_resolution: params.keepResolution
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      if (data.success && data.output) {
        console.log(`${params.modelType} generation successful:`, data.output);
        completeProgress();
        // Add AI response with generated lip sync video
        const aiMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai' as const,
          content: `${params.modelType === 'kling' ? 'Lip sync' : 'Talking face'} video generated successfully`,
          timestamp: new Date(),
          videos: [data.output],
          mode: 'lip-sync'
        };
        addMessage(aiMessage);
      } else if (data.suggestion === 'kling_fallback') {
        // Sonic failed, suggest switching to Kling model
        const errorMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai' as const,
          content: 'Sonic model is currently experiencing issues. Please try the Kling model instead, which is more reliable for lip sync generation.',
          timestamp: new Date()
        };
        addMessage(errorMessage);
      } else {
        throw new Error(data.error || 'No output URL received from the API');
      }
    } catch (err: any) {
      console.error('Lip sync generation error:', err);

      // Provide user-friendly error messages
      let errorMessage = 'An error occurred while generating lip sync';

      if (err.message.includes('API token not configured')) {
        errorMessage = 'Lip sync service is not configured. Please contact support.';
      } else if (err.message.includes('Invalid Replicate API token')) {
        errorMessage = 'Lip sync service authentication failed. Please contact support.';
      } else if (err.message.includes('quota exceeded')) {
        errorMessage = 'Lip sync service quota exceeded. Please try again later.';
      } else if (err.message.includes('Failed to upload file')) {
        errorMessage = 'Failed to upload files. Please try again.';
      } else if (err.message.includes('network') || err.message.includes('fetch')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (err.message.includes('output file not found') || err.message.includes('No such file or directory')) {
        if (params.modelType === 'sonic') {
          errorMessage = 'Sonic model processing failed. This may be due to image quality or format issues. Try using a different high-quality portrait image, or switch to the Kling model for more reliable results.';
        } else {
          errorMessage = 'Model processing failed. Please try with different files.';
        }
      } else if (err.message.includes('memory constraints')) {
        errorMessage = 'File too large for processing. Please try with smaller files.';
      } else if (err.message.includes('timeout')) {
        errorMessage = 'Processing timed out. Please try with smaller files.';
      } else {
        errorMessage = err.message || errorMessage;
      }

      // Add error message to chat
      const errorMsg = {
        id: (Date.now() + 1).toString(),
        type: 'ai' as const,
        content: `Error: ${errorMessage}`,
        timestamp: new Date()
      };
      addMessage(errorMsg);
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle music modal submit
  const handleMusicModalSubmit = async () => {
    // Check if at least one file is uploaded
    const hasAnyFile = Object.values(musicReferenceFiles).some(file => file !== null);

    if (hasAnyFile) {
      setIsGenerating(true);
      setGenerationPrompt(musicModalLyrics || "Generating music with reference file...");
      simulateProgress('audio');

      // Close modal and reset
      setShowMusicModal(false);
      const lyricsToProcess = musicModalLyrics;
      setMusicModalLyrics("");

      try {
        // Prepare request data with all uploaded files
        const requestData: any = {
          lyrics: lyricsToProcess,
          bitrate: musicBitrate,
          sample_rate: musicSampleRate
        };

        // Add all uploaded files to the request
        if (musicReferenceFiles.song) {
          requestData.song_file = musicReferenceFiles.song;
        }
        if (musicReferenceFiles.voice) {
          requestData.voice_file = musicReferenceFiles.voice;
        }
        if (musicReferenceFiles.instrumental) {
          requestData.instrumental_file = musicReferenceFiles.instrumental;
        }

        console.log('Sending music generation request with files:', {
          hasSong: !!musicReferenceFiles.song,
          hasVoice: !!musicReferenceFiles.voice,
          hasInstrumental: !!musicReferenceFiles.instrumental
        });

        // Call the API to generate music with reference
        const response = await fetch('/api/generate-music', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        const data = await response.json();
        console.log('Music generation API response:', data);

        if (data.success && data.audioUrl) {
          console.log('Generated music:', data.audioUrl);
          completeProgress();
          // Add AI response with generated music
          const aiMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: generationPrompt,
            timestamp: new Date(),
            audioUrl: data.audioUrl,
            mode: 'music-generation' // Add mode property for music generation
          };
          addMessage(aiMessage);
        } else {
          console.error('API error or no music:', data);
          // Add error message
          const errorMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: `Error: ${data.error || 'Failed to generate music or no audio returned'}`,
            timestamp: new Date()
          };
          addMessage(errorMessage);
        }
      } catch (error) {
        console.error('Music generation error:', error);
        // Add error message to chat
        const errorMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai' as const,
          content: `Error: Failed to connect to music generation service.`,
          timestamp: new Date()
        };
        addMessage(errorMessage);
      } finally {
        setIsGenerating(false);
      }
    }
  };

  // Handle modal submit
  const handleModalSubmit = async () => {
    if (modalImage && modalPrompt.trim()) {
      setIsGenerating(true);
      setGenerationPrompt(modalPrompt);
      // Determine media type based on active mode
      const mediaType = activeMode === 'image-to-video' ? 'video' : 'image';
      simulateProgress(mediaType);

      // Close modal and reset
      setShowImageModal(false);
      const imageToProcess = modalImage;
      const promptToProcess = modalPrompt;
      setModalImage(null);
      setModalPrompt("");

      try {
        if (activeMode === 'image-to-image') {
          // Call the API to generate image-to-image
          const response = await fetch('/api/generate-image', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              prompt: promptToProcess,
              model: selectedModel,
              style: selectedStyle,
              ratio: selectedRatio,
              mode: 'image-to-image',
              imageUrl: imageToProcess || null,
              num_outputs: numberOfOutputs
            }),
          });

          const data = await response.json();
          console.log('Image-to-image API response:', data);

          if (data.success && data.images && data.images.length > 0) {
            console.log('Generated images:', data.images);
            completeProgress();
            // Add AI response with generated image
            const aiMessage = {
              id: (Date.now() + 1).toString(),
              type: 'ai' as const,
              content: prompt,
              timestamp: new Date(),
              images: data.images
            };
            addMessage(aiMessage);
          } else {
            console.error('API error or no images:', data);
            // Add error message
            const errorMessage = {
              id: (Date.now() + 1).toString(),
              type: 'ai' as const,
              content: `Error: ${data.error || 'Failed to generate image or no images returned'}`,
              timestamp: new Date()
            };
            addMessage(errorMessage);
          }
        } else if (activeMode === 'image-to-video') {
          // Call the API to generate image-to-video
          const response = await fetch('/api/generate-video', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              prompt: promptToProcess,
              mode: 'image-to-video',
              duration: videoDuration,
              aspectRatio: videoAspectRatio,
              model: selectedVideoModel,
              imageUrl: imageToProcess || null
            }),
          });

          const data = await response.json();
          console.log('Image-to-video API response:', data);

          if (data.success && data.videos && data.videos.length > 0) {
            console.log('Generated videos:', data.videos);
            completeProgress();
            // Add AI response with generated video
            const aiMessage = {
              id: (Date.now() + 1).toString(),
              type: 'ai' as const,
              content: prompt,
              timestamp: new Date(),
              videos: data.videos
            };
            addMessage(aiMessage);
          } else {
            console.error('API error or no videos:', data);
            // Add error message
            const errorMessage = {
              id: (Date.now() + 1).toString(),
              type: 'ai' as const,
              content: `Error: ${data.error || 'Failed to generate video or no videos returned'}`,
              timestamp: new Date()
            };
            addMessage(errorMessage);
          }
        }
      } catch (error) {
        console.error('Generation error:', error);
        // Add error message to chat
        const errorMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai' as const,
          content: `Error: Failed to connect to ${activeMode === 'image-to-image' ? 'image' : 'video'} generation service.`,
          timestamp: new Date()
        };
        addMessage(errorMessage);
      } finally {
        setIsGenerating(false);
      }
    }
  };

  // Handle generate with chat functionality
  const handleGenerate = async (image?: string) => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    setGenerationPrompt(prompt);

    // Start progress simulation based on active mode
    let mediaType: 'image' | 'video' | 'audio';
    if (activeMode === 'text-to-image' || activeMode === 'image-to-image') {
      mediaType = 'image';
    } else if (activeMode === 'text-to-video' || activeMode === 'image-to-video') {
      mediaType = 'video';
    } else if (activeMode === 'text-to-speech' || activeMode === 'music-generation' || activeMode === 'sound-effects') {
      mediaType = 'audio';
    } else {
      mediaType = 'image'; // fallback
    }
    simulateProgress(mediaType);

    // Add user message to chat
    const userMessage = {
      id: Date.now().toString(),
      type: 'user' as const,
      content: prompt,
      timestamp: new Date()
    };
    addMessage(userMessage);

    try {
      if (activeMode === 'text-to-image') {
        // Call the API to generate image
        const response = await fetch('/api/generate-image', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt,
            model: selectedModel,
            style: selectedStyle,
            ratio: selectedRatio,
            mode: 'text-to-image',
            imageUrl: image || null,
            num_outputs: numberOfOutputs
          }),
        });

        const data = await response.json();
        console.log('Text-to-image API response:', data);

        // Support both `images` array or `image_url` (single or array) from API
        const retrievedImages: string[] = Array.isArray(data.images)
          ? data.images
          : data.image_url
            ? (Array.isArray(data.image_url) ? data.image_url : [data.image_url])
            : [];

        if (data.success && retrievedImages.length > 0) {
          console.log('Generated images:', data.images);
          completeProgress();

          const aiMessage: ChatMessage = {
            id: `ai-${Date.now()}`,
            type: 'ai',
            content: prompt,
            timestamp: new Date(),
            images: retrievedImages,
          };
          addMessage(aiMessage);
        } else {
          console.error('API error or no images:', data);
          // Add error message
          const errorMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: `Error: ${data.error || 'Failed to generate image or no images returned'}`,
            timestamp: new Date()
          };
          addMessage(errorMessage);
        }
      } else if (activeMode === 'image-to-image') {
        // For image-to-image, require an uploaded image
        if (!image) {
          const errorMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: `Error: Image-to-image generation requires an uploaded image. Please upload an image first.`,
            timestamp: new Date()
          };
          addMessage(errorMessage);
          setIsGenerating(false);
          return;
        }

        // Call the API to generate image-to-image
        const response = await fetch('/api/generate-image', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt,
            model: selectedModel,
            style: selectedStyle,
            ratio: selectedRatio,
            mode: 'image-to-image',
            imageUrl: image,
            num_outputs: numberOfOutputs
          }),
        });

        const data = await response.json();
        console.log('Image-to-image API response:', data);

        if (data.success && data.images && data.images.length > 0) {
          console.log('Generated images:', data.images);
          completeProgress();
          // Add AI response with generated image
          const aiMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: prompt,
            timestamp: new Date(),
            images: data.images
          };
          addMessage(aiMessage);
          // Clear the uploaded image after successful generation
          setUploadedImage(null);
        } else {
          console.error('API error or no images:', data);
          // Add error message
          const errorMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: `Error: ${data.error || 'Failed to generate image or no images returned'}`,
            timestamp: new Date()
          };
          addMessage(errorMessage);
        }
      } else if (activeMode === 'text-to-video') {
        // Call the API to generate video
        const response = await fetch('/api/generate-video', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt,
            mode: 'text-to-video',
            duration: videoDuration,
            aspectRatio: videoAspectRatio,
            model: selectedVideoModel,
            imageUrl: image || null
          }),
        });

        const data = await response.json();
        console.log('Text-to-video API response:', data);

        if (data.success && data.videos && data.videos.length > 0) {
          console.log('Generated videos:', data.videos);
          completeProgress();
          // Add AI response with generated video
          const aiMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: prompt,
            timestamp: new Date(),
            videos: data.videos
          };
          addMessage(aiMessage);
        } else {
          console.error('API error or no videos:', data);
          // Add error message
          const errorMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: `Error: ${data.error || 'Failed to generate video or no videos returned'}`,
            timestamp: new Date()
          };
          addMessage(errorMessage);
        }
      } else if (activeMode === 'image-to-video') {
        // For image-to-video, require an uploaded image
        if (!image) {
          const errorMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: `Error: Image-to-video generation requires an uploaded image. Please upload an image first.`,
            timestamp: new Date()
          };
          addMessage(errorMessage);
          setIsGenerating(false);
          return;
        }

        // Call the API to generate image-to-video
        const response = await fetch('/api/generate-video', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt,
            mode: 'image-to-video',
            duration: videoDuration,
            aspectRatio: videoAspectRatio,
            model: selectedVideoModel,
            imageUrl: image
          }),
        });

        const data = await response.json();
        console.log('Image-to-video API response:', data);

        if (data.success && data.videos && data.videos.length > 0) {
          console.log('Generated videos:', data.videos);
          completeProgress();
          // Add AI response with generated video
          const aiMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: prompt,
            timestamp: new Date(),
            videos: data.videos
          };
          addMessage(aiMessage);
          // Clear the uploaded image after successful generation
          setUploadedImage(null);
        } else {
          console.error('API error or no videos:', data);
          // Add error message
          const errorMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: `Error: ${data.error || 'Failed to generate video or no videos returned'}`,
            timestamp: new Date()
          };
          addMessage(errorMessage);
        }
      } else if (activeMode === 'text-to-speech') {
        // Call the API to generate speech
        const response = await fetch('/api/generate-speech', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            text: prompt,
            voice: selectedVoice,
            speed: speechSpeed
          }),
        });

        const data = await response.json();
        console.log('Text-to-speech API response:', data);

        if (data.success && data.audioUrl) {
          console.log('Generated audio:', data.audioUrl);
          completeProgress();
          // Add AI response with generated audio
          const aiMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: prompt,
            timestamp: new Date(),
            audioUrl: data.audioUrl,
            mode: 'text-to-speech' // Add mode property for text-to-speech
          };
          addMessage(aiMessage);
        } else {
          console.error('API error or no audio:', data);
          // Add error message
          const errorMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: `Error: ${data.error || 'Failed to generate speech or no audio returned'}`,
            timestamp: new Date()
          };
          addMessage(errorMessage);
        }
      } else if (activeMode === 'music-generation') {
        // For music generation without modal, show error message asking for reference
        const errorMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai' as const,
          content: `Error: Music generation requires a reference file (song, voice, or instrumental). Please click the + button to upload a reference file.`,
          timestamp: new Date()
        };
        addMessage(errorMessage);
      } else if (activeMode === 'sound-effects') {
        // Call the API to generate sound effects
        const response = await fetch('/api/generate-sound-effects', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt,
            model: soundEffectModel,
            steps: soundEffectSteps,
            guidance: soundEffectGuidance
          }),
        });

        const data = await response.json();
        console.log('Sound effects API response:', data);

        if (data.success && data.audioUrl) {
          console.log('Generated sound effect:', data.audioUrl);
          completeProgress();
          // Add AI response with generated sound effect
          const aiMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: prompt,
            timestamp: new Date(),
            audioUrl: data.audioUrl,
            mode: 'sound-effects' // Add mode property for sound-effects
          };
          addMessage(aiMessage);
        } else {
          console.error('API error or no audio:', data);
          // Add error message
          const errorMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai' as const,
            content: `Error: ${data.error || 'Failed to generate sound effect or no audio returned'}`,
            timestamp: new Date()
          };
          addMessage(errorMessage);
        }
      }
    } catch (error) {
      console.error('Generation error:', error);
      // Add error message to chat
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai' as const,
        content: `Error: Failed to connect to ${activeMode === 'text-to-image' ? 'image' : activeMode === 'text-to-speech' ? 'speech' : activeMode === 'music-generation' ? 'music' : activeMode === 'sound-effects' ? 'sound effects' : 'video'} generation service.`,
        timestamp: new Date()
      };
      addMessage(errorMessage);
    } finally {
      setIsGenerating(false);
      setPrompt(""); // Clear prompt after generation
      // Note: uploadedImage is only cleared after successful image-to-video generation
    }
  };



  return (
    <DashboardLayout
      header={<Header />}
      sidebar={
        <Sidebar
          menuItems={sidebarMenu}
          histories={historySections}
          isLoading={isLoading}
          onMenuItemClick={handleMenuItemClick}
          onHistoryItemClick={handleHistoryItemClick}
          onClearHistory={handleClearHistory}
          onSeeMoreClick={() => setIsHistoryModalOpen(true)}
        />
      }
    >
      <div className="flex-1 flex flex-col relative">
        {/* Chat Messages Area */}
        <div className={`flex-1 transition-all duration-300 ease-in-out  ${isCollapsed ? 'pb-20' : 'pb-56'}`}>
          <ChatMessagesArea
            chatMessages={chatMessages}
            isGenerating={isGenerating}
            generationProgress={generationProgress}
            generationPrompt={generationPrompt}
            activeMode={activeMode}
            onRewrite={handleRewrite}
          />
        </div>

        {/* Sticky Chat Input at Bottom */}
        <div
          className={`fixed bottom-0 right-0 z-40 chatbox-transition left-0 md:left-[250px] lg:left-[280px] xl:left-[300px] 2xl:left-[320px] ${isCollapsed
            ? 'bg-transparent border-none shadow-none'
            : 'border-none shadow-lg'
            }`}
          style={{
            backgroundColor: isCollapsed ? 'transparent' : 'rgb(10, 15, 13)',
            paddingBottom: 'env(safe-area-inset-bottom)',
            backdropFilter: isCollapsed ? 'none' : 'blur(8px)',
            WebkitBackdropFilter: isCollapsed ? 'none' : 'blur(8px)'
          }}
        >
          <div className={`max-w-5xl mx-auto transition-all duration-300 ease-in-out ${isCollapsed ? 'px-3 py-2' : 'px-4 py-3'
            }`}>
            <ChatInputControls
              prompt={prompt}
              setPrompt={setPrompt}
              selectedModel={selectedModel}
              setSelectedModel={setSelectedModel}
              selectedStyle={selectedStyle}
              setSelectedStyle={setSelectedStyle}
              selectedRatio={selectedRatio}
              setSelectedRatio={setSelectedRatio}
              isGenerating={isGenerating}
              handleGenerate={handleGenerate}
              activeMode={activeMode}
              handleModeChange={handleModeChange}
              videoDuration={videoDuration}
              setVideoDuration={setVideoDuration}
              videoAspectRatio={videoAspectRatio}
              setVideoAspectRatio={setVideoAspectRatio}
              selectedVideoModel={selectedVideoModel}
              handleVideoModelChange={handleVideoModelChange}
              getModelOptions={getModelOptions}
              handlePlusButtonClick={handlePlusButtonClick}
              selectedVoice={selectedVoice}
              setSelectedVoice={setSelectedVoice}
              speechSpeed={speechSpeed}
              setSpeechSpeed={setSpeechSpeed}
              musicBitrate={musicBitrate}
              setMusicBitrate={setMusicBitrate}
              musicSampleRate={musicSampleRate}
              setMusicSampleRate={setMusicSampleRate}
              soundEffectModel={soundEffectModel}
              setSoundEffectModel={setSoundEffectModel}
              soundEffectSteps={soundEffectSteps}
              setSoundEffectSteps={setSoundEffectSteps}
              soundEffectGuidance={soundEffectGuidance}
              setSoundEffectGuidance={setSoundEffectGuidance}
              numberOfOutputs={numberOfOutputs}
              setNumberOfOutputs={setNumberOfOutputs}
              uploadedImage={uploadedImage}
              setUploadedImage={setUploadedImage}
              collapsed={isCollapsed}
            />
          </div>
        </div>
        <ImageUploadModal
          show={showImageModal}
          onClose={() => setShowImageModal(false)}
          modalImage={modalImage}
          setModalImage={setModalImage}
          modalPrompt={modalPrompt}
          setModalPrompt={setModalPrompt}
          handleModalSubmit={handleModalSubmit}
          fileInputRef={fileInputRef}
          activeMode={activeMode}
          videoDuration={videoDuration}
          setVideoDuration={setVideoDuration}
          videoAspectRatio={videoAspectRatio}
          setVideoAspectRatio={setVideoAspectRatio}
          selectedVideoModel={selectedVideoModel}
          getModelOptions={getModelOptions}
          handleModalImageUpload={handleModalImageUpload}
        />
        <MusicUploadModal
          show={showMusicModal}
          onClose={() => setShowMusicModal(false)}
          musicReferenceFiles={musicReferenceFiles}
          setMusicReferenceFiles={setMusicReferenceFiles}
          musicReferenceType={musicReferenceType}
          setMusicReferenceType={setMusicReferenceType}
          musicModalLyrics={musicModalLyrics}
          setMusicModalLyrics={setMusicModalLyrics}
          handleMusicModalSubmit={handleMusicModalSubmit}
          musicFileInputRef={musicFileInputRef}
          handleMusicFileUpload={handleMusicFileUpload}
          musicBitrate={musicBitrate}
          setMusicBitrate={setMusicBitrate}
          musicSampleRate={musicSampleRate}
          setMusicSampleRate={setMusicSampleRate}
        />
        <LipSyncModal
          show={showLipSyncModal}
          onClose={() => setShowLipSyncModal(false)}
          videoFile={videoFile}
          setVideoFile={setVideoFile}
          audioFile={audioFile}
          setAudioFile={setAudioFile}
          imageFile={imageFile}
          setImageFile={setImageFile}
          onGenerate={handleLipSyncGenerate}
          videoFileInputRef={videoFileInputRef}
          audioFileInputRef={audioFileInputRef}
          imageFileInputRef={imageFileInputRef}
          handleVideoFileUpload={handleVideoFileUpload}
          handleAudioFileUpload={handleAudioFileUpload}
          handleImageFileUpload={handleImageFileUpload}
        />
      </div>
      <HistoryModal
        open={isHistoryModalOpen}
        onClose={() => setIsHistoryModalOpen(false)}
        histories={historySections}
        onHistoryItemClick={handleHistoryItemClick}
      />
    </DashboardLayout>
  );
}
