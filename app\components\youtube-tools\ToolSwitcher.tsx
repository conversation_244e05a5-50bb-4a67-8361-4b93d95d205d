"use client";

import React from 'react';

interface Tool {
  name: string;
  icon: React.ReactNode;
}

interface ToolSwitcherProps {
  tools: Tool[];
  activeTool: string;
  setActiveTool: (tool: string) => void;
}

const ToolSwitcher: React.FC<ToolSwitcherProps> = ({ tools, activeTool, setActiveTool }) => {
  return (
    <div className="mb-8 flex flex-wrap gap-3">
      {tools.map((tool) => {
        const active = activeTool === tool.name;
        return (
          <button
            key={tool.name}
            onClick={() => setActiveTool(tool.name)}
            className={`inline-flex items-center gap-2 rounded-full px-5 py-2 text-sm font-medium transition-all
              ${active
                ? 'bg-gradient-to-r from-[#8B5CF6] to-[#60A5FA] text-white shadow-[0_0_0_2px_rgba(139,92,246,.25)]'
                : 'bg-[#0B1318] text-[#B6C0CC] border border-white/10 hover:border-white/20 hover:text-white'
              }`}
            aria-pressed={active}
          >
            <span className={`${active ? 'opacity-100' : 'opacity-80'}`}>{tool.icon}</span>
            <span>{tool.name}</span>
          </button>
        );
      })}
    </div>
  );
};

export default ToolSwitcher;
