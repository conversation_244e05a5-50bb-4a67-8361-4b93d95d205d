import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  const { keyword } = await req.json();

  if (!keyword) {
    return NextResponse.json({ error: 'keyword is required' }, { status: 400 });
  }

  const url = `https://keyword-research-for-youtube.p.rapidapi.com/yttags.php?keyword=${encodeURIComponent(keyword)}`;
  const options = {
    method: 'GET',
    headers: {
      'x-rapidapi-key': '**************************************************',
      'x-rapidapi-host': 'keyword-research-for-youtube.p.rapidapi.com'
    }
  };

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return NextResponse.json(result);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to fetch keywords' }, { status: 500 });
  }
}