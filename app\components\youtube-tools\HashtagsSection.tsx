"use client";

import React from 'react';
import { HashtagInsightsIcon, ThumbnailDesignerIcon } from '../icons/YoutubeIcons';
import { CopyIcon, ShareIcon } from '../icons/icons';

interface HashtagsSectionProps {
  hashtags: string[];
  copyToClipboard: (text: string) => void;
  shareContent: (title: string, text: string) => void;
  regenerateSection: (type: 'titles' | 'description' | 'keywords' | 'hashtags' | 'all') => void;
  gptThumbUrl: string | null;
  isGeneratingGptThumb: boolean;
  gptThumbError: string | null;
  onGenerateThumbnail: () => void;
}

const HashtagsSection: React.FC<HashtagsSectionProps> = ({
  hashtags,
  copyToClipboard,
  shareContent,
  regenerateSection,
  gptThumbUrl,
  isGeneratingGptThumb,
  gptThumbError,
  onGenerateThumbnail,
}) => {
  if (hashtags.length === 0) return null;

  return (
    <div className="mt-8 rounded-2xl border border-white/10 bg-[#0A0F14] p-6 sm:p-8">
      <div className="mb-5 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-[#8B5CF6]"><HashtagInsightsIcon /></span>
          <h3 className="text-lg font-semibold">Hashtag Suggestions</h3>
        </div>
        <div className="flex items-center gap-2 text-white/70">
          <button
            onClick={() => copyToClipboard(hashtags.join(', '))}
            className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
            title="Copy"
          >
            <CopyIcon /><span>Copy</span>
          </button>
          <button
            onClick={() => shareContent('Hashtag Suggestions', hashtags.join(' '))}
            className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
            title="Share"
          >
            <ShareIcon /><span>Share</span>
          </button>
          <button
            onClick={() => regenerateSection('hashtags')}
            className="rounded-md px-2 py-1 text-xs hover:bg-white/5"
          >
            Redo
          </button>
        </div>
      </div>
      <div className="flex flex-wrap gap-2">
        {hashtags.map((h: string, i: number) => (
          <span key={i} className="rounded-full border border-white/10 bg-[#0B1318] px-3 py-1 text-xs text-white/85">{h}</span>
        ))}
      </div>

      {/* Thumbnail Generator [GPT-Image-1] */}
      <div className="mt-8 rounded-xl border border-white/10 bg-[#0A0F14] p-4">
        <div className="mb-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-[#8B5CF6]"><ThumbnailDesignerIcon /></span>
            <h4 className="text-base font-semibold">Thumbnail Generator</h4>
          </div>
          <div className="flex items-center gap-2 text-white/70">
            <button
              onClick={onGenerateThumbnail}
              className="rounded-md px-2 py-1 text-xs hover:bg-white/5 disabled:opacity-50"
              disabled={isGeneratingGptThumb}
            >
              {isGeneratingGptThumb ? 'Generating…' : 'Generate'}
            </button>
          </div>
        </div>

        {gptThumbError && (
          <div className="rounded-md border border-red-500/20 bg-red-500/10 p-3 text-sm text-red-100/90">
            {gptThumbError}
          </div>
        )}

        <div className="mt-3">
          <div
            className="relative overflow-hidden rounded-lg border border-white/10 bg-[#0B1318]"
            style={{ width: 1280, height: 720, maxWidth: '100%' }}
          >
            {gptThumbUrl ? (
              <img
                src={gptThumbUrl}
                alt="Generated thumbnail (GPT-Image-1)"
                className="h-full w-full object-cover"
                width={1024}
                height={720}
              />
            ) : (
              <div className="flex h-full w-full items-center justify-center text-white/60">
                {isGeneratingGptThumb ? 'Generating thumbnail…' : 'No GPT-Image-1 thumbnail yet'}
              </div>
            )}
          </div>

          {gptThumbUrl && (
            <div className="mt-3 flex items-center gap-2 text-white/70">
              <a
                href={gptThumbUrl}
                download="thumbnail-gpt-image-1-1024x720.png"
                className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
              >
                Download
              </a>
              <button
                onClick={() => copyToClipboard(gptThumbUrl)}
                className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
              >
                <CopyIcon /><span>Copy URL</span>
              </button>
              <button
                onClick={() => shareContent('Generated Thumbnail (GPT-Image-1)', gptThumbUrl)}
                className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
              >
                <ShareIcon /><span>Share</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HashtagsSection;
