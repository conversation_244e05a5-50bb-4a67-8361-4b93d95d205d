import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";

export async function GET(req: NextRequest) {
  try {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
    const hasToken = Boolean(token);
    console.log("[debug][token] hasToken:", hasToken, "token:", token ?? null);

    return NextResponse.json({
      ok: true,
      hasToken,
      token, // Will include sub, email, id, etc. (no secrets)
    });
  } catch (err: any) {
    console.error("[debug][token] error", err?.message);
    return NextResponse.json(
      { ok: false, error: err?.message ?? "unknown error" },
      { status: 500 }
    );
  }
}