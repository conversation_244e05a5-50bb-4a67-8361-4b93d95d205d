import { NextRequest, NextResponse } from 'next/server';

// Define the response type for the Google Keyword Insight API
export interface KeywordInsight {
  text: string;
  volume: number;
  competition_level: string;
  competition_index: number;
  low_bid: number;
  high_bid: number;
  trend: number;
}

export async function GET(request: NextRequest) {
  try {
    // Get the keyword from the query parameters
    const { searchParams } = new URL(request.url);
    const keyword = searchParams.get('keyword');
    const location = searchParams.get('location') || 'US';
    const lang = searchParams.get('lang') || 'en';

    if (!keyword) {
      return NextResponse.json(
        { error: 'Keyword parameter is required' },
        { status: 400 }
      );
    }

    // Check if API key is configured
    if (!process.env.RAPIDAPI_KEY) {
      return NextResponse.json(
        { error: 'RapidAPI key not configured. Please set RAPIDAPI_KEY environment variable.' },
        { status: 500 }
      );
    }

    // Encode the keyword for URL
    const encodedKeyword = encodeURIComponent(keyword);
    
    // Construct the API URL
    const url = `https://google-keyword-insight1.p.rapidapi.com/keysuggest/?keyword=${encodedKeyword}&location=${location}&lang=${lang}`;
    
    // Set up the request options
    const options = {
      method: 'GET',
      headers: {
        'x-rapidapi-key': process.env.RAPIDAPI_KEY,
        'x-rapidapi-host': 'google-keyword-insight1.p.rapidapi.com'
      }
    };

    console.log(`Fetching keyword insights for: ${keyword}`);
    
    // Make the request to the RapidAPI endpoint
    const response = await fetch(url, options);
    
    // Check if the response is successful
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error:', errorText);
      return NextResponse.json(
        { error: `Failed to fetch keyword insights: ${response.statusText}` },
        { status: response.status }
      );
    }
    
    // Parse the response
    const data = await response.json();
    
    // Return the data
    return NextResponse.json({ success: true, data });
    
  } catch (error) {
    console.error('Error fetching keyword insights:', error);
    return NextResponse.json(
      { error: 'Failed to fetch keyword insights' },
      { status: 500 }
    );
  }
}
