'use client';

import { useEffect } from 'react';
import <PERSON><PERSON><PERSON> from 'next/script';

declare global {
  interface Window {
    AppleID: {
      auth: {
        init: (config: any) => void;
        signIn: () => Promise<any>;
      };
    };
  }
}

export default function AppleSignInInit() {
  useEffect(() => {
    if (typeof window !== 'undefined' && window.AppleID) {
      window.AppleID.auth.init({
        clientId: process.env.NEXT_PUBLIC_APPLE_CLIENT_ID,
        scope: 'email name',
        redirectURI: process.env.NEXT_PUBLIC_APPLE_REDIRECT_URI,
        state: 'origin:web',
        usePopup: true,
      });
    }
  }, []);

  return (
    <Script
      src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"
      strategy="lazyOnload"
      onLoad={() => {
        if (window.AppleID) {
          window.AppleID.auth.init({
            clientId: process.env.NEXT_PUBLIC_APPLE_CLIENT_ID,
            scope: 'email name',
            redirectURI: process.env.NEXT_PUBLIC_APPLE_REDIRECT_URI,
            state: 'origin:web',
            usePopup: true,
          });
        }
      }}
    />
  );
} 