import React, { RefObject, useState, useCallback } from 'react';

interface LipSyncModalProps {
  show: boolean;
  onClose: () => void;
  videoFile: File | null;
  setVideoFile: (file: File | null) => void;
  audioFile: File | null;
  setAudioFile: (file: File | null) => void;
  imageFile: File | null;
  setImageFile: (file: File | null) => void;
  onGenerate: (params: {
    videoFile: File | null;
    audioFile: File | null;
    imageFile: File | null;
    modelType: 'kling' | 'sonic';
    keepResolution: boolean;
  }) => void;
  videoFileInputRef: RefObject<HTMLInputElement>;
  audioFileInputRef: RefObject<HTMLInputElement>;
  imageFileInputRef: RefObject<HTMLInputElement>;
  handleVideoFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleAudioFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleImageFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const LipSyncModal: React.FC<LipSyncModalProps> = ({
  show,
  onClose,
  videoFile,
  setVideoFile,
  audioFile,
  setAudioFile,
  imageFile,
  setImageFile,
  onGenerate,
  videoFileInputRef,
  audioFileInputRef,
  imageFileInputRef,
  handleVideoFileUpload,
  handleAudioFileUpload,
  handleImageFileUpload,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [modelType, setModelType] = useState<'kling' | 'sonic'>('kling');
  const [keepResolution, setKeepResolution] = useState(true);

  const handleLipSyncSubmit = useCallback(async () => {
    // Validate inputs based on model type
    if (modelType === 'kling') {
      if (!videoFile || !audioFile) {
        setError('Please upload both video and audio files for Kling model.');
        return;
      }
    } else if (modelType === 'sonic') {
      if (!imageFile || !audioFile) {
        setError('Please upload both image and audio files for Sonic model.');
        return;
      }
    }

    setError(null);

    // Close modal immediately and pass generation parameters to parent
    onClose();
    onGenerate({
      videoFile,
      audioFile,
      imageFile,
      modelType,
      keepResolution
    });
  }, [videoFile, audioFile, imageFile, modelType, keepResolution, onGenerate, onClose]);

  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1a1a2e] border border-[#2a2a3e] rounded-lg p-6 w-full max-w-lg max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">
            Lip Sync Settings
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>

        {/* Model Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Model Type
          </label>
          <div className="flex gap-2">
            <button
              onClick={() => setModelType('kling')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                modelType === 'kling'
                  ? 'bg-[#6366f1] text-white'
                  : 'bg-[#2a2a3e] text-gray-300 hover:bg-[#3a3a4e]'
              }`}
            >
              Kling (Video + Audio)
              <span className="ml-1 text-xs opacity-75">✓ Reliable</span>
            </button>
            <button
              onClick={() => setModelType('sonic')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                modelType === 'sonic'
                  ? 'bg-[#6366f1] text-white'
                  : 'bg-[#2a2a3e] text-gray-300 hover:bg-[#3a3a4e]'
              }`}
            >
              Sonic (Image + Audio)
              <span className="ml-1 text-xs opacity-75">⚠ Experimental</span>
            </button>
          </div>
          <p className="text-xs text-gray-400 mt-1">
            {modelType === 'kling' 
              ? 'Syncs lip movements in existing videos' 
              : 'Creates talking face animations from portrait images. Best results with clear, high-resolution frontal portraits. Note: This model may be less reliable than Kling.'
            }
          </p>
        </div>

        {/* Video File Upload Area (Kling Model) */}
        {modelType === 'kling' && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Upload Video
            </label>
            <div
              className="border-2 border-dashed border-[#2a2a3e] rounded-lg p-6 text-center cursor-pointer hover:border-[#3a3a4e] transition-colors"
              onClick={() => videoFileInputRef.current?.click()}
            >
              {videoFile ? (
                <div className="flex flex-col items-center">
                  <div className="flex items-center justify-center w-16 h-16 bg-[#6366f1] rounded-lg mb-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                      <path d="M15 12l-6-4v8l6-4z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <rect x="3" y="4" width="18" height="16" rx="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="text-white text-sm font-medium">Video file uploaded</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setVideoFile(null);
                    }}
                    className="mt-2 text-xs text-gray-400 hover:text-white transition-colors"
                  >
                    Remove file
                  </button>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 mb-2">
                    <path d="M15 12l-6-4v8l6-4z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <rect x="3" y="4" width="18" height="16" rx="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span className="text-gray-400 text-sm">Upload a video file</span>
                  <span className="text-gray-500 text-xs mt-1">Supports MP4, AVI, MOV, WMV</span>
                </div>
              )}
            </div>
            <input
              ref={videoFileInputRef}
              type="file"
              accept="video/*"
              onChange={handleVideoFileUpload}
              className="hidden"
            />
          </div>
        )}

        {/* Image File Upload Area (Sonic Model) */}
        {modelType === 'sonic' && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Upload Portrait Image
            </label>
            <div
              className="border-2 border-dashed border-[#2a2a3e] rounded-lg p-6 text-center cursor-pointer hover:border-[#3a3a4e] transition-colors"
              onClick={() => imageFileInputRef.current?.click()}
            >
              {imageFile ? (
                <div className="flex flex-col items-center">
                  <div className="flex items-center justify-center w-16 h-16 bg-[#6366f1] rounded-lg mb-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                      <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" strokeWidth="2"/>
                      <path d="M21 15l-5-5L5 21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="text-white text-sm font-medium">Image file uploaded</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setImageFile(null);
                    }}
                    className="mt-2 text-xs text-gray-400 hover:text-white transition-colors"
                  >
                    Remove file
                  </button>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 mb-2">
                    <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" strokeWidth="2"/>
                    <path d="M21 15l-5-5L5 21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span className="text-gray-400 text-sm">Upload a portrait image</span>
                  <span className="text-gray-500 text-xs mt-1">Best with frontal or near-frontal views</span>
                  <span className="text-gray-500 text-xs">High resolution recommended for best results</span>
                </div>
              )}
            </div>
            <input
              ref={imageFileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageFileUpload}
              className="hidden"
            />
          </div>
        )}

        {/* Audio File Upload Area */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Upload Audio
          </label>
          <div
            className="border-2 border-dashed border-[#2a2a3e] rounded-lg p-6 text-center cursor-pointer hover:border-[#3a3a4e] transition-colors"
            onClick={() => audioFileInputRef.current?.click()}
          >
            {audioFile ? (
              <div className="flex flex-col items-center">
                <div className="flex items-center justify-center w-16 h-16 bg-[#6366f1] rounded-lg mb-2">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                    <path d="M9 18V5l12-2v13M9 18c0 1.657-1.343 3-3 3s-3-1.343-3-3 1.343-3 3-3 3 1.343 3 3zM21 16c0 1.657-1.343 3-3 3s-3-1.343-3-3 1.343-3 3-3 3 1.343 3 3z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <span className="text-white text-sm font-medium">Audio file uploaded</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setAudioFile(null);
                  }}
                  className="mt-2 text-xs text-gray-400 hover:text-white transition-colors"
                >
                  Remove file
                </button>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 mb-2">
                  <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M19 10v2a7 7 0 0 1-14 0v-2M12 19v4M8 23h8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-gray-400 text-sm">Upload an audio file</span>
                <span className="text-gray-500 text-xs mt-1">Supports MP3 and WAV files</span>
              </div>
            )}
          </div>
          <input
            ref={audioFileInputRef}
            type="file"
            accept="audio/*"
            onChange={handleAudioFileUpload}
            className="hidden"
          />
        </div>

        {/* Sonic Model Options */}
        {modelType === 'sonic' && (
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-gray-300 mb-2">
              <input
                type="checkbox"
                checked={keepResolution}
                onChange={(e) => setKeepResolution(e.target.checked)}
                className="mr-2 rounded border-gray-600 bg-gray-700 text-[#6366f1] focus:ring-[#6366f1]"
              />
              Keep Original Resolution
            </label>
            <p className="text-xs text-gray-400">
              Maintains the original image resolution in the output video
            </p>
          </div>
        )}

        {error && (
          <div className="bg-red-500 text-white text-sm p-3 rounded-lg mb-4">
            {error}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 bg-[#2a2a3e] text-white rounded-lg hover:bg-[#3a3a4e] transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleLipSyncSubmit}
            disabled={
              !audioFile || 
              (modelType === 'kling' && !videoFile) ||
              (modelType === 'sonic' && !imageFile)
            }
            className="flex-1 px-4 py-2 bg-[#6366f1] text-white rounded-lg hover:bg-[#5855eb] disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
          >
            Generate {modelType === 'kling' ? 'Lip Sync' : 'Talking Face'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LipSyncModal;
