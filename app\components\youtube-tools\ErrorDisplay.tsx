"use client";

import React from 'react';

interface ErrorDisplayProps {
  error: string | null;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error }) => {
  if (!error) return null;

  return (
    <div className="mt-8 rounded-2xl border border-red-500/20 bg-red-500/10 p-6">
      <h3 className="mb-2 text-lg font-semibold text-red-200">Error</h3>
      <p className="text-red-100/90">{error}</p>
    </div>
  );
};

export default ErrorDisplay;
