"use client";
import { useRef } from 'react';
import Image from 'next/image';
import type { ImageData, ImageModalProps } from './types';
import { DownloadIcon } from './icons/icons';

// Sample related images - in a real app, these would be passed as props or fetched
const relatedImages = [
  { src: "/inspiration/1.jpg", height: "h-32" },
  { src: "/inspiration/2.jpg", height: "h-32" },
  { src: "/inspiration/3.jpg", height: "h-32" },
  { src: "/inspiration/4.jpg", height: "h-32" },
];

export default function ImageModal({
  image,
  isOpen,
  onClose,
  onDownload,
  title = "Example Model",
  description = "Here is your processed output"
}: ImageModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);

  // Close modal on outside click
  const handleModalClick = (e: React.MouseEvent) => {
    if (modalRef.current && e.target === modalRef.current) {
      onClose();
    }
  };

  if (!isOpen || !image) return null;

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 backdrop-blur-sm p-2 sm:p-4"
      onClick={handleModalClick}
    >
      <div className="relative bg-[#0f1419] rounded-2xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-y-auto border border-[#2a3441]">
        {/* Close button */}
        <button
          className="absolute top-4 right-4 text-[#9ca3af] hover:text-white text-2xl transition-colors z-20 bg-black bg-opacity-50 rounded-full w-8 h-8 flex items-center justify-center"
          onClick={onClose}
          aria-label="Close"
        >
          &times;
        </button>

        {/* Main image section */}
        <div className="relative">
          <Image
            src={image.src}
            alt="Preview"
            width={800}
            height={500}
            className="w-full h-[300px] sm:h-[400px] md:h-[500px] object-cover rounded-t-2xl"
          />
          {/* Download button on image */}
          <button
            className="absolute top-4 right-16  transition-all duration-200 hover:bg-opacity-80"
            onClick={() => onDownload(image.src)}
            title="Download"
          >
            <DownloadIcon />

          </button>
        </div>

        {/* Description section */}
        <div className="p-4 sm:p-6">
          <p className="text-[#e1e5e9] text-sm sm:text-base leading-relaxed mb-6">
            {description}
          </p>

          {/* Related images grid */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
            {relatedImages.map((relatedImg, index) => (
              <div
                key={index}
                className="relative group cursor-pointer rounded-lg overflow-hidden bg-[#1a1a1a] border border-[#2a2a2a] hover:border-[#3a3a3a] transition-all duration-300"
                onClick={() => {
                  // In a real app, you'd update the main image or open a new modal
                  console.log('Related image clicked:', relatedImg);
                }}
              >
                <Image
                  src={relatedImg.src}
                  alt={`Related ${index + 1}`}
                  width={200}
                  height={150}
                  className="w-full h-24 sm:h-32 object-cover transition-all duration-300 group-hover:scale-105"
                />
                {/* Download button for related images */}
                <button
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-all duration-200"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDownload(relatedImg.src);
                  }}
                  title="Download"
                >
                  <DownloadIcon />

                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
