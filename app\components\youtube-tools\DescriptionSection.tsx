"use client";

import React from 'react';
import { DescriptionGeneratorIcon } from '../icons/YoutubeIcons';
import { CopyIcon, ShareIcon } from '../icons/icons';

interface DescriptionSectionProps {
  description: string;
  copyToClipboard: (text: string) => void;
  shareContent: (title: string, text: string) => void;
  regenerateSection: (type: 'titles' | 'description' | 'keywords' | 'hashtags' | 'all') => void;
}

const DescriptionSection: React.FC<DescriptionSectionProps> = ({ description, copyToClipboard, shareContent, regenerateSection }) => {
  if (!description) return null;

  return (
    <div className="mt-8 rounded-2xl border border-white/10 bg-[#0A0F14] p-6 sm:p-8">
      <div className="mb-5 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-[#8B5CF6]"><DescriptionGeneratorIcon /></span>
          <h3 className="text-lg font-semibold">Video Description</h3>
        </div>
        <div className="flex items-center gap-2 text-white/70">
          <button
            onClick={() => copyToClipboard(description)}
            className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
            title="Copy"
          >
            <CopyIcon /><span>Copy</span>
          </button>
          <button
            onClick={() => shareContent('Video Description', description)}
            className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
            title="Share"
          >
            <ShareIcon /><span>Share</span>
          </button>
          <button
            onClick={() => regenerateSection('description')}
            className="rounded-md px-2 py-1 text-xs hover:bg-white/5"
          >
            Redo
          </button>
        </div>
      </div>
      <div className="prose prose-invert max-w-none">
        <p className="whitespace-pre-line text-white/85">{description}</p>
      </div>
    </div>
  );
};

export default DescriptionSection;
