"use client";

import React from 'react';
import { AnalyzeVideoIcon } from '../icons/YoutubeIcons';
import { GeneratebtnIcon } from '../icons/icons';

interface InputCardProps {
  activeTool: string;
  inputValue: string;
  setInputValue: (value: string) => void;
  isAnalyzing: boolean;
  isGeneratingGptThumb: boolean;
  handleAnalyze: () => void;
  onThumbnailGenerate: () => void;
}

const InputCard: React.FC<InputCardProps> = ({
  activeTool,
  inputValue,
  setInputValue,
  isAnalyzing,
  isGeneratingGptThumb,
  handleAnalyze,
  onThumbnailGenerate,
}) => {
  const isThumbnailDesigner = activeTool === 'Thumbnail Designer';
  const isProcessing = isThumbnailDesigner ? isGeneratingGptThumb : isAnalyzing;

  return (
    <div className="relative overflow-hidden rounded-2xl border border-white/10 bg-[#0A0F14] p-6 sm:p-8">
      <div className="pointer-events-none absolute inset-y-0 right-0 w-1/2">
        <div className="absolute inset-0 bg-gradient-to-l from-[#8B5CF6] via-[#7C9CF2]/40 to-transparent opacity-60"></div>
        <div
          className="absolute inset-0 opacity-20 mix-blend-screen"
          style={{ backgroundImage: "url('/Gradient-Lines.png')", backgroundSize: 'cover', backgroundPosition: 'center' }}
        />
        <div className="absolute -right-24 top-1/2 h-72 w-72 -translate-y-1/2 rounded-full bg-[#8B5CF6]/40 blur-3xl"></div>
      </div>

      <div className="relative z-10 grid gap-6 sm:grid-cols-2">
        <div>
          <p className="mb-1 text-xs uppercase tracking-[0.16em] text-white/60">
            {isThumbnailDesigner ? 'Thumbnail Designer' : 'Assistant'}
          </p>
          <h2 className="mb-4 text-xl font-semibold">
            {isThumbnailDesigner ? 'Thumbnail Designer' : 'YouTube Content Assistant'}
          </h2>

          <div className="relative">
            <div className="pointer-events-none absolute left-3 top-1/2 -translate-y-1/2 opacity-70">
              <AnalyzeVideoIcon />
            </div>
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              aria-label="Paste YouTube URL, Title, or Idea"
              placeholder="https://www.youtube.com/..."
              className="w-full rounded-full border border-white/15 bg-[#05080A] py-3 pl-11 pr-12 text-sm text-white placeholder:text-white/40 outline-none ring-0 focus:border-[#8B5CF6] focus:shadow-[0_0_0_3px_rgba(139,92,246,.25)] transition-all"
            />
          </div>
          <p className="mt-2 text-xs text-white/50">Paste YouTube Video URL, Title, or Idea</p>
        </div>

        <div className="flex items-center justify-end">
          <button
            onClick={isThumbnailDesigner ? onThumbnailGenerate : handleAnalyze}
            disabled={isProcessing}
            className={`group relative z-10 inline-flex items-center gap-2 rounded-full px-6 py-3 text-sm font-semibold text-white transition-all
              ${isProcessing
                ? 'bg-[#8B5CF6]/60 cursor-not-allowed'
                : 'bg-[#8B5CF6] hover:bg-[#7C3AED]'
              } shadow-[0_10px_40px_-5px_rgba(139,92,246,.45)]
            `}
          >
            <span className="opacity-90"><GeneratebtnIcon /></span>
            <span>{isThumbnailDesigner
              ? (isGeneratingGptThumb ? 'Generating…' : 'Generate')
              : (isAnalyzing ? 'Analyzing…' : 'Analyze Video')}</span>
            {!isProcessing && (
              <span className="absolute -inset-px rounded-full bg-gradient-to-r from-[#8B5CF6]/0 via-white/10 to-[#60A5FA]/0 opacity-0 transition-opacity group-hover:opacity-100" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default InputCard;
