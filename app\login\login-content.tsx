"use client";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import SocialLoginButtons from "../components/SocialLoginButtons";
import Image from "next/image";

export default function LoginContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [error, setError] = useState("");
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    const message = searchParams.get("message");
    if (message) {
      setMessage(message);
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    try {
      const res = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.error || "Something went wrong");
      }

      router.push("/dashboard");
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <div className="flex w-full lg:w-3/5 xl:w-1/2 min-h-screen lg:min-h-0 items-center justify-center relative px-4 py-8">
      {/* Gradient Lines Background */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/Gradient-Lines.png"
          alt="Gradient Lines"
          fill
          className="object-cover opacity-20 "
        />
      </div>
      <div className="bg-[#23233a] p-8 rounded-2xl shadow-2xl w-full max-w-md border border-[#35355a] relative z-10 my-auto animate-fade-in-up">
        <div className="mb-8 animate-fade-in-up" style={{animationDelay: '0.1s', animationFillMode: 'both'}}>
          <div className="text-[#b3b3c6] text-sm mb-2">Welcome to Keoo</div>
          <h2 className="text-2xl font-semibold text-white">Sign in to your account</h2>
        </div>
        <div className="animate-fade-in-up" style={{animationDelay: '0.2s', animationFillMode: 'both'}}>
          <SocialLoginButtons />
        </div>
        <div className="flex items-center my-6 animate-fade-in-up" style={{animationDelay: '0.3s', animationFillMode: 'both'}}>
          <div className="flex-1 h-px bg-[#35355a]" />
          <span className="mx-4 text-[#b3b3c6] text-sm">Or</span>
          <div className="flex-1 h-px bg-[#35355a]" />
        </div>
        <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
          <div className="group animate-fade-in-up relative" style={{animationDelay: '0.4s', animationFillMode: 'both'}}>
            <input
              type="email"
              name="email"
              className="w-full px-4 py-5 rounded-full bg-[#18182a] border border-[#35355a] text-white placeholder-transparent focus:outline-none focus:ring-2 focus:ring-[#7c5cff] focus:border-[#7c5cff] hover:border-[#4a4a6a] transition-all duration-300 ease-in-out transform hover:scale-[1.02] focus:scale-[1.02] hover:shadow-lg focus:shadow-xl focus:shadow-[#7c5cff]/20 text-base peer"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleChange}
              autoComplete="email"
              required
            />
            <label className="absolute left-4 top-5 text-[#6b6b8a] text-base transition-all duration-300 ease-in-out peer-placeholder-shown:top-5 peer-placeholder-shown:text-base peer-placeholder-shown:text-[#6b6b8a] peer-focus:-top-2 peer-focus:left-4 peer-focus:text-xs peer-focus:text-[#7c5cff] peer-focus:bg-[#18182a] peer-focus:px-2 peer-focus:rounded peer-valid:-top-2 peer-valid:left-4 peer-valid:text-xs peer-valid:text-[#7c5cff] peer-valid:bg-[#18182a] peer-valid:px-2 peer-valid:rounded pointer-events-none">
              Email address *
            </label>
          </div>
          <div className="group animate-fade-in-up relative" style={{animationDelay: '0.5s', animationFillMode: 'both'}}>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                className="w-full px-4 py-5 rounded-full bg-[#18182a] border border-[#35355a] text-white placeholder-transparent focus:outline-none focus:ring-2 focus:ring-[#7c5cff] focus:border-[#7c5cff] hover:border-[#4a4a6a] transition-all duration-300 ease-in-out transform hover:scale-[1.02] focus:scale-[1.02] hover:shadow-lg focus:shadow-xl focus:shadow-[#7c5cff]/20 pr-12 text-base peer"
                placeholder="Password"
                value={formData.password}
                onChange={handleChange}
                autoComplete="current-password"
                required
              />
              <label className="absolute left-4 top-5 text-[#6b6b8a] text-base transition-all duration-300 ease-in-out peer-placeholder-shown:top-5 peer-placeholder-shown:text-base peer-placeholder-shown:text-[#6b6b8a] peer-focus:-top-2 peer-focus:left-4 peer-focus:text-xs peer-focus:text-[#7c5cff] peer-focus:bg-[#18182a] peer-focus:px-2 peer-focus:rounded peer-valid:-top-2 peer-valid:left-4 peer-valid:text-xs peer-valid:text-[#7c5cff] peer-valid:bg-[#18182a] peer-valid:px-2 peer-valid:rounded pointer-events-none">
                Password *
              </label>
              <button
                type="button"
                className="absolute right-4 top-1/2 -translate-y-1/2 text-[#6b6b8a] hover:text-white hover:scale-110 transition-all duration-200 ease-in-out z-10"
                onClick={() => setShowPassword(v => !v)}
                tabIndex={-1}
              >
                {showPassword ? (
                  <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/></svg>
                ) : (
                  <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7a9.956 9.956 0 012.293-3.95M6.7 6.7A9.956 9.956 0 0112 5c4.478 0 8.268 2.943 9.542 7a9.956 9.956 0 01-4.043 5.112M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path d="M3 3l18 18"/></svg>
                )}
              </button>
            </div>
          </div>
          {error && <div className="text-red-400 text-sm text-center mt-2 animate-fade-in-up animate-shake bg-red-500/10 border border-red-500/20 rounded-lg p-3">{error}</div>}
          {message && <div className="text-green-400 text-sm text-center mt-2 animate-fade-in-up bg-green-500/10 border border-green-500/20 rounded-lg p-3">{message}</div>}
          <div className="animate-fade-in-up" style={{animationDelay: '0.6s', animationFillMode: 'both'}}>
            <button
              type="submit"
              className="w-full mt-6 py-6 rounded-full bg-[#7c5cff] hover:bg-[#6a4eea] hover:shadow-xl hover:shadow-[#7c5cff]/30 active:scale-[0.98] transition-all duration-300 ease-in-out transform hover:scale-[1.02] text-white font-medium text-base shadow-lg disabled:opacity-60 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-lg"
              disabled={loading}
            >
              {loading ? (
                <span className="flex items-center justify-center gap-2">
                  <div className="loading-spinner"></div>
                  Please wait...
                </span>
              ) : (
                "Continue"
              )}
            </button>
          </div>
        </form>
        <div className="mt-6 text-center text-[#b3b3c6] text-sm animate-fade-in-up" style={{animationDelay: '0.7s', animationFillMode: 'both'}}>
          <span>Forgot your password? </span>
          <Link href="/forgot-password" className="text-[#7c5cff] hover:underline font-medium transition-colors duration-200">
            Send reset link
          </Link>
        </div>
        <div className="mt-2 text-center text-[#b3b3c6] text-sm animate-fade-in-up" style={{animationDelay: '0.8s', animationFillMode: 'both'}}>
          Don&apos;t have an account?{' '}
          <Link href="/signup" className="text-[#7c5cff] hover:underline font-medium transition-colors duration-200">
            Sign up
          </Link>
        </div>
      </div>
    </div>
  );
}