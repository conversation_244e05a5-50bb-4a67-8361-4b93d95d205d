{"scenarios": {"1": {"name": "Random Style", "prompt": "Turn Image Idea Into YouTube Thumbnail    Use the provided Image Idea and Visual Description to create a high-quality, visually compelling image suitable for a YouTube thumbnail.   Guidelines: Aspect Ratio:  Must add padding around all edges of the image. Do not place any text on the edges of the image padding as shown in the \"Thumbnail Design Structure None\" image attached. Must follow the instructions written on the \"Thumbnail Design Structure None\" image attached  Keep the composition focused and not cluttered. Make the subject pop and ensure the thumbnail works well even when viewed on a small screen.   Style: Realistic or stylistically fitting based on tone (e.g., cinematic, dramatic, moody, illustrative)   Composition: Strong central focus Clear emotional or visual hook   Text (if included): Must be under 8 words Bold, legible, and integrated into the scene naturally Should enhance, not clutter, the thumbnail   Goal: Create an image that is bold, clean, emotionally resonant, and immediately scroll-stopping — perfect for YouTube thumbnail performance.   Input: Thumbnail Text Title: Must use this text if provided {{ Insert Thumbnail Text Title }}. If not provided, use image idea.  Thumbnail Background Assets: Must use this if provided use these: {{ Insert Thumbnail Background Assets }}. Must not use blank background. If background asset not entered, use the image idea and visual description to come up with background asset. The \"Design guidelines\" have priority over the image idea and visual description, so ensure to adjust the image idea and visual description so that it 100% satisfies the design guidelines.   Uploaded Main Image: Must use this main image if provided {{ Insert Uploaded “Main Image” }}. Recreate the person in the uploaded \"Main Image\" with 100% accuracy in facial identity and physical features. This includes: Face shape, skin tone, and skin texture Eye shape and color Eyebrows, nose, lips, jawline, and cheek structure Hair style, hair color, and hair texture Facial hair (if any), beard or mustache shape and density Body type/build and posture The person must be instantly recognizable as the same individual. Do not alter any facial or bodily characteristics. Output should look like a high-quality, natural photograph with the same person clearly visible and unchanged. If main image is not provided, use visual description to come up with an image.   Thumbnail Design Structure: Must follow the instructions written on this uploaded image {{ Insert Uploaded “Thumbnail Design Structure One” }}   Image Idea & Visual Description:  {{ Insert Image Idea & Visual Description }}"}, "2a": {"name": "Image Left, Text Right", "prompt": "Turn Image Idea Into YouTube Thumbnail    Use the provided Image Idea and Visual Description to create a high-quality, visually compelling image suitable for a YouTube thumbnail.   You must follow these \"Design Guidelines\":  Aspect Ratio must be  Must add padding around all edges of the image. Do not place any text on the edges of the image padding as shown in the \"Thumbnail Design Structure One\" image attached. Must follow the instructions written on the \"Thumbnail Design Structure One\" image attached  Must Place the “Main Image” as shown in the \"Thumbnail Design Structure One\" image attached   Must Place the “Thumbnail Text Title” as shown in the \"Thumbnail Design Structure One\" image attached   Style: Realistic or stylistically fitting based on tone (e.g., cinematic, dramatic, moody, illustrative)   Composition: Strong central focus Clear emotional or visual hook   Text (if included): Must be under 8 words Bold, legible, and integrated into the scene naturally Should enhance, not clutter, the thumbnail   Goal: Create an image that is bold, clean, emotionally resonant, and immediately scroll-stopping — perfect for YouTube thumbnail performance.   Input Instructions: Thumbnail Text Title: Must use this text if provided {{ Insert Thumbnail Text Title }}. If not provided, use image idea.  Thumbnail Background Assets: Must use this if provided use these: {{ Insert Thumbnail Background Assets }}. Must not use blank background. If background asset not entered, use the image idea and visual description to come up with background asset. The \"Design guidelines\" have priority over the image idea and visual description, so ensure to adjust the image idea and visual description so that it 100% satisfies the design guidelines.   Uploaded Main Image: Must use this main image if provided {{ Insert Uploaded “Main Image” }}. Recreate the person in the uploaded \"Main Image\" with 100% accuracy in facial identity and physical features. This includes: Face shape, skin tone, and skin texture Eye shape and color Eyebrows, nose, lips, jawline, and cheek structure Hair style, hair color, and hair texture Facial hair (if any), beard or mustache shape and density Body type/build and posture The person must be instantly recognizable as the same individual. Do not alter any facial or bodily characteristics. Output should look like a high-quality, natural photograph with the same person clearly visible and unchanged. If main image is not provided, use visual description to come up with an image.   Thumbnail Design Structure: Must follow the instructions written on this uploaded image {{ Insert Uploaded “Thumbnail Design Structure One” }}   Image Idea & Visual Description:  {{ Insert Image Idea & Visual Description }}"}, "2b": {"name": "Image Right, Text Left", "prompt": "Turn Image Idea Into YouTube Thumbnail    Use the provided Image Idea and Visual Description to create a high-quality, visually compelling image suitable for a YouTube thumbnail.   You must follow these \"Design Guidelines\": Aspect Ratio must be  Must add padding around all edges of the image. Do not place any text on the edges of the image padding as shown in the \"Thumbnail Design Structure Two\" image attached. Must follow the instructions written on the \"Thumbnail Design Structure Two\" image attached  Must Place the “Main Image” as shown in the \"Thumbnail Design Structure Two\" image attached  Must Place the “Thumbnail Text Title” as shown in the \"Thumbnail Design Structure Two\" image attached  Style: Realistic or stylistically fitting based on tone (e.g., cinematic, dramatic, moody, illustrative)   Composition: Strong central focus Clear emotional or visual hook   Text (if included): Must be under 8 words Bold, legible, and integrated into the scene naturally Should enhance, not clutter, the thumbnail   Goal: Create an image that is bold, clean, emotionally resonant, and immediately scroll-stopping — perfect for YouTube thumbnail performance.   Input Instructions: Thumbnail Text Title: Must use this text if provided {{ Insert Thumbnail Text Title }}. If not provided, use image idea.  Thumbnail Background Assets: Must use this if provided use these: {{ Insert Thumbnail Background Assets }}. Must not use blank background. If background asset not entered, use the image idea and visual description to come up with background asset. The \"Design guidelines\" have priority over the image idea and visual description, so ensure to adjust the image idea and visual description so that it 100% satisfies the design guidelines.   Uploaded Main Image: Must use this main image if provided {{ Insert Uploaded “Main Image” }}. Recreate the person in the uploaded \"Main Image\" with 100% accuracy in facial identity and physical features. This includes: Face shape, skin tone, and skin texture Eye shape and color Eyebrows, nose, lips, jawline, and cheek structure Hair style, hair color, and hair texture Facial hair (if any), beard or mustache shape and density Body type/build and posture The person must be instantly recognizable as the same individual. Do not alter any facial or bodily characteristics. Output should look like a high-quality, natural photograph with the same person clearly visible and unchanged. If main image is not provided, use visual description to come up with an image.   Thumbnail Design Structure: Must follow the instructions written on this uploaded image {{Insert Uploaded “Thumbnail Design Structure Two”}}   Image Idea & Visual Description:  {{ Insert Image Idea & Visual Description }}"}, "2c": {"name": "Image Center, Text Bottom", "prompt": "Turn Image Idea Into YouTube Thumbnail    Use the provided Image Idea and Visual Description to create a high-quality, visually compelling image suitable for a YouTube thumbnail.   You must follow these \"Design Guidelines\": Aspect Ratio must be  Must add padding around all edges of the image. Do not place any text on the edges of the image padding as shown in the \"Thumbnail Design Structure Three\" image attached. Must follow the instructions written on the \"Thumbnail Design Structure Three\" image attached  Must Place the “Main Image” as shown in the “Thumbnail Design Structure Three\" image attached  Must place the \"Main Image\" at the centre as shown in the “Thumbnail Design Structure Three\" image attached  Must Place the “Thumbnail Text Title” as shown in the “Thumbnail Design Structure Three\" image attached  Must place the “Thumbnail Text Title” at the bottom as shown in the “Thumbnail Design Structure Three\" image attached  Style: Realistic or stylistically fitting based on tone (e.g., cinematic, dramatic, moody, illustrative)   Composition: Strong central focus Clear emotional or visual hook   Text (if included): Must be under 8 words Bold, legible, and integrated into the scene naturally Should enhance, not clutter, the thumbnail   Goal: Create an image that is bold, clean, emotionally resonant, and immediately scroll-stopping — perfect for YouTube thumbnail performance.   Input Instructions: Thumbnail Text Title: Must use this text if provided {{ Insert Thumbnail Text Title }}. If not provided Use this thumbnail text: {{ Insert Image Idea }}.  Thumbnail Background Assets: Must use this if provided use these: {{ Insert Thumbnail Background Assets }}. Must not use blank background. If background asset not entered, use the image idea and visual description to come up with background asset. The \"Design guidelines\" have priority over the image idea and visual description, so ensure to adjust the image idea and visual description so that it 100% satisfies the design guidelines.   Uploaded Main Image: Must use this main image if provided {{ Insert Uploaded “Main Image” }}. Recreate the person in the uploaded \"Main Image\" with 100% accuracy in facial identity and physical features. This includes: Face shape, skin tone, and skin texture Eye shape and color Eyebrows, nose, lips, jawline, and cheek structure Hair style, hair color, and hair texture Facial hair (if any), beard or mustache shape and density Body type/build and posture The person must be instantly recognizable as the same individual. Do not alter any facial or bodily characteristics. Output should look like a high-quality, natural photograph with the same person clearly visible and unchanged. If main image is not provided, use image idea & visual description to come up with a main Image.   Thumbnail Design Structure: Must follow the instructions written on this uploaded image {{Insert Uploaded “Thumbnail Design Structure Three”}}   Image Idea & Visual Description:  {{ Insert Image Idea & Visual Description }}"}, "2d": {"name": "Image Center, Text Top", "prompt": "Turn Image Idea Into YouTube Thumbnail    Use the provided Image Idea and Visual Description to create a high-quality, visually compelling image suitable for a YouTube thumbnail.   You must follow these \"Design Guidelines\": Aspect Ratio must be  Must add padding around all edges of the image. Do not place any text on the edges of the image padding as shown in the \"Thumbnail Design Structure Four\" image attached. Must follow the instructions written on the \"Thumbnail Design Structure Four\" image attached  Must Place the “Main Image” as shown in the “Thumbnail Design Structure Four\" image attached  Must place the \"Main Image\" at the centre as shown in the “Thumbnail Design Structure Four\" image attached  Must Place the “Thumbnail Text Title” as shown in the “Thumbnail Design Structure Four\" image attached  Must place the “Thumbnail Text Title” at the top of the image, just above the main image as shown in the “Thumbnail Design Structure Four\" image attached. Make the text smaller or break the text to another line so it fits at the top.   Style: Realistic or stylistically fitting based on tone (e.g., cinematic, dramatic, moody, illustrative)   Composition: Strong central focus Clear emotional or visual hook   Text (if included): Must be under 8 words Bold, legible, and integrated into the scene naturally Should enhance, not clutter, the thumbnail   Goal: Create an image that is bold, clean, emotionally resonant, and immediately scroll-stopping — perfect for YouTube thumbnail performance.   Input Instructions: Thumbnail Text Title: Must use this text if provided {{ Insert Thumbnail Text Title }}. If not provided, use image idea.  Thumbnail Background Assets: Must use this if provided use  these : {{ Insert Thumbnail Background Assets }}. Must not use blank background. If background asset not entered, use the image idea and visual description to come up with background asset. The \"Design guidelines\" have priority over the image idea and visual description, so ensure to adjust the image idea and visual description so that it 100% satisfies the design guidelines.   Uploaded Main Image: Must use this main image if provided {{ Insert Uploaded “Main Image” }}. Recreate the person in the uploaded \"Main Image\" with 100% accuracy in facial identity and physical features. This includes: Face shape, skin tone, and skin texture Eye shape and color Eyebrows, nose, lips, jawline, and cheek structure Hair style, hair color, and hair texture Facial hair (if any), beard or mustache shape and density Body type/build and posture The person must be instantly recognizable as the same individual. Do not alter any facial or bodily characteristics. Output should look like a high-quality, natural photograph with the same person clearly visible and unchanged. If main image is not provided, use image idea & visual description to come up with a main Image.   Thumbnail Design Structure: Must follow the instructions written on this uploaded image {{Insert Uploaded “Thumbnail Design Structure Four”}}   Image Idea & Visual Description:  {{ Insert Image Idea & Visual Description }}"}, "3": {"name": "Reference Image", "prompt": "Recreate a YouTube thumbnail ( aspect ratio) using the provided assets and instructions. It must 100% match the “reference thumbnail” in layout, style, and structure — with the only change being the main character, replaced by the uploaded “Main Image”.  If “main image” is not provided, use “image idea & visual description” to come up with a “main image”.   You must follow these \"Design Guidelines\": Aspect Ratio must be  Must add padding around all edges of the image. Do not place any text on the edges of the image padding as shown in the \"Thumbnail Design Structure None\" image attached. Must follow the instructions written on the \"Thumbnail Design Structure None\" image attached  Replace the main character in the “Reference Thumbnail” with the uploaded “Main Image”. If main image is not provided, use “image idea & visual description” to come up with a main image. Recreate a Youtube Thumbnail. It must look exactly like it 100%. Same design, same image assets and same text placement and design. The only thing that should change is the “Main Character” in the reference image.  Style must 100% match the tone of the “Reference thumbnail” (e.g., cinematic, dramatic, moody, illustrative, or realistic).  Style: Realistic or stylistically fitting based on tone (e.g., cinematic, dramatic, moody, illustrative)   Composition: Strong central focus Clear emotional or visual hook   Text (if included): Must be under 8 words Bold, legible, and integrated into the scene naturally Should enhance, not clutter, the thumbnail   Recreate a YouTube thumbnail ( aspect ratio) using the provided assets and instructions. The final image must exactly match the “Reference Thumbnail” in layout, style, and structure — with the only change being the main character, replaced by the uploaded “Main Image”. If “main image” is not provided, use “image idea & visual description” to come up with a “main image”.   Input Instructions: Thumbnail Text Title: Must use this text if provided {{ Insert Thumbnail Text Title }} to replace the text in the “Reference Thumbnail”. If not provided, use image idea.  Uploaded Main Image: Must use this main image if provided {{ Insert Uploaded “Main Image” }}. Recreate the person in the uploaded \"Main Image\" with 100% accuracy in facial identity and physical features. This includes: Face shape, skin tone, and skin texture Eye shape and color Eyebrows, nose, lips, jawline, and cheek structure Hair style, hair color, and hair texture Facial hair (if any), beard or mustache shape and density Body type/build and posture The person must be instantly recognizable as the same individual. Do not alter any facial or bodily characteristics. Output should look like a high-quality, natural photograph with the same person clearly visible and unchanged. Replace the main character in the “Reference Thumbnail” with the uploaded “Main Image”. If main image is not provided, use image idea & visual description to come up with a main image.   Image Idea & Visual Description:  {{ Insert Image Idea & Visual Description }}  Reference Thumbnail: Must use this {{ Insert Uploaded “Reference Thumbnail” }} as the template for design. Match all layout elements 100% exactly — image assets, text placement, lighting, mood, and composition. Only the main character should be replaced.  Note: If “main image” is not provided, go ahead and use the \"image idea & visual description\" to come up with a main image for the thumbnail."}}, "summarizePrompt": "Task: Analyze the transcript or text provided below.\nObjective: Identify the core message, emotional tone, setting, and any visual symbolism or characters. Then, translate these into a single attention-grabbing YouTube thumbnail idea with a  aspect ratio in mind.\nOutput Format:\nImage Idea: [Short visual concept or title]\nVisual Description: [Detailed, vivid description of the scene, including: Environment, Key characters or objects (if any), Emotions portrayed, Lighting/mood, Realistic art style or cinematic composition. This should be clear enough to guide AI image generation tools like DALL·E or Midjourney.]\nInstructions:\nDo not quote or summarize the full text.\nFocus only on the core visual hook that would stop a viewer from scrolling.\nThink in cinematic, symbolic, or emotional visuals.\nDo not include minors\nConstraints:\nMust be grounded in real-world or traditional/historical/organic visuals.\nNo sci-fi, futuristic cities, tech, or surreal elements.\nKeep it visually bold, clean, and emotionally resonant\nPerfect for a YouTube thumbnail .\nTranscript/Text:\n"}