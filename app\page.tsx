"use client";
import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useSession } from "next-auth/react";

export default function Home() {
  const router = useRouter();
  const pathname = usePathname();
  const { status, data } = useSession();

  // Debug: log session status changes and routing decisions
  useEffect(() => {
    // Always log current auth state for diagnosis
    console.log("[Home] pathname:", pathname);
    console.log("[Home] session.status:", status);
    console.log("[Home] session.user:", data?.user ?? null);

    if (status === "loading") {
      console.log("[Home] status=loading, skipping redirect");
      return;
    }

    if (status === "authenticated") {
      console.log("[Home] authenticated -> redirecting to /dashboard");
      router.push("/dashboard");
    } else {
      console.log("[Home] unauthenticated -> redirecting to /login");
      router.push("/login");
    }
  }, [status, router, pathname, data?.user]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#18182a]">
      <div className="text-[#b3b3c6]">
        Home redirecting... check console logs for auth flow details.
      </div>
    </div>
  );
}
