import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

// Initialize Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function POST(request: NextRequest) {
  try {
    // Check if API token is configured
    if (!process.env.REPLICATE_API_TOKEN) {
      return NextResponse.json(
        { error: 'Replicate API token not configured. Please set REPLICATE_API_TOKEN environment variable.' },
        { status: 500 }
      );
    }

    const { video_url, audio_file, image_url, model_type = 'kling', keep_resolution = true } = await request.json();

    // Validate inputs based on model type
    if (model_type === 'kling') {
      if (!video_url || !audio_file) {
        return NextResponse.json(
          { error: 'Both video_url and audio_file are required for Kling model' },
          { status: 400 }
        );
      }
    } else if (model_type === 'sonic') {
      if (!image_url || !audio_file) {
        return NextResponse.json(
          { error: 'Both image_url and audio_file are required for Sonic model' },
          { status: 400 }
        );
      }

      // Additional validation for Sonic model
      if (!image_url.includes('cloudinary.com')) {
        return NextResponse.json(
          { error: 'Image must be uploaded to Cloudinary for Sonic model' },
          { status: 400 }
        );
      }

      if (!audio_file.includes('cloudinary.com')) {
        return NextResponse.json(
          { error: 'Audio must be uploaded to Cloudinary for Sonic model' },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json(
        { error: 'Invalid model_type. Must be "kling" or "sonic"' },
        { status: 400 }
      );
    }

    console.log(`Starting ${model_type} generation with:`, { 
      video_url, 
      audio_file, 
      image_url, 
      keep_resolution 
    });

    let prediction;
    
    if (model_type === 'kling') {
      // Create prediction using Kling lip-sync model
      prediction = await replicate.predictions.create({
        version: "kwaivgi/kling-lip-sync",
        input: {
          video_url: video_url,
          audio_file: audio_file
        }
      });
    } else if (model_type === 'sonic') {
      // Create prediction using Sonic talking face model
      console.log('Creating Sonic prediction with inputs:', {
        audio: audio_file,
        image: image_url,
        keep_resolution: keep_resolution
      });
      
      prediction = await replicate.predictions.create({
        version: "zsxkib/sonic:a2aad29ea95f19747a5ea22ab14fc6594654506e5815f7f5ba4293e888d3e20f",
        input: {
          audio: audio_file,
          image: image_url,
          keep_resolution: keep_resolution
        }
      });
    }

    console.log('Prediction created:', prediction.id);

    // Wait for the prediction to complete
    let result = prediction;
    let attempts = 0;
    const maxAttempts = 3000; // 50 minutes max wait time

    while (result.status !== "succeeded" && result.status !== "failed" && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
      result = await replicate.predictions.get(prediction.id);
      attempts++;
      
      if (attempts % 30 === 0) { // Log every 30 seconds
        console.log(`${model_type} prediction status: ${result.status} (attempt ${attempts})`);
      }
    }

    console.log(`${model_type} prediction completed:`, result.status);

    if (result.status === "succeeded" && result.output) {
      const output = Array.isArray(result.output) ? result.output[0] : result.output;
      console.log(`${model_type} generation successful:`, output);

      return NextResponse.json({
        success: true,
        output: output,
        model_type: model_type
      });
    } else if (result.status === "failed") {
      console.error(`${model_type} prediction failed:`, result.error);
      
      // If Sonic model fails, suggest trying Kling model as fallback
      if (model_type === 'sonic' && result.error && result.error.includes('No such file or directory')) {
        console.log('Sonic model failed, suggesting Kling model as fallback');
        return NextResponse.json({
          success: false,
          error: 'Sonic model processing failed. Please try the Kling model instead, which is more reliable for lip sync generation.',
          suggestion: 'kling_fallback',
          model_type: model_type
        });
      }
      
      // Provide more specific error messages for common issues
      let errorMessage = result.error || 'Unknown error';
      
      if (errorMessage.includes('No such file or directory')) {
        errorMessage = 'Model processing failed - output file not found. This may be due to input file issues or model limitations.';
      } else if (errorMessage.includes('CUDA') || errorMessage.includes('GPU')) {
        errorMessage = 'Model processing failed due to hardware limitations. Please try again.';
      } else if (errorMessage.includes('memory') || errorMessage.includes('RAM')) {
        errorMessage = 'Model processing failed due to memory constraints. Try with smaller files.';
      } else if (errorMessage.includes('timeout')) {
        errorMessage = 'Model processing timed out. Please try again with smaller files.';
      }
      
      throw new Error(`${model_type} prediction failed: ${errorMessage}`);
    } else {
      throw new Error(`${model_type} prediction failed: ${result.error || 'Unknown error'}`);
    }

  } catch (error) {
    console.error('Lip sync generation error:', error);
    
    // Handle specific Replicate errors
    if (error instanceof Error) {
      if (error.message.includes('authentication')) {
        return NextResponse.json(
          { error: 'Invalid Replicate API token. Please check your configuration.' },
          { status: 401 }
        );
      }
      if (error.message.includes('quota') || error.message.includes('limit')) {
        return NextResponse.json(
          { error: 'API quota exceeded. Please try again later.' },
          { status: 429 }
        );
      }
    }

    return NextResponse.json(
      { 
        error: 'Failed to generate lip sync video',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 