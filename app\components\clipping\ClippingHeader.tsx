"use client";
import React from "react";

type Props = {
  title?: string;
  subtitle?: string;
};

export default function ClippingHeader({ title = "Clipping", subtitle = "Create TikToks, <PERSON><PERSON>, Shorts from your long\nvideos in just one click." }: Props) {
  return (
    <div className="mb-2">
      <h1
        className="text-[20px] font-medium mb-1 tracking-[0.01em]"
        style={{ color: "#a78bfa" }}
      >
        {title}
      </h1>
      <p
        className="text-[15px] text-white/70 leading-tight font-normal"
        style={{ marginLeft: 2, marginTop: -2 }}
      >
        {subtitle.split("\n").map((line, idx) => (
          <span key={idx}>
            {line}
            {idx === 0 ? <br /> : null}
          </span>
        ))}
      </p>
    </div>
  );
}