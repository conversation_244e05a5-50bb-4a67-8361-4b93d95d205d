"use client";
import React, { useState } from 'react';

interface VideoTutorial {
  id: string;
  title: string;
  thumbnail: string;
  videoUrl: string;
  duration?: string;
}

const videoTutorials: VideoTutorial[] = [
  {
    id: '1',
    title: 'Getting Started with...',
    thumbnail: '/inspiration/10.png',
    videoUrl: 'https://example.com/video1.mp4',
  },
  {
    id: '2',
    title: 'How to Create Your First AI Video',
    thumbnail: '/inspiration/10.png',
    videoUrl: 'https://example.com/video2.mp4',
  },
  {
    id: '3',
    title: 'Mastering Text-to-Image Gener......',
    thumbnail: '/inspiration/10.png',
    videoUrl: 'https://example.com/video3.mp4',
  },
  {
    id: '4',
    title: 'Using AI Voiceovers for Your...',
    thumbnail: '/inspiration/10.png',
    videoUrl: 'https://example.com/video4.mp4',
  },
  {
    id: '5',
    title: 'Mastering Text-to-Image GProj...',
    thumbnail: '/inspiration/10.png',
    videoUrl: 'https://example.com/video5.mp4',
  },
  {
    id: '6',
    title: 'How to Create Your First AI Video',
    thumbnail: '/inspiration/10.png',
    videoUrl: 'https://example.com/video6.mp4',
  },
  {
    id: '7',
    title: 'Getting Started with...',
    thumbnail: '/inspiration/10.png',
    videoUrl: 'https://example.com/video7.mp4',
  },
  {
    id: '8',
    title: 'How to Create Your First AI Video',
    thumbnail: '/inspiration/10.png',
    videoUrl: 'https://example.com/video8.mp4',
  },
  {
    id: '9',
    title: 'Mastering Text-to-Image Gener......',
    thumbnail: '/inspiration/10.png',
    videoUrl: 'https://example.com/video9.mp4',
  },
];

const HeartIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" stroke="white" strokeWidth="2" fill="none" />
  </svg>
);

const DownloadIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="white" strokeWidth="2" />
    <polyline points="7,10 12,15 17,10" stroke="white" strokeWidth="2" fill="none" />
    <line x1="12" y1="15" x2="12" y2="3" stroke="white" strokeWidth="2" />
  </svg>
);

const PlayIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg">
    <polygon points="5,3 19,12 5,21" fill="white" />
  </svg>
);



// Video Modal Component
interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoUrl: string;
  title: string;
}

const VideoModal: React.FC<VideoModalProps> = ({ isOpen, onClose, videoUrl, title }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 backdrop-blur-sm p-2 sm:p-4">
      <div className="relative bg-[#0f1419] rounded-2xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-y-auto border border-[#2a3441]">
        <button
          className="absolute top-4 right-4 text-[#9ca3af] hover:text-white text-2xl transition-colors z-20 bg-black bg-opacity-50 rounded-full w-8 h-8 flex items-center justify-center"
          onClick={onClose}
          aria-label="Close"
        >
          ×
        </button>
        <div className="p-6">
          <h2 className="text-xl font-semibold text-white mb-4">{title}</h2>
          <div className="aspect-video bg-black rounded-lg overflow-hidden">
            <video
              src={videoUrl}
              controls
              className="w-full h-full"
              autoPlay
            >
              Your browser does not support the video tag.
            </video>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function LearningResources() {
  const [selectedVideo, setSelectedVideo] = useState<VideoTutorial | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('All');

  const handleVideoClick = (video: VideoTutorial) => {
    setSelectedVideo(video);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedVideo(null);
  };

  const tabs = ['All', 'Favourates'];

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="mb-8 p-6 border border-[#FFFFFF] border-opacity-20 rounded-lg">
        <h1 className="text-3xl font-medium text-[#D1D1D1] mb-4">Learning Resources</h1>
        <p className="text-[#F6F6F6] text-lg mb-8 max-w-4xl">Explore tutorials, guides, and tips to master Keoo AI. Whether you're just starting out or looking to sharpen your skills, everything you need to create smarter is right here.</p>

        {/* Tabs */}
        <div className="flex gap-1">
          {tabs.map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-14 py-3 rounded-full text-lg font-medium transition-all ${activeTab === tab
                ? 'bg-[#8B7CF8] bg-opacity-20 text-white border border-[#8B7CF8] border-opacity-30'
                : 'text-[#B8B8B8] hover:text-white hover:bg-[#8B7CF8] hover:bg-opacity-10'
                }`}
            >
              {tab}
            </button>
          ))}
        </div>
      </div>



      {/* Video Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {videoTutorials.map((video) => (
          <div key={video.id} className="group cursor-pointer" onClick={() => handleVideoClick(video)}>
            <div className="relative bg-[#1a1f1e] rounded-lg overflow-hidden border border-[#2a2f2e] hover:border-[#3a3f3e] transition-colors">
              {/* Thumbnail */}
              <div className="relative aspect-video">
                {/* Image Background */}
                <img
                  src={video.thumbnail}
                  alt={video.title}
                  className="absolute inset-0 w-full h-full object-cover"
                />

                {/* Overlay for better contrast */}
                <div className="absolute inset-0 bg-black bg-opacity-20"></div>

                {/* Action Icons */}
                <div className="absolute top-3 right-3 flex gap-2">
                  <button
                    className="p-2 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70 transition-all"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <HeartIcon />
                  </button>
                  <button
                    className="p-2 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70 transition-all"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <DownloadIcon />
                  </button>
                </div>

                {/* Play Button */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <button className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all backdrop-blur-sm">
                    <PlayIcon />
                  </button>
                </div>
              </div>

              {/* Title */}
              <div className="p-4">
                <h3 className="text-white font-medium text-sm leading-tight">
                  {video.title}
                </h3>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Video Modal */}
      {selectedVideo && (
        <VideoModal
          isOpen={isModalOpen}
          onClose={closeModal}
          videoUrl={selectedVideo.videoUrl}
          title={selectedVideo.title}
        />
      )}
    </div>
  );
}
