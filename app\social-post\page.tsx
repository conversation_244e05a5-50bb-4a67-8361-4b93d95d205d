"use client";
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  DashboardLayout,
  Header,
  Sidebar,
} from '../components';
import { useChatHistory } from '../hooks/useChatHistory';
import {
  Inspirationicon,
  IdeaLabicon,
  SocialPosticon,
  Clippingicon,
  Storyboardicon,
  Youtubeicon,
  Keywordicon,
  Accounticon,
  NewPostIcon,
  SchedulerIcon,
  PublishedPostsIcon,
  FailedPostsIcon,
  GeneratebtnIcon
} from '../components/icons/icons';

// Types for social post generation
interface SocialPostResult {
  title: string;
  caption: string;
  imageUrl: string;
  imagePrompt: string;
  extractedText: string;
}

interface GeneratedPost {
  id: string;
  sourceType: string;
  result: SocialPostResult;
  createdAt: Date;
}

// Icon components for better consistency
const IconComponents = {
  inspiration: <Inspirationicon />,
  ideaLab: <IdeaLabicon />,
  socialPost: <SocialPosticon />,
  clipping: <Clippingicon />,
  storyboard: <Storyboardicon />,
  youtube: <Youtubeicon />,
  keyword: <Keywordicon />,
  account: <Accounticon />
};

// Source type buttons data
const sourceTypes = [
  { id: 'text', label: 'Text', active: true },
  { id: 'article', label: 'Article', active: false },
  { id: 'youtube', label: 'YouTube', active: false },
  { id: 'tiktok', label: 'TikTok', active: false },
  { id: 'audio', label: 'Audio', active: false },
  { id: 'document', label: 'Document', active: false },
  { id: 'reels', label: 'Reels', active: false },
];

export default function SocialPost() {
  const [sidebarMenu, setSidebarMenu] = useState([
    { label: "Inspiration", icon: IconComponents.inspiration, active: false },
    { label: "Idea Lab", icon: IconComponents.ideaLab, active: false },
    {
      label: "Social Post",
      icon: IconComponents.socialPost,
      active: true,
      submenu: [
        { label: "New Post", active: true },
        { label: "Scheduler", active: false },
        { label: "Published Posts", active: false },
        { label: "Failed Posts", active: false },
      ]
    },
    { label: "Clipping", icon: IconComponents.clipping, active: false },
    { label: "Storyboard Editor", icon: IconComponents.storyboard, active: false },
    { label: "YouTube Tools", icon: IconComponents.youtube, active: false },
    { label: "Keyword Insights", icon: IconComponents.keyword, active: false },
    { label: "Account", icon: IconComponents.account, active: false },
  ]);

  const router = useRouter();
  const [activeSourceType, setActiveSourceType] = useState('text');
  const [sourceText, setSourceText] = useState('');
  const [sourceUrl, setSourceUrl] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadedFileUrl, setUploadedFileUrl] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPosts, setGeneratedPosts] = useState<GeneratedPost[]>([]);
  const [currentResult, setCurrentResult] = useState<SocialPostResult | null>(null);
  const [activeSubmenu, setActiveSubmenu] = useState('New Post');
  const [error, setError] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);

  // Chat history management for sidebar
  const {
    historySections,
    isLoading,
    clearAllHistory
  } = useChatHistory();

  // Handle menu item clicks with navigation
  const handleMenuItemClick = (item: any, index: number) => {
    // Handle submenu clicks for Social Post
    if (item.label === "New Post" || item.label === "Scheduler" || item.label === "Published Posts" || item.label === "Failed Posts") {
      setActiveSubmenu(item.label);

      // Update the submenu active states
      setSidebarMenu(currentMenu => {
        const socialPostIndex = currentMenu.findIndex(menuItem => menuItem.label === "Social Post");
        if (socialPostIndex === -1) return currentMenu;

        const newMenuItems = [...currentMenu];
        const updatedSubmenu = newMenuItems[socialPostIndex].submenu?.map(subItem => ({
          ...subItem,
          active: subItem.label === item.label
        }));

        newMenuItems[socialPostIndex] = {
          ...newMenuItems[socialPostIndex],
          submenu: updatedSubmenu
        };

        return newMenuItems;
      });
      return;
    }

    const routes: { [key: string]: string } = {
      "Inspiration": "/dashboard",
      "Idea Lab": "/idealab",
      "Social Post": "/social-post",
      "Clipping": "/clipping",
      "Storyboard Editor": "/storyboard",
      "YouTube Tools": "/youtube-tools",
      "Keyword Insights": "/keyword-insights",
      "Account": "/account"
    };

    const route = routes[item.label];
    if (route) {
      router.push(route);
    }
  };

  // Handle history item click to navigate to Idea Lab
  const handleHistoryItemClick = (historyItem: any, _sectionIndex: number, _itemIndex: number) => {
    if (historyItem.sessionId) {
      // Navigate to Idea Lab with the session
      router.push('/idealab');
    }
  };

  // Handle clear history
  const handleClearHistory = () => {
    clearAllHistory();
  };

  // Handle source type change
  const handleSourceTypeChange = (sourceId: string) => {
    setActiveSourceType(sourceId);
    setError('');
    setSourceText('');
    setSourceUrl('');
    setUploadedFile(null);
    setUploadedFileUrl('');
  };

  // Handle file upload
  const handleFileUpload = async (file: File) => {
    try {
      setUploadProgress(0);
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload file');
      }

      const result = await response.json();
      setUploadedFileUrl(result.url);
      setUploadProgress(100);
      setError('');
    } catch (error) {
      console.error('Upload error:', error);
      setError('Failed to upload file. Please try again.');
      setUploadProgress(0);
    }
  };

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      handleFileUpload(file);
    }
  };

  // Handle skip source
  const handleSkipSource = () => {
    setSourceText('');
    setSourceUrl('');
    setUploadedFile(null);
    setUploadedFileUrl('');
    setError('');
    setCurrentResult(null);
  };

  // Handle generate social post
  const handleGeneratePost = async () => {
    try {
      setIsGenerating(true);
      setError('');

      // Validate input based on source type
      let requestData: any = { sourceType: activeSourceType };

      switch (activeSourceType) {
        case 'text':
          if (!sourceText.trim()) {
            throw new Error('Please enter some text');
          }
          requestData.content = sourceText;
          break;
        case 'article':
        case 'youtube':
        case 'tiktok':
        case 'reels':
          if (!sourceUrl.trim()) {
            throw new Error('Please enter a valid URL');
          }
          requestData.url = sourceUrl;
          break;
        case 'audio':
        case 'document':
          if (!uploadedFileUrl) {
            throw new Error('Please upload a file first');
          }
          requestData.fileUrl = uploadedFileUrl;
          requestData.fileType = uploadedFile?.type || '';
          break;
        default:
          throw new Error('Invalid source type');
      }

      const response = await fetch('/api/social-post-automation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate social post');
      }

      const result = await response.json();
      
      if (result.success) {
        const newPost: GeneratedPost = {
          id: Date.now().toString(),
          sourceType: activeSourceType,
          result: result.data,
          createdAt: new Date()
        };
        
        setGeneratedPosts(prev => [newPost, ...prev]);
        setCurrentResult(result.data);
        
        // Clear inputs after successful generation
        setSourceText('');
        setSourceUrl('');
        setUploadedFile(null);
        setUploadedFileUrl('');
      } else {
        throw new Error(result.error || 'Failed to generate social post');
      }
    } catch (error) {
      console.error('Generation error:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate social post');
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle add source (now calls generate)
  const handleAddSource = () => {
    handleGeneratePost();
  };

  // Render input area based on source type
  const renderInputArea = () => {
    switch (activeSourceType) {
      case 'text':
        return (
          <textarea
            value={sourceText}
            onChange={(e) => setSourceText(e.target.value)}
            placeholder="Paste the text here"
            className="w-full h-80 bg-[#1a1a2e] border border-[#2a2a3e] rounded-xl p-6 text-white text-base resize-none focus:outline-none focus:border-[#4a4a6e] placeholder-[#6b7280] transition-colors"
            style={{
              fontFamily: 'var(--font-geist-sans), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
            }}
          />
        );
      case 'article':
        return (
          <input
            type="url"
            value={sourceUrl}
            onChange={(e) => setSourceUrl(e.target.value)}
            placeholder="Enter article URL (e.g., https://example.com/article)"
            className="w-full h-16 bg-[#1a1a2e] border border-[#2a2a3e] rounded-xl px-6 text-white text-base focus:outline-none focus:border-[#4a4a6e] placeholder-[#6b7280] transition-colors"
          />
        );
      case 'youtube':
        return (
          <input
            type="url"
            value={sourceUrl}
            onChange={(e) => setSourceUrl(e.target.value)}
            placeholder="Enter YouTube URL (e.g., https://youtube.com/watch?v=...)"
            className="w-full h-16 bg-[#1a1a2e] border border-[#2a2a3e] rounded-xl px-6 text-white text-base focus:outline-none focus:border-[#4a4a6e] placeholder-[#6b7280] transition-colors"
          />
        );
      case 'tiktok':
        return (
          <input
            type="url"
            value={sourceUrl}
            onChange={(e) => setSourceUrl(e.target.value)}
            placeholder="Enter TikTok URL (e.g., https://tiktok.com/@user/video/...)"
            className="w-full h-16 bg-[#1a1a2e] border border-[#2a2a3e] rounded-xl px-6 text-white text-base focus:outline-none focus:border-[#4a4a6e] placeholder-[#6b7280] transition-colors"
          />
        );
      case 'reels':
        return (
          <input
            type="url"
            value={sourceUrl}
            onChange={(e) => setSourceUrl(e.target.value)}
            placeholder="Enter Reels URL (e.g., https://www.instagram.com/reel/...)"
            className="w-full h-16 bg-[#1a1a2e] border border-[#2a2a3e] rounded-xl px-6 text-white text-base focus:outline-none focus:border-[#4a4a6e] placeholder-[#6b7280] transition-colors"
          />
        );
      case 'audio':
        return (
          <div className="w-full">
            <div className="border-2 border-dashed border-[#2a2a3e] rounded-xl p-8 text-center hover:border-[#4a4a6e] transition-colors">
              <input
                type="file"
                accept="audio/*"
                onChange={handleFileChange}
                className="hidden"
                id="audio-upload"
              />
              <label htmlFor="audio-upload" className="cursor-pointer">
                <div className="text-[#9ca3af] mb-4">
                  <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                  </svg>
                </div>
                <p className="text-white font-medium mb-2">Upload Audio File</p>
                <p className="text-[#6b7280] text-sm">MP3, WAV, or other audio formats</p>
              </label>
              {uploadedFile && (
                <div className="mt-4 p-3 bg-[#2a2a3a] rounded-lg">
                  <p className="text-white text-sm">{uploadedFile.name}</p>
                  {uploadProgress > 0 && uploadProgress < 100 && (
                    <div className="w-full bg-[#1a1a2e] rounded-full h-2 mt-2">
                      <div className="bg-[#887DFF] h-2 rounded-full transition-all duration-300" style={{ width: `${uploadProgress}%` }}></div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        );
      case 'document':
        return (
          <div className="w-full">
            <div className="border-2 border-dashed border-[#2a2a3e] rounded-xl p-8 text-center hover:border-[#4a4a6e] transition-colors">
              <input
                type="file"
                accept=".pdf,.doc,.docx"
                onChange={handleFileChange}
                className="hidden"
                id="document-upload"
              />
              <label htmlFor="document-upload" className="cursor-pointer">
                <div className="text-[#9ca3af] mb-4">
                  <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <p className="text-white font-medium mb-2">Upload Document</p>
                <p className="text-[#6b7280] text-sm">PDF, DOC, or DOCX files</p>
              </label>
              {uploadedFile && (
                <div className="mt-4 p-3 bg-[#2a2a3a] rounded-lg">
                  <p className="text-white text-sm">{uploadedFile.name}</p>
                  {uploadProgress > 0 && uploadProgress < 100 && (
                    <div className="w-full bg-[#1a1a2e] rounded-full h-2 mt-2">
                      <div className="bg-[#887DFF] h-2 rounded-full transition-all duration-300" style={{ width: `${uploadProgress}%` }}></div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  // Render content based on active submenu
  const renderContent = () => {
    switch (activeSubmenu) {
      case 'New Post':
        return (
          <div className="max-w-6xl mx-auto px-6 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Input */}
              <div>
                {/* Header Section */}
                <div className="mb-8">
                  <h1 className="text-2xl font-semibold text-[#887DFF] mb-2">Add source</h1>
                  <p className="text-[#9ca3af] text-sm">Add your source to generate posts.</p>
                </div>

                {/* Source Type Buttons */}
                <div className="flex flex-wrap gap-3 mb-8">
                  {sourceTypes.map((source) => (
                    <button
                      key={source.id}
                      onClick={() => handleSourceTypeChange(source.id)}
                      className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-200 ${
                        activeSourceType === source.id
                          ? 'bg-[#2a2a3a] text-white border border-[#4a4a6e]'
                          : 'bg-transparent text-[#9ca3af] border border-transparent hover:border-[#4a4a6e] hover:text-white'
                      }`}
                    >
                      {source.label}
                    </button>
                  ))}
                </div>

                {/* Dynamic Input Area */}
                <div className="mb-6">
                  {renderInputArea()}
                </div>

                {/* Error Message */}
                {error && (
                  <div className="mb-6 p-4 bg-red-900/20 border border-red-500/30 rounded-xl">
                    <p className="text-red-400 text-sm">{error}</p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex justify-end gap-4">
                  <button
                    onClick={handleSkipSource}
                    className="px-6 py-3 rounded-full text-white text-sm font-medium border border-[#4a4a6e] bg-transparent hover:bg-[#1a1a2e] transition-colors"
                    disabled={isGenerating}
                  >
                    Clear
                  </button>
                  <button
                    onClick={handleAddSource}
                    disabled={isGenerating}
                    className="px-6 py-3 rounded-full text-white text-sm font-medium bg-[#887DFF] hover:bg-[#5855eb] transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isGenerating ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Generating...
                      </>
                    ) : (
                      <>
                        <GeneratebtnIcon />
                        Generate Post
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Right Column - Results */}
              <div>
                {currentResult ? (
                  <div className="bg-[#1a1a2e] border border-[#2a2a3e] rounded-xl p-6">
                    <h2 className="text-xl font-semibold text-white mb-6">Generated Post</h2>
                    
                    {/* Generated Image */}
                    {currentResult.imageUrl && (
                      <div className="mb-6">
                        <img
                          src={currentResult.imageUrl}
                          alt="Generated post image"
                          className="w-full h-64 object-cover rounded-lg"
                        />
                      </div>
                    )}
                    
                    {/* Generated Title */}
                    <div className="mb-4">
                      <label className="block text-[#9ca3af] text-sm font-medium mb-2">Title</label>
                      <div className="bg-[#0a0f0d] border border-[#2a2a3e] rounded-lg p-4">
                        <p className="text-white">{currentResult.title}</p>
                      </div>
                    </div>
                    
                    {/* Generated Caption */}
                    <div className="mb-4">
                      <label className="block text-[#9ca3af] text-sm font-medium mb-2">Caption</label>
                      <div className="bg-[#0a0f0d] border border-[#2a2a3e] rounded-lg p-4">
                        <p className="text-white whitespace-pre-wrap">{currentResult.caption}</p>
                      </div>
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex gap-3">
                      <button className="flex-1 px-4 py-2 bg-[#887DFF] hover:bg-[#5855eb] text-white rounded-lg transition-colors">
                        Copy All
                      </button>
                      <button className="flex-1 px-4 py-2 border border-[#4a4a6e] text-white rounded-lg hover:bg-[#2a2a3a] transition-colors">
                        Save Post
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="bg-[#1a1a2e] border border-[#2a2a3e] rounded-xl p-6 text-center">
                    <div className="text-[#6b7280] mb-4">
                      <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    </div>
                    <h3 className="text-white font-medium mb-2">No Post Generated Yet</h3>
                    <p className="text-[#9ca3af] text-sm">Add your content source and click "Generate Post" to create your social media content.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      case 'Scheduler':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="text-center py-20">
              <SchedulerIcon />
              <h2 className="text-xl font-semibold text-white mb-2 mt-4">Scheduler</h2>
              <p className="text-[#9ca3af]">Schedule your posts for optimal engagement.</p>
            </div>
          </div>
        );
      case 'Published Posts':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="text-center py-20">
              <PublishedPostsIcon />
              <h2 className="text-xl font-semibold text-white mb-2 mt-4">Published Posts</h2>
              <p className="text-[#9ca3af]">View your successfully published posts.</p>
            </div>
          </div>
        );
      case 'Failed Posts':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="text-center py-20">
              <FailedPostsIcon />
              <h2 className="text-xl font-semibold text-white mb-2 mt-4">Failed Posts</h2>
              <p className="text-[#9ca3af]">Review posts that failed to publish.</p>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <DashboardLayout
      header={<Header />}
      sidebar={
        <Sidebar
          menuItems={sidebarMenu}
          histories={historySections}
          isLoading={isLoading}
          onMenuItemClick={handleMenuItemClick}
          onHistoryItemClick={handleHistoryItemClick}
          onClearHistory={handleClearHistory}
        />
      }
    >
      <div className="flex-1 overflow-auto bg-[#0a0f0d]">
        {renderContent()}
      </div>
    </DashboardLayout>
  );
}
