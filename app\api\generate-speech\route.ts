import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';
import cloudinary from '../../../lib/cloudinary';

// Helper function to upload audio to Cloudinary
async function uploadToCloudinary(dataUrl: string): Promise<string> {
  try {
    const result = await cloudinary.uploader.upload(dataUrl, {
      resource_type: 'video', // Audio is treated as video in Cloudinary
      folder: 'ai-generated-speech', // Optional: organize in a folder
    });
    return result.secure_url;
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw new Error('Failed to upload audio to Cloudinary');
  }
}

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function POST(req: NextRequest) {
  try {
    const { text, voice, speed } = await req.json();

    if (!text) {
      return NextResponse.json(
        { error: "Text is required" },
        { status: 400 }
      );
    }

    // Validate text length (reasonable limit for TTS)
    if (text.length > 2000) {
      return NextResponse.json(
        { error: "Text is too long. Maximum 2000 characters allowed." },
        { status: 400 }
      );
    }

    // Validate speed range according to official schema
    if (speed && (speed < 0.1 || speed > 5.0)) {
      return NextResponse.json(
        { error: "Speed must be between 0.1 and 5.0" },
        { status: 400 }
      );
    }

    // Prepare input for Kokoro-82m model according to official schema
    const input: any = {
      text: text,
      voice: voice || "af_bella", // Default voice
      speed: speed || 1.0 // Default speed (0.1 to 5.0 range)
    };

    console.log(`Generating speech with Kokoro-82m model`);
    console.log(`Input parameters:`, input);

    // Use the Kokoro-82m model from Replicate with the specific version
    const output = await replicate.run("jaaari/kokoro-82m:f559560eb822dc509045f3921a1921234918b91739db4bf3daab2169b71c7a13", { input });

    console.log('Raw Replicate output:', output);
    console.log('Output type:', typeof output);

    let audioUrl: string | null = null;

    // Handle different output formats from Replicate
    if (typeof output === 'string') {
      // Direct URL string
      audioUrl = output;
    } else if (output && typeof output === 'object' && Symbol.asyncIterator in output) {
      // Handle ReadableStream (binary data)
      console.log('Output is a ReadableStream, collecting binary data...');
      const chunks: Uint8Array[] = [];

      try {
        for await (const chunk of output as AsyncIterable<Uint8Array>) {
          if (chunk instanceof Uint8Array) {
            chunks.push(chunk);
          }
        }

        console.log(`Collected ${chunks.length} chunks of binary data`);

        if (chunks.length > 0) {
          // Combine all chunks into a single buffer
          const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
          const combinedBuffer = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of chunks) {
            combinedBuffer.set(chunk, offset);
            offset += chunk.length;
          }

          // Convert to base64 data URL for audio
          const base64String = Buffer.from(combinedBuffer).toString('base64');
          const dataUrl = `data:audio/wav;base64,${base64String}`;
          const cloudinaryUrl = await uploadToCloudinary(dataUrl);
          audioUrl = cloudinaryUrl;

          console.log('Successfully created data URL from binary data');
        }

      } catch (e) {
        console.error('Error reading ReadableStream:', e);
        return NextResponse.json(
          {
            error: "Failed to process audio stream",
            details: "Error reading binary audio data from the model"
          },
          { status: 500 }
        );
      }
    } else if (output && typeof output === 'object') {
      // Handle object response (try to extract URL)
      const obj = output as any;
      if (obj.url) {
        audioUrl = obj.url;
      } else if (obj.audio) {
        audioUrl = obj.audio;
      } else if (obj.output) {
        audioUrl = obj.output;
      } else {
        console.error('Unexpected object format:', obj);
        return NextResponse.json(
          {
            error: "Unexpected output format",
            details: "The model returned an unexpected object format",
            rawOutput: output
          },
          { status: 500 }
        );
      }
    } else {
      console.error('Unexpected output format. Got:', typeof output, output);
      return NextResponse.json(
        {
          error: "Unexpected output format",
          details: "The model returned an unexpected output format",
          rawOutput: output
        },
        { status: 500 }
      );
    }

    console.log('Processed audio URL/data:', audioUrl ? 'Generated successfully' : 'Failed');

    // Validate that we have valid audio data
    if (!audioUrl) {
      console.error('No valid audio data found in output:', output);
      return NextResponse.json(
        {
          error: "No audio generated",
          details: "The model did not return valid audio data",
          rawOutput: output
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      audioUrl: audioUrl,
      model: "kokoro-82m",
      text: text,
      voice: voice || "af_bella",
      speed: speed || 1.0,
      parameters: {
        text: input.text,
        voice: input.voice,
        speed: input.speed
      }
    });

  } catch (error: any) {
    console.error("Speech generation error:", error);
    
    // Handle specific Replicate errors
    if (error.message?.includes('authentication')) {
      return NextResponse.json(
        { error: "Authentication failed. Please check API token." },
        { status: 401 }
      );
    }
    
    if (error.message?.includes('rate limit')) {
      return NextResponse.json(
        { error: "Rate limit exceeded. Please try again later." },
        { status: 429 }
      );
    }

    return NextResponse.json(
      { 
        error: "Failed to generate speech", 
        details: error.message || "Unknown error occurred"
      },
      { status: 500 }
    );
  }
}
