import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';
import cloudinary from '../../../lib/cloudinary';

// Helper function to upload image to Cloudinary
async function uploadToCloudinary(dataUrl: string): Promise<string> {
  try {
    const result = await cloudinary.uploader.upload(dataUrl, {
      folder: 'ai-generated-images', // Optional: organize in a folder
    });
    return result.secure_url;
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw new Error('Failed to upload image to Cloudinary');
  }
}

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function POST(req: NextRequest) {
  try {
    const { prompt, model, style, ratio, mode, imageUrl, num_outputs } = await req.json();

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    // Validate image-to-image mode requirements
    if (mode === 'image-to-image' && !imageUrl) {
      return NextResponse.json(
        { error: "Image URL is required for image-to-image mode" },
        { status: 400 }
      );
    }

    // Validate aspect ratio - Flux 1.1 Pro Ultra supports specific ratios
    const validAspectRatios = ["1:1", "16:9", "9:16", "4:3", "3:2", "21:9"];
    const aspectRatio = validAspectRatios.includes(ratio) ? ratio : "1:1";

    // Enhance prompt with style
    const enhancedPrompt = style && style !== "Photorealistic"
      ? `${prompt}, ${style.toLowerCase()} style`
      : prompt;

    // Base input parameters for Flux 1.1 Pro Ultra
    let input: any = {
      prompt: enhancedPrompt,
      aspect_ratio: aspectRatio,
      raw: style === "Photorealistic", // Use raw mode for photorealistic style
      safety_tolerance: 2, // 0-6, where 0 is most strict, 6 is least strict
      output_format: "jpg", // jpg or png
      seed: Math.floor(Math.random() * 1000000),
      ...(num_outputs ? { num_outputs: Math.min(Math.max(num_outputs, 1), 4) } : {})
    };

    // Handle image-to-image mode
    if (mode === 'image-to-image' && imageUrl) {
      // For image-to-image, we'll use flux-dev which supports this mode
      // Flux 1.1 Pro Ultra is primarily for text-to-image
      switch (model) {
        case "flux-dev-lora":
        case "flux-kontext-max":
          // These models can handle image-to-image
          input.image = imageUrl;
          input.prompt_strength = 0.8; // How much to transform the input image
          break;
        default:
          // For flux-1.1-pro-ultra, we'll fall back to flux-dev for img2img
          input.image = imageUrl;
          input.prompt_strength = 0.8;
          break;
      }
    }

    // Select the appropriate model
    let modelName: `${string}/${string}` = "black-forest-labs/flux-1.1-pro-ultra";

    if (mode === 'image-to-image') {
      // For image-to-image mode, use flux-dev which supports it better
      switch (model) {
        case "flux-dev-lora":
          modelName = "black-forest-labs/flux-dev";
          break;
        case "flux-kontext-max":
          modelName = "black-forest-labs/flux-1.1-pro";
          break;
        default:
          // Fall back to flux-dev for image-to-image
          modelName = "black-forest-labs/flux-dev";
          break;
      }
    } else {
      // For text-to-image mode
      switch (model) {
        case "flux-1.1-pro-ultra":
          modelName = "black-forest-labs/flux-1.1-pro-ultra";
          break;
        case "flux-dev-lora":
          modelName = "black-forest-labs/flux-dev";
          break;
        case "flux-kontext-max":
          modelName = "black-forest-labs/flux-1.1-pro";
          break;
        default:
          modelName = "black-forest-labs/flux-1.1-pro-ultra";
      }
    }

    console.log(`Generating image with model: ${modelName}`);
    console.log(`Input parameters:`, input);

    const output = await replicate.run(modelName, { input });

    console.log('Raw Replicate output:', output);
    console.log('Output type:', typeof output);
    console.log('Is array:', Array.isArray(output));

    // Handle different output formats from Replicate
    let imageUrls: string[] = [];

    // Check if output is an async iterable (common for Replicate streaming)
    if (output && typeof output === 'object' && Symbol.asyncIterator in output) {
      console.log('Output is an async iterable, collecting binary data...');
      const chunks: Uint8Array[] = [];

      try {
        for await (const chunk of output as AsyncIterable<Uint8Array>) {
          if (chunk instanceof Uint8Array) {
            chunks.push(chunk);
          }
        }

        console.log(`Collected ${chunks.length} chunks of binary data`);

        if (chunks.length > 0) {
          // Combine all chunks into a single buffer
          const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
          const combinedBuffer = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of chunks) {
            combinedBuffer.set(chunk, offset);
            offset += chunk.length;
          }

          // Convert to base64 data URL
          const base64String = Buffer.from(combinedBuffer).toString('base64');
          const dataUrl = `data:image/jpeg;base64,${base64String}`;
          const cloudinaryUrl = await uploadToCloudinary(dataUrl);
          imageUrls = [cloudinaryUrl];

          console.log('Successfully created data URL from binary data');
        }

      } catch (e) {
        console.error('Error reading async iterator:', e);
      }
    } else if (Array.isArray(output)) {
      console.log('Output is an array, processing each item...');

      for (const item of output) {
        if (typeof item === 'string') {
          imageUrls.push(item);
        } else if (item && typeof item === 'object' && Symbol.asyncIterator in item) {
          // Handle ReadableStream in array (common for image-to-image)
          console.log('Found ReadableStream in array, collecting binary data...');
          const chunks: Uint8Array[] = [];

          try {
            for await (const chunk of item as AsyncIterable<Uint8Array>) {
              if (chunk instanceof Uint8Array) {
                chunks.push(chunk);
              }
            }

            console.log(`Collected ${chunks.length} chunks from array item`);

            if (chunks.length > 0) {
              // Combine all chunks into a single buffer
              const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
              const combinedBuffer = new Uint8Array(totalLength);
              let offset = 0;

              for (const chunk of chunks) {
                combinedBuffer.set(chunk, offset);
                offset += chunk.length;
              }

              // Convert to base64 data URL
              const base64String = Buffer.from(combinedBuffer).toString('base64');
              const dataUrl = `data:image/jpeg;base64,${base64String}`;
              const cloudinaryUrl = await uploadToCloudinary(dataUrl);
              imageUrls.push(cloudinaryUrl);

              console.log('Successfully created data URL from array item binary data');
            }
          } catch (e) {
            console.error('Error reading async iterator from array item:', e);
          }
        } else if (item && typeof item === 'object') {
          const obj = item as any;
          if (typeof obj.url === 'string') {
            imageUrls.push(obj.url);
          } else if (typeof obj.image === 'string') {
            imageUrls.push(obj.image);
          }
        }
      }
    } else if (typeof output === 'string') {
      // If output is a single string URL
      imageUrls = [output];
    } else if (output && typeof output === 'object') {
      // If output is an object, try to extract URL
      const obj = output as any;
      if (obj.url) {
        imageUrls = [obj.url];
      } else if (obj.image) {
        imageUrls = [obj.image];
      } else if (obj.images && Array.isArray(obj.images)) {
        imageUrls = obj.images.filter(Boolean);
      }
    }

    console.log('Processed image URLs:', imageUrls);

    // Validate that we have at least one valid image URL
    if (!imageUrls || imageUrls.length === 0) {
      console.error('No valid image URLs found in output:', output);
      return NextResponse.json(
        {
          error: "No images generated",
          details: "The model did not return any valid image URLs",
          rawOutput: output
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      images: imageUrls,
      model: modelName,
      prompt: enhancedPrompt,
      mode: mode,
      aspectRatio: aspectRatio,
      style: style,
      parameters: {
        raw: input.raw,
        safety_tolerance: input.safety_tolerance,
        output_format: input.output_format,
        seed: input.seed,
        ...(mode === 'image-to-image' && { prompt_strength: input.prompt_strength })
      }
    });

  } catch (error: any) {
    console.error("Image generation error:", error);
    
    // Handle specific Replicate errors
    if (error.message?.includes('authentication')) {
      return NextResponse.json(
        { error: "Authentication failed. Please check API token." },
        { status: 401 }
      );
    }
    
    if (error.message?.includes('rate limit')) {
      return NextResponse.json(
        { error: "Rate limit exceeded. Please try again later." },
        { status: 429 }
      );
    }

    return NextResponse.json(
      { 
        error: "Failed to generate image", 
        details: error.message || "Unknown error occurred"
      },
      { status: 500 }
    );
  }
}
