"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    setLoading(true);
    const res = await fetch("/api/auth/forgot-password", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email }),
    });
    setLoading(false);
    const data = await res.json();
    if (res.ok) {
      setSuccess("Password reset email sent! Please check your inbox.");
    } else {
      setError(data.error || "Something went wrong");
    }
  };

  return (
    <div className="min-h-screen flex bg-[#18182a]">
      {/* Left image section */}
      <div className="hidden md:flex w-1/2 items-center justify-center relative">
        <Image src="/auth-img.gif" alt="Keoo Art" fill className="object-cover opacity-80" />
        <div className="absolute top-8 left-8">
          <Image src="/logo_main2.png" alt="Keoo Logo" width={128} height={128} />
        </div>
      </div>
      {/* Right form section */}
      <div className="flex w-full md:w-1/2 min-h-screen items-center justify-center">
        <div className="bg-[#23233a] p-10 rounded-2xl shadow-xl w-full max-w-md border border-[#7c5cff]/30">
          <div className="mb-8">
            <div className="text-[#b3b3c6] text-sm mb-1">Forgot your password?</div>
            <h2 className="text-2xl font-bold text-white mb-2">Reset your password</h2>
          </div>
          <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
            <div>
              <label className="block text-[#b3b3c6] text-sm mb-1">Email address *</label>
              <input
                type="email"
                className="w-full px-4 py-3 rounded-xl bg-[#18182a] border border-[#35355a] text-white placeholder-[#b3b3c6] focus:outline-none focus:ring-2 focus:ring-[#7c5cff] transition"
                placeholder="<EMAIL>"
                value={email}
                onChange={e => setEmail(e.target.value)}
                autoComplete="email"
                required
              />
            </div>
            {error && <div className="text-red-400 text-sm text-center">{error}</div>}
            {success && <div className="text-green-400 text-sm text-center">{success}</div>}
            <button
              type="submit"
              className="w-full mt-2 py-3 rounded-xl bg-[#7c5cff] hover:bg-[#6a4eea] transition text-white font-semibold text-lg shadow-md disabled:opacity-60"
              disabled={loading}
            >
              {loading ? "Please wait..." : "Send reset link"}
            </button>
          </form>
          <div className="mt-6 text-center text-[#b3b3c6] text-sm">
            <button onClick={() => router.push("/login")}
              className="text-[#7c5cff] hover:underline font-medium">
              Back to login
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 