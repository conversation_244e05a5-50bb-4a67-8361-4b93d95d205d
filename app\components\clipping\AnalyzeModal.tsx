"use client";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { GeneratebtnIcon } from "../icons/icons";

type Props = {
  open: boolean;
  onClose: () => void;
};

export default function AnalyzeModal({ open, onClose }: Props) {
  const dialogRef = useRef<HTMLDivElement>(null);

  // Control local UI state (Hooks must be declared unconditionally and before any early returns)
  const [duration, setDuration] = useState("1 min");
  const [autoDuration, setAutoDuration] = useState(true);
  const [language, setLanguage] = useState("English");
  const [reframe, setReframe] = useState(true);
  const [dimensions, setDimensions] = useState("Portrait");
  const [hook, setHook] = useState(false);
  const [captions, setCaptions] = useState(false);
  const [emojis, setEmojis] = useState(true);

  // Basic focus trap and Esc handling
  useEffect(() => {
    if (!open) return;
    const previouslyFocused = document.activeElement as HTMLElement | null;
    const handleKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
      if (e.key === "Tab") {
        const focusable = dialogRef.current?.querySelectorAll<HTMLElement>(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        if (!focusable || focusable.length === 0) return;
        const first = focusable[0];
        const last = focusable[focusable.length - 1];
        if (e.shiftKey && document.activeElement === first) {
          e.preventDefault();
          last.focus();
        } else if (!e.shiftKey && document.activeElement === last) {
          e.preventDefault();
          first.focus();
        }
      }
    };
    document.addEventListener("keydown", handleKey);
    // focus first focusable
    setTimeout(() => {
      dialogRef.current?.querySelector<HTMLElement>("button, input, select")?.focus();
    }, 0);
    return () => {
      document.removeEventListener("keydown", handleKey);
      previouslyFocused?.focus();
    };
  }, [open, onClose]);

  if (!open) return null;

  return (
    <div
      aria-modal="true"
      role="dialog"
      className="fixed inset-0 z-[100] flex items-center justify-center"
    >
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/70"
        onClick={onClose}
        aria-hidden
      />

      {/* Scroll container to allow content overflow */}
      <div className="absolute inset-0 overflow-y-auto">
        {/* Centering wrapper to keep the card centered while allowing scroll */}
        <div className="min-h-full flex items-start justify-center px-3 py-6 sm:px-6 sm:py-10">
          {/* Modal Card */}
          <div
            ref={dialogRef}
            className="relative w-[92vw] max-w-[720px] rounded-[22px] border border-[#273044] bg-[#0a1311] p-5 sm:p-6 md:p-8 shadow-[0_20px_60px_rgba(0,0,0,0.5)]"
            style={{ boxShadow: "0 0 0 1px #273044 inset" }}
          >
            {/* Close */}
            <button
              onClick={onClose}
              className="absolute right-3 top-3 h-8 w-8 rounded-full bg-[#121a18] border border-[#273044] text-white/70 hover:text-white"
              aria-label="Close"
            >
              ×
            </button>

        {/* Video Preview */}
        {/* <div className="rounded-xl border border-[#2e3a52] p-2 bg-[#0d1715]">
          <div className="relative h-[180px] sm:h-[220px] md:h-[260px] w-full overflow-hidden rounded-lg">
            <Image
              src="/inspiration/8.jpg"
              alt="Video preview"
              fill
              className="object-cover opacity-90"
              priority
            />
          </div>
        </div> */}

        {/* Subtitle */}
        <p className="mt-4 text-center text-white/80">
          Create up to 10 shorts from your video
        </p>

        {/* Style carousel mock */}
        {/*
        <div className="mt-5 rounded-2xl border border-[#2e3a52] bg-[#19182A] p-4">
          <div className="text-[#a78bfa] mb-3">Present</div>
          <div className="relative">
            <button
              className="absolute left-0 top-1/2 -translate-y-1/2 h-8 w-8 rounded-full bg-[#1b1531] border border-[#433d6b] text-white/80"
              aria-label="Previous styles"
            >
              ‹
            </button>
            <div className="mx-10 flex gap-3 overflow-hidden">
              {Array.from({ length: 7 }).map((_, i) => (
                <div
                  key={i}
                  className="h-16 w-16 shrink-0 overflow-hidden rounded-lg border border-[#433d6b] bg-[#0f0e1c]"
                >
                   <Image
                    src={`/inspiration/${(i % 6) + 1}.jpg`}
                    alt={`Style ${i + 1}`}
                    width={64}
                    height={64}
                    className="h-full w-full object-cover opacity-80"
                  />
                </div>
              ))}
            </div>
            <button
              className="absolute right-0 top-1/2 -translate-y-1/2 h-8 w-8 rounded-full bg-[#1b1531] border border-[#433d6b] text-white/80"
              aria-label="Next styles"
            >
              ›
            </button>
          </div>
        </div>
        */}

        {/* Controls */}
        <div className="mt-5 space-y-4">
          {/* Duration */}
          <div className="rounded-2xl border border-[#2e3a52] bg-[#19182A] p-4">
            <div className="flex items-center justify-between">
              <label className="text-white">Duration</label>
              <div className="flex items-center gap-2">
                {/*
                <span className="rounded-full border border-[#433d6b] bg-black/40 px-3 py-1 text-white/90 text-sm">
                  {duration}
                </span>
                */}
                <button
                  onClick={() => setAutoDuration((v) => !v)}
                  className={`rounded-md border border-[#433d6b] px-3 py-1 text-sm ${
                    autoDuration ? "bg-[#28214e] text-white" : "bg-black text-white/80"
                  }`}
                >
                  Auto
                </button>
              </div>
            </div>
          </div>

          {/* Language */}
          <div className="rounded-2xl border border-[#2e3a52] bg-[#19182A] p-4">
            <div className="flex items-center justify-between">
              <label className="text-white">Language</label>
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value)}
                className="rounded-md border border-[#433d6b] bg-black px-3 py-1 text-white"
              >
                <option>English</option>
                <option>Spanish</option>
                <option>French</option>
              </select>
            </div>
          </div>

          {/* Reframe / Dimensions */}
          <div className="rounded-2xl border border-[#2e3a52] bg-[#19182A] p-4">
            <div className="flex items-center justify-between">
              <label className="text-white">Reframe</label>
              <button
                onClick={() => setReframe((v) => !v)}
                className={`h-6 w-12 rounded-full border border-[#433d6b] p-0.5 ${
                  reframe ? "bg-[#7f6bf7]" : "bg-[#0f0e1c]"
                }`}
                aria-pressed={reframe}
              >
                <span
                  className={`block h-5 w-5 rounded-full bg-white transition-transform ${
                    reframe ? "translate-x-6" : "translate-x-0"
                  }`}
                />
              </button>
            </div>
            <div className="mt-4 flex items-center justify-between">
              <label className="text-white">Dimensions</label>
              <select
                value={dimensions}
                onChange={(e) => setDimensions(e.target.value)}
                className="rounded-md border border-[#433d6b] bg-black px-3 py-1 text-white"
              >
                <option>Portrait</option>
                <option>Landscape</option>
                <option>Square</option>
              </select>
            </div>
          </div>

          {/* Hook */}
          <div className="rounded-2xl border border-[#2e3a52] bg-[#19182A] p-4">
            <div className="flex items-center justify-between">
              <label className="text-white">Hook</label>
              <button
                onClick={() => setHook((v) => !v)}
                className={`h-6 w-12 rounded-full border border-[#433d6b] p-0.5 ${
                  hook ? "bg-[#7f6bf7]" : "bg-[#0f0e1c]"
                }`}
                aria-pressed={hook}
              >
                <span
                  className={`block h-5 w-5 rounded-full bg-white transition-transform ${
                    hook ? "translate-x-6" : "translate-x-0"
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Captions / Emojis */}
          <div className="rounded-2xl border border-[#2e3a52] bg-[#19182A] p-4">
            <div className="flex items-center justify-between">
              <label className="text-white">Captions</label>
              <button
                onClick={() => setCaptions((v) => !v)}
                className={`h-6 w-12 rounded-full border border-[#433d6b] p-0.5 ${
                  captions ? "bg-[#7f6bf7]" : "bg-[#0f0e1c]"
                }`}
                aria-pressed={captions}
              >
                <span
                  className={`block h-5 w-5 rounded-full bg-white transition-transform ${
                    captions ? "translate-x-6" : "translate-x-0"
                  }`}
                />
              </button>
            </div>
            <div className="mt-4 flex items-center justify-between">
              <label className="text-white">Emojis</label>
              <button
                onClick={() => setEmojis((v) => !v)}
                className={`h-6 w-12 rounded-full border border-[#433d6b] p-0.5 ${
                  emojis ? "bg-[#7f6bf7]" : "bg-[#0f0e1c]"
                }`}
                aria-pressed={emojis}
              >
                <span
                  className={`block h-5 w-5 rounded-full bg-white transition-transform ${
                    emojis ? "translate-x-6" : "translate-x-0"
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

            {/* Footer CTA */}
            <div className="mt-6">
              <button
                type="button"
                className="w-full h-14 rounded-full text-white text-[16px] font-medium flex items-center justify-center"
                style={{
                  background: "linear-gradient(90deg,#8b7bfa 0%,#a78bfa 100%)",
                  boxShadow: "0 0 18px 2px #a78bfa55",
                }}
                onClick={async () => {
                  const reqId = `ui-create-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
                  try {
                    // Collect UI -> API payload mapping
                    const payload = {
                      videoUrl: (document.querySelector('input[aria-label="Your Video"]') as HTMLInputElement)?.value || "",
                      langLabel: language, // English/Spanish/French -> mapped server-side
                      autoDuration: autoDuration,
                      durationLabel: duration, // mapped server-side
                      dimensionsLabel: dimensions, // Portrait/Landscape/Square -> mapped server-side
                      hook: hook,
                      captions: captions,
                      emojis: emojis,
                      removeSilence: false, // no explicit toggle in UI yet
                      maxClipNumber: 10,
                      keywords: undefined,
                      projectName: undefined,
                      templateId: undefined,
                      highlight: false,
                    };

                    // Basic client validation
                    if (!payload.videoUrl) {
                      console.warn(`[UI][CREATE][${reqId}] Missing videoUrl`);
                      alert("Please provide a video URL in the input bar on the main page.");
                      return;
                    }

                    console.log(`[UI][CREATE][${reqId}] Payload=`, {
                      hasVideoUrl: !!payload.videoUrl,
                      langLabel: payload.langLabel,
                      autoDuration: payload.autoDuration,
                      durationLabel: payload.durationLabel,
                      dimensionsLabel: payload.dimensionsLabel,
                      hook: payload.hook,
                      captions: payload.captions,
                      emojis: payload.emojis,
                      removeSilence: payload.removeSilence,
                      maxClipNumber: payload.maxClipNumber,
                      hasKeywords: false,
                      hasProjectName: false,
                      templateId: payload.templateId,
                      highlight: payload.highlight,
                    });

                    const started = Date.now();
                    const res = await fetch("/api/vizard/create-project", {
                      method: "POST",
                      headers: { "content-type": "application/json" },
                      body: JSON.stringify(payload),
                    }).catch((e) => {
                      console.error(`[UI][CREATE][${reqId}] Fetch exception:`, e);
                      throw e;
                    });

                    const durationMs = Date.now() - started;
                    let json: any = null;
                    try {
                      json = await res.json();
                    } catch (e) {
                      console.error(`[UI][CREATE][${reqId}] Failed to parse JSON:`, e);
                    }

                    console.log(`[UI][CREATE][${reqId}] Response ok=${res.ok} status=${res.status} durationMs=${durationMs} json=`, json);

                    if (!res.ok) {
                      console.error(`[UI][CREATE][${reqId}] Create project error:`, json);
                      alert((json && json.error) || "Failed to create Vizard clipping project.");
                      return;
                    }

                    const projectId = json?.projectId;
                    if (!projectId) {
                      console.warn(`[UI][CREATE][${reqId}] No projectId in response`, json);
                      alert("No projectId returned from Vizard.");
                      return;
                    }

                    // Navigate to results with projectId
                    window.location.href = `/clipping/results?projectId=${encodeURIComponent(projectId)}`;

                    // Close modal
                    onClose?.();
                  } catch (e: any) {
                    console.error(`[UI][CREATE][${reqId}] Exception:`, e);
                    alert(e?.message || "Unexpected error while creating project.");
                  }
                }}
              >
                <GeneratebtnIcon /> <span className="ml-2">Generate Clips</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}