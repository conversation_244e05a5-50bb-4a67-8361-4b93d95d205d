"use client";
import { useState, useEffect } from 'react';
import type { MenuItem, HistorySection, HistoryItem, SidebarProps } from './types';
import { useMobile } from './DashboardLayout';
import React from 'react';
import { Accounticon, Clippingicon, IdeaLabicon, Inspirationicon, Keywordicon, NewIdeaIcon, SocialPosticon, Storyboardicon, Youtubeicon, NewPostIcon, SchedulerIcon, PublishedPostsIcon, FailedPostsIcon, LearningResourcesIcon, ProfileSettingsIcon, SubscriptionIcon, CreditsActivityIcon } from './icons/icons';
import IdeaLab from '../idealab/page';

interface SidebarMenuItem extends MenuItem {
  submenu?: { label: string; active: boolean; sessionId?: string }[];
}

interface SidebarPropsExtended {
  menuItems?: SidebarMenuItem[];
  histories?: HistorySection[];
  isLoading?: boolean;
  onMenuItemClick?: (item: SidebarMenuItem, index: number) => void;
  onHistoryItemClick?: (historyItem: HistoryItem, sectionIndex: number, itemIndex: number) => void;
  onClearHistory?: () => void;
  onSeeMoreClick?: () => void;
}

// Icon components for better consistency
const IconComponents = {
  inspiration: (<Inspirationicon />),
  ideaLab: (<IdeaLabicon />),
  socialPost: (<SocialPosticon />),
  clipping: (<Clippingicon />),
  storyboard: (<Storyboardicon />),
  youtube: (<Youtubeicon />),
  keyword: (<Keywordicon />),
  account: (<Accounticon />)
};

// Organized menu items into categories
const menuCategories = {
  creative: [
    { label: "Inspiration", icon: IconComponents.inspiration, active: true },
    { label: "Idea Lab", icon: IconComponents.ideaLab, active: false },
    { label: "Social Post", icon: IconComponents.socialPost, active: false },
    { label: "Clipping", icon: IconComponents.clipping, active: false },
    { label: "Storyboard Editor", icon: IconComponents.storyboard, active: false },
  ],
  tools: [
    { label: "YouTube Tools", icon: IconComponents.youtube, active: false },
    { label: "Keyword Insights", icon: IconComponents.keyword, active: false },
  ],
  user: [
    { label: "Account", icon: IconComponents.account, active: false },
  ]
};

const defaultHistories: HistorySection[] = [
  {
    title: "Today",
    titleColor: "text-[#6366f1]",
    items: [
      { text: "Writing Converting Proposal" },
      { text: "Cold message template des" }
    ]
  },
  {
    title: "Yesterday",
    titleColor: "text-[#6b7873]",
    items: [
      { text: "Transcript Request Clarif" },
      { text: "Content Posting Strategy" },
      { text: "Edit Icon Functionality Exp" }
    ]
  },
  {
    title: "Previous 7 Days",
    titleColor: "text-[#6b7873]",
    items: [
      { text: "Cold message template of" },
      { text: "Transcript Request Clarif" }
    ]
  }
];

const logClick = (event: MouseEvent) => {
  console.log('Clicked element:', event.target);
  console.log('Clicked element class:', (event.target as HTMLElement).className);
};

export default function Sidebar({
  menuItems = [],
  histories = defaultHistories,
  isLoading = false,
  onMenuItemClick,
  onHistoryItemClick,
  onClearHistory,
  onSeeMoreClick
}: SidebarPropsExtended) {
  const { closeSidebar } = useMobile();
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set());

  // Helper function to render icon
  const renderIcon = (icon: string | React.ReactElement) => {
    if (typeof icon === 'string') {
      // If it's a string, check if it's a key in IconComponents
      const iconKey = icon as keyof typeof IconComponents;
      return IconComponents[iconKey] || icon;
    }
    // If it's already a React element, return it directly
    return icon;
  };

  // Helper function to get consistent icon for menu item
  const getConsistentIcon = (item: SidebarMenuItem) => {
    // Always use our own icon definitions for consistency
    const iconMap: { [key: string]: React.ReactElement } = {
      "Inspiration": IconComponents.inspiration,
      "Idea Lab": IconComponents.ideaLab,
      "Social Post": IconComponents.socialPost,
      "Clipping": IconComponents.clipping,
      "Storyboard Editor": IconComponents.storyboard,
      "YouTube Tools": IconComponents.youtube,
      "Keyword Insights": IconComponents.keyword,
      "Account": IconComponents.account,
    };

    return iconMap[item.label] || item.icon;
  };

  const handleMenuItemClick = (item: SidebarMenuItem, index: number) => {
    // Toggle submenu expansion if item has submenu
    if (item.submenu) {
      const newExpanded = new Set(expandedMenus);
      if (newExpanded.has(item.label)) {
        newExpanded.delete(item.label);
      } else {
        newExpanded.add(item.label);
      }
      setExpandedMenus(newExpanded);
    }

    onMenuItemClick?.(item, index);
    // Close sidebar on mobile when menu item is clicked (only if no submenu)
    if (!item.submenu) {
      closeSidebar();
    }
  };

  const handleSubmenuClick = (parentItem: SidebarMenuItem, subItem: { label: string; active: boolean; sessionId?: string }) => {
    // Handle submenu item click - pass the submenu item (including sessionId) to parent
    onMenuItemClick?.({ ...parentItem, ...subItem }, 0);
    closeSidebar();
  };

  const handleHistoryItemClick = (historyItem: HistoryItem, sectionIndex: number, itemIndex: number) => {
    // Handle history item click
    onHistoryItemClick?.(historyItem, sectionIndex, itemIndex);
    closeSidebar();
  };

  // Use provided menuItems if available, otherwise use default categories
  const useCustomMenu = menuItems.length > 0;

  // Organize menu items into categories if using custom menu
  const categorizeMenuItems = (items: SidebarMenuItem[]) => {
    const creative: SidebarMenuItem[] = [];
    const tools: SidebarMenuItem[] = [];
    const user: SidebarMenuItem[] = [];

    items.forEach(item => {
      if (item.label === "Account") {
        user.push(item);
      } else if (item.label === "YouTube Tools" || item.label === "Keyword Insights") {
        tools.push(item);
      } else {
        creative.push(item);
      }
    });

    return { creative, tools, user };
  };

  const { creative, tools, user } = useCustomMenu ? categorizeMenuItems(menuItems) : menuCategories;

  useEffect(() => {
    document.addEventListener('click', logClick);
    return () => document.removeEventListener('click', logClick);
  }, []);

  return (
    <aside className="w-[250px] sm:w-[280px] flex flex-col bg-[#0a0f0d] py-4 sm:py-5 px-3 sm:px-4 border-r border-[#1a1a1a] h-screen fixed left-0 top-0 overflow-hidden">
      {/* Single scrollable container for all content */}
      <div className="flex-1 overflow-y-auto" style={{
        scrollbarWidth: 'thin',
        scrollbarColor: '#4B5563 #1F2937'
      }}>
        {/* Navigation */}
        <nav>
          {/* Creative Tools Category */}
          <ul className="space-y-1">
            {creative.map((item, i) => (
              <li key={item.label}>
                <button
                  className={`w-full flex items-center gap-2 sm:gap-3 px-2 sm:px-3 py-2 sm:py-2.5 rounded-full text-xs sm:text-sm font-medium transition-all relative ${item.active
                    ? "text-white bg-[#2a2a3a]"
                    : "text-[#9ca3af] hover:bg-[#1a1a1a] hover:text-white"
                    }`}
                  onClick={() => handleMenuItemClick(item, i)}
                >
                  {item.active && (
                    <div
                      className="absolute inset-0 rounded-full pointer-events-none"
                      style={{
                        border: '1px solid transparent',
                        background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%) border-box',
                        WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                        WebkitMaskComposite: 'xor',
                        maskComposite: 'exclude',
                      }}
                    />
                  )}
                  <div className="flex items-center gap-2 sm:gap-3">
                    <span className={`text-sm sm:text-base flex-shrink-0 ${item.active ? 'filter brightness-0 invert' : ''}`}>{renderIcon(getConsistentIcon(item))}</span>
                    <span className="text-xs sm:text-base truncate">{item.label}</span>
                  </div>
                  {item.submenu && (
                    <span className="text-sm sm:text-base flex-shrink-0 transition-transform duration-200" style={{
                      transform: (expandedMenus.has(item.label) || (item.label === 'Idea Lab' && item.active) || (item.label === 'Social Post' && item.active) || (item.label === 'Account' && item.active)) ? 'rotate(90deg)' : 'rotate(0deg)'
                    }}>
                      {/* {IconComponents.arrowRight} */}
                    </span>
                  )}
                </button>

                {/* Submenu */}
                {item.submenu && (expandedMenus.has(item.label) || (item.label === 'Idea Lab' && item.active) || (item.label === 'Social Post' && item.active) || (item.label === 'Account' && item.active)) && (
                  <ul className="ml-4 sm:ml-6 mt-2 space-y-1 animate-in slide-in-from-top-2 duration-200">
                    {item.submenu.map((subItem) => (
                      <li key={subItem.sessionId || subItem.label}>
                        <button
                          className={`w-full flex items-center gap-2 sm:gap-3 px-2 sm:px-3 py-2 sm:py-2.5  text-xs sm:text-sm font-medium transition-all ${subItem.active
                            ? "text-[#887DFF]"
                            : "text-[#9ca3af] hover:bg-[#1a1a1a] hover:text-white"
                            }`}
                          onClick={() => handleSubmenuClick(item, subItem)}
                        >
                          {/* Render appropriate icon based on parent menu and submenu item */}
                          {item.label === 'Idea Lab' && subItem.active && (
                            <NewIdeaIcon />
                          )}
                          {item.label === 'Social Post' && (
                            <span className="flex-shrink-0">
                              {subItem.label === 'New Post' && <NewPostIcon />}
                              {subItem.label === 'Scheduler' && <SchedulerIcon />}
                              {subItem.label === 'Published Posts' && <PublishedPostsIcon />}
                              {subItem.label === 'Failed Posts' && <FailedPostsIcon />}
                            </span>
                          )}
                          {item.label === 'Account' && (
                            <span className="flex-shrink-0">
                              {subItem.label === 'Learning Resources' && <LearningResourcesIcon />}
                              {subItem.label === 'Profile Settings' && <ProfileSettingsIcon />}
                              {subItem.label === 'Subscription' && <SubscriptionIcon />}
                              {subItem.label === 'Credits Activity' && <CreditsActivityIcon />}
                            </span>
                          )}
                          <span className="truncate">{subItem.label}</span>
                        </button>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>

          {/* Vertical Gap */}
          {tools.length > 0 && <div className="my-8"></div>}

          {/* YouTube Tools and Keyword Insights Category */}
          {tools.length > 0 && (
            <ul className="space-y-1">
              {tools.map((item, i) => (
                <li key={item.label}>
                  <button
                    className={`w-full flex items-center gap-2 sm:gap-3 px-2 sm:px-3 py-2 sm:py-2.5 rounded-full text-xs sm:text-sm font-medium transition-all relative ${item.active
                      ? "text-white bg-[#2a2a3a]"
                      : "text-[#9ca3af] hover:bg-[#1a1a1a] hover:text-white"
                      }`}
                    onClick={() => handleMenuItemClick(item, creative.length + i)}
                  >
                    {item.active && (
                      <div
                        className="absolute inset-0 rounded-full pointer-events-none"
                        style={{
                          border: '1px solid transparent',
                          background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%) border-box',
                          WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                          WebkitMaskComposite: 'xor',
                          maskComposite: 'exclude',
                        }}
                      />
                    )}
                    <span className="text-sm sm:text-base flex-shrink-0 relative z-10">{renderIcon(getConsistentIcon(item))}</span>
                    <span className="text-xs sm:text-base truncate relative z-10">{item.label}</span>
                  </button>
                </li>
              ))}
            </ul>
          )}

          {/* Vertical Gap */}
          {user.length > 0 && <div className="my-8"></div>}

          {/* Account Category */}
          {user.length > 0 && (
            <ul className="space-y-1">
              {user.map((item, i) => (
                <li key={item.label}>
                  <button
                    className={`w-full flex items-center gap-2 sm:gap-3 px-2 sm:px-3 py-2 sm:py-2.5 rounded-full text-xs sm:text-sm font-medium transition-all relative ${item.active
                      ? "text-white bg-[#2a2a3a]"
                      : "text-[#9ca3af] hover:bg-[#1a1a1a] hover:text-white"
                      }`}
                    onClick={() => handleMenuItemClick(item, creative.length + tools.length + i)}
                  >
                    {item.active && (
                      <div
                        className="absolute inset-0 rounded-full pointer-events-none"
                        style={{
                          border: '1px solid transparent',
                          background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%) border-box',
                          WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                          WebkitMaskComposite: 'xor',
                          maskComposite: 'exclude',
                        }}
                      />
                    )}
                    <div className="flex items-center gap-2 sm:gap-3 relative z-10">
                      <span className={`text-sm sm:text-base flex-shrink-0 ${item.active ? 'filter brightness-0 invert' : ''}`}>{renderIcon(getConsistentIcon(item))}</span>
                      <span className="text-xs sm:text-base truncate">{item.label}</span>
                    </div>
                    {item.submenu && (
                      <span className="text-sm sm:text-base flex-shrink-0 transition-transform duration-200" style={{
                        transform: (expandedMenus.has(item.label) || (item.label === 'Idea Lab' && item.active) || (item.label === 'Social Post' && item.active) || (item.label === 'Account' && item.active)) ? 'rotate(90deg)' : 'rotate(0deg)'
                      }}>
                        {/* {IconComponents.arrowRight} */}
                      </span>
                    )}
                  </button>

                  {/* Submenu */}
                  {item.submenu && (expandedMenus.has(item.label) || (item.label === 'Idea Lab' && item.active) || (item.label === 'Social Post' && item.active) || (item.label === 'Account' && item.active)) && (
                    <ul className="ml-4 sm:ml-6 mt-2 space-y-1 animate-in slide-in-from-top-2 duration-200">
                      {item.submenu.map((subItem) => (
                        <li key={subItem.sessionId || subItem.label}>
                          <button
                            className={`w-full flex items-center gap-2 sm:gap-3 px-2 sm:px-3 py-2 sm:py-2.5  text-xs sm:text-sm font-medium transition-all ${subItem.active
                              ? "text-[#887DFF]"
                              : "text-[#9ca3af] hover:bg-[#1a1a1a] hover:text-white"
                              }`}
                            onClick={() => handleSubmenuClick(item, subItem)}
                          >
                            {/* Render appropriate icon based on parent menu and submenu item */}
                            {item.label === 'Idea Lab' && subItem.active && (
                              <NewIdeaIcon />
                            )}
                            {item.label === 'Social Post' && (
                              <span className="flex-shrink-0">
                                {subItem.label === 'New Post' && <NewPostIcon />}
                                {subItem.label === 'Scheduler' && <SchedulerIcon />}
                                {subItem.label === 'Published Posts' && <PublishedPostsIcon />}
                                {subItem.label === 'Failed Posts' && <FailedPostsIcon />}
                              </span>
                            )}
                            {item.label === 'Account' && (
                              <span className="flex-shrink-0">
                                {subItem.label === 'Learning Resources' && <LearningResourcesIcon />}
                                {subItem.label === 'Profile Settings' && <ProfileSettingsIcon />}
                                {subItem.label === 'Subscription' && <SubscriptionIcon />}
                                {subItem.label === 'Credits Activity' && <CreditsActivityIcon />}
                              </span>
                            )}
                            <span className="truncate">{subItem.label}</span>
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </li>
              ))}
            </ul>
          )}
        </nav>

        {/* Idea Lab Histories - Now part of the main scroll */}
        <div className="mt-6 mb-60 sm:mt-8 px-1">
          <div className="flex items-center justify-between mb-3 sm:mb-4">
            <div className="text-[#6b7280] text-[10px] sm:text-[11px] font-medium uppercase tracking-wide">Idea Lab Histories</div>
            {histories.length > 0 && !isLoading && (
              <button
                className="text-[#6b7280] text-[8px] sm:text-[9px] hover:text-white transition-colors"
                onClick={() => {
                  // Clear history functionality can be added here
                  onClearHistory?.();
                }}
                title="Clear History"
              >
                Clear
              </button>
            )}
          </div>

          <div className="space-y-2 sm:space-y-3">
            {isLoading ? (
              // Loading skeleton
              <div className="space-y-3">
                <div className="text-[#6366f1] text-[10px] sm:text-[11px] font-semibold mb-1 sm:mb-1.5 uppercase tracking-wide">
                  Today
                </div>
                {[1, 2, 3].map((i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-3 bg-[#1a1a1a] rounded w-3/4 mb-1"></div>
                  </div>
                ))}
                <div className="text-[#6b7873] text-[10px] sm:text-[11px] font-semibold mb-1 sm:mb-1.5 uppercase tracking-wide mt-4">
                  Yesterday
                </div>
                {[1, 2].map((i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-3 bg-[#1a1a1a] rounded w-2/3 mb-1"></div>
                  </div>
                ))}
              </div>
            ) : histories.length > 0 ? (
              <>
                {histories.map((section, sectionIndex) => (
                  <div key={sectionIndex}>
                    <div className={`${section.titleColor || 'text-[#6366f1]'} text-[10px] sm:text-[11px] font-semibold mb-1 sm:mb-1.5 uppercase tracking-wide`}>
                      {section.title}
                    </div>
                    {section.items.map((item, itemIndex) => (
                      <button
                        key={itemIndex}
                        className="w-full text-left text-[#9ca3af] text-[10px] sm:text-[11px] mb-1 leading-relaxed truncate hover:text-white hover:bg-[#1a1a1a] px-2 py-1 rounded transition-all duration-200 cursor-pointer"
                        onClick={() => handleHistoryItemClick(item, sectionIndex, itemIndex)}
                        title={item.text}
                      >
                        {item.text}
                      </button>
                    ))}
                  </div>
                ))}
                {/* See More Button */}
                <div className="flex justify-center mt-2">
                  <button
                    className="text-xs text-[#6366f1] hover:underline hover:text-white px-2 py-1 rounded pointer-events-auto"
                    onClick={onSeeMoreClick}
                  >
                    See More
                  </button>
                </div>
              </>
            ) : (
              <div className="text-[#6b7280] text-[10px] sm:text-[11px] text-center py-4">
                No history yet
              </div>
            )}
          </div>
        </div>
      </div>
    </aside>
  );
}
