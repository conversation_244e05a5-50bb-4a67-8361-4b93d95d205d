import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/auth.config";
import {
  normalizeQueryResponse,
  vizardQueryProject,
} from "../../../../lib/vizard";

export async function GET(req: NextRequest) {
  const reqId = `api-query-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
  const started = Date.now();

  // Auth required
  const session = await getServerSession(authOptions as any);
  if (!session) {
    console.warn(`[API][QUERY_PROJECT][${reqId}] Unauthorized request`);
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(req.url);
  const projectId = searchParams.get("projectId");

  if (!projectId) {
    console.error(`[API][QUERY_PROJECT][${reqId}] Missing projectId`);
    return NextResponse.json(
      { error: "projectId query parameter is required", reqId },
      { status: 400 }
    );
  }

  console.log(`[API][QUERY_PROJECT][${reqId}] projectId=${projectId}`);

  try {
    const resp: any = await vizardQueryProject(projectId);
    const duration = Date.now() - started;
    console.log(
      `[API][QUERY_PROJECT][${reqId}] Upstream response code=${resp?.code} msg=${resp?.msg} durationMs=${duration}`
    );

    // Handle processing state per docs: code === 1000
    if (resp?.code === 1000) {
      const result = normalizeQueryResponse(resp);
      return NextResponse.json({ result, raw: resp, reqId });
    }

    // Success per docs: code === 2000
    if (typeof resp?.code !== "number" || resp.code !== 2000) {
      const msg = resp?.msg || resp?.errMsg || "Vizard query failed";
      console.error(`[API][QUERY_PROJECT][${reqId}] Upstream business error:`, {
        msg,
        code: resp?.code,
      });
      return NextResponse.json({ error: msg, raw: resp, reqId }, { status: 502 });
    }
    const normalized = normalizeQueryResponse(resp);
    return NextResponse.json({ result: normalized, raw: resp, reqId });
  } catch (err: any) {
    const duration = Date.now() - started;
    console.error(
      `[API][QUERY_PROJECT][${reqId}] Exception durationMs=${duration} error=`,
      err?.message || err
    );
    return NextResponse.json(
      { error: err?.message || "Vizard query request error", reqId },
      { status: 500 }
    );
  }
}