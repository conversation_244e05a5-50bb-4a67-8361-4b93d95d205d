import { AuthOptions, SessionStrategy, DefaultSession } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import { MongoDBAdapter } from "@auth/mongodb-adapter";
import clientPromise from "../../../../lib/mongodb";
import { compare } from "bcryptjs";
import { connectToDatabase } from "../../../../lib/mongodb";

declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
    } & DefaultSession["user"]
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
  }
}

export const authOptions: AuthOptions = {
  adapter: MongoDBAdapter(clientPromise),
  secret: process.env.NEXTAUTH_SECRET,
  providers: [
    // Add Google provider so "Continue with Google" uses the same config as the route
    GoogleProvider({
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Please enter an email and password");
        }

        const { db } = await connectToDatabase();
        const user = await db.collection("users").findOne({ email: credentials.email });

        if (!user) {
          throw new Error("No user found with this email");
        }

        if (!user.emailVerified) {
          throw new Error("Please verify your email before signing in");
        }

        const isValid = await compare(credentials.password, user.password);

        if (!isValid) {
          throw new Error("Invalid password");
        }

        return {
          id: user._id.toString(),
          email: user.email,
          name: user.name,
        };
      }
    })
  ],
  session: {
    strategy: "jwt" as SessionStrategy,
  },
  pages: {
    // Keep custom pages if you actually have them, but ensure /login is your real sign-in page in app
    signIn: "/login",
    error: "/auth/error",
  },
  callbacks: {
    async jwt({ token, user, account, profile, trigger, session }) {
      // Debug log to trace token evolution
      console.log("[nextauth][jwt] start", {
        hasUser: Boolean(user),
        provider: account?.provider,
        tokenId: (token as any)?.id,
      });

      if (user) {
        // For both credentials and social login, normalize id into token.id
        (token as any).id = (user as any).id ?? (token as any).sub ?? (user as any).email ?? null;
      }

      // When using OAuth with JWT strategy, persist provider subject to token.sub
      // No extra action needed; getToken will read this cookie-signed JWT.

      console.log("[nextauth][jwt] end", {
        tokenId: (token as any)?.id,
        sub: (token as any)?.sub,
      });
      return token;
    },
    async session({ session, token }) {
      console.log("[nextauth][session] before assign", {
        hasToken: Boolean(token),
        tokenId: (token as any)?.id,
        sub: (token as any)?.sub,
        userEmail: session.user?.email,
      });

      if (token) {
        (session.user as any).id = (token as any).id ?? (token as any).sub ?? null;
      }

      console.log("[nextauth][session] after assign", {
        userId: (session.user as any)?.id ?? null,
        email: session.user?.email ?? null,
      });

      return session;
    },
    async redirect({ url, baseUrl }) {
      // Respect ?from=... to return users after login
      try {
        const u = new URL(url, baseUrl);
        const from = u.searchParams.get("from");
        if (from && from.startsWith("/")) {
          console.log("[nextauth][redirect] honoring from param:", from);
          return `${baseUrl}${from}`;
        }
      } catch {}
      console.log("[nextauth][redirect] default to baseUrl", { url, baseUrl });
      return baseUrl;
    }
  },
  cookies: {
    // Ensure cookies are set on the same-site for local dev; adjust if using HTTPS/custom domains
    sessionToken: {
      name: process.env.NODE_ENV === "production" ? "__Secure-next-auth.session-token" : "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  }
};