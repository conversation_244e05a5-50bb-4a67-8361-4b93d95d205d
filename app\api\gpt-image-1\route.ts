import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

export const runtime = 'nodejs';

/**
 * Revert: keep the original full implementation of /api/gpt-image-1.
 * We'll create a new endpoint at /api/gpt-image-designer instead of trying to proxy here.
 */

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// Simple in-memory store for resized images (Map<id, { mime: string; buffer: Buffer }>)
const memoryStore = new Map<string, { mime: string; buffer: Buffer }>();

let sharpModule: any = null;
async function ensureSharp() {
  if (sharpModule) return sharpModule;
  try {
    const mod = await import('sharp');
    // @ts-ignore
    sharpModule = mod.default || mod;
  } catch (e) {
    console.warn('sharp not available, skipping server-side resize');
    sharpModule = null;
  }
  return sharpModule;
}

function validateEnv() {
  console.log('[gpt-image-1] validateEnv: start');
  console.log('[gpt-image-1] REPLICATE_API_TOKEN present:', !!process.env.REPLICATE_API_TOKEN);
  console.log('[gpt-image-1] OPENAI key present:', !!(process.env.OpenAI_API_KEY || process.env.OPENAI_API_KEY));
  if (!process.env.REPLICATE_API_TOKEN) {
    console.error('[gpt-image-1] Missing REPLICATE_API_TOKEN');
    throw new Error('Missing REPLICATE_API_TOKEN in environment');
  }
  if (!process.env.OpenAI_API_KEY && !process.env.OPENAI_API_KEY) {
    console.error('[gpt-image-1] Missing OPENAI_API_KEY/OpenAI_API_KEY');
    throw new Error('Missing OPENAI_API_KEY (or OpenAI_API_KEY) in environment');
  }
  console.log('[gpt-image-1] validateEnv: ok');
}

function getOpenAIKey(): string {
  const key = process.env.OpenAI_API_KEY || process.env.OPENAI_API_KEY || '';
  console.log('[gpt-image-1] getOpenAIKey: key exists?', !!key);
  return key;
}

export async function POST(req: NextRequest) {
  try {
    console.log('[gpt-image-1] POST invoked:', req.method, req.url);
    validateEnv();

    const body = await req.json();
    console.log('[gpt-image-1] request body keys:', Object.keys(body || {}));
    const {
      prompt,
      number_of_images = 1,
      quality = 'auto',
      background = 'auto',
      output_format = 'png',
      aspect_ratio = '3:2',
      input_images = undefined,
      input_fidelity = 'low',
      output_compression = 90,
      user_id = null,
      moderation = 'auto',
      resize = true,
    } = body || {};

    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json({ error: 'prompt is required' }, { status: 400 });
    }

    const input: Record<string, any> = {
      openai_api_key: getOpenAIKey(),
      prompt,
      aspect_ratio,
      number_of_images: Math.min(Math.max(Number(number_of_images) || 1, 1), 10),
      quality,
      background,
      output_format,
      input_fidelity,
      output_compression,
      moderation,
    };
    console.log('[gpt-image-1] prepared input (trunc):', JSON.stringify(input).slice(0, 600), '...');

    if (Array.isArray(input_images) && input_images.length > 0) {
      input.input_images = input_images;
      console.log('[gpt-image-1] input_images count:', input_images.length);
    }
    if (user_id) {
      input.user_id = user_id;
      console.log('[gpt-image-1] user_id:', user_id);
    }

    const prediction = await replicate.predictions.create({
      model: 'openai/gpt-image-1',
      input
    });
    console.log('[gpt-image-1] created prediction id:', (prediction as any)?.id, 'status:', (prediction as any)?.status);

    let pred = prediction as any;
    const start = Date.now();
    const timeoutMs = 120000;
    while (!['succeeded', 'failed', 'canceled'].includes(pred.status)) {
      await new Promise((r) => setTimeout(r, 1500));
      pred = await replicate.predictions.get(pred.id);
      console.log('[gpt-image-1] polled id:', pred?.id, 'status:', pred?.status);
      if (Date.now() - start > timeoutMs) {
        console.warn('[gpt-image-1] polling timeout hit');
        break;
      }
    }
    console.log('[gpt-image-1] final prediction status:', pred?.status);

    let urls: string[] = [];
    const out = pred?.output;
    console.log('[gpt-image-1] output type:', Array.isArray(out) ? 'array' : typeof out, 'value (trunc):', typeof out === 'string' ? out.slice(0, 120) : JSON.stringify(out || {}).slice(0, 200));

    if (Array.isArray(out)) {
      for (const item of out) {
        if (typeof item === 'string') {
          urls.push(item);
        } else if (item && typeof (item as any).url === 'function') {
          try {
            const u = (item as any).url();
            if (typeof u === 'string') urls.push(u);
          } catch {}
        } else if (item && typeof (item as any).url === 'string') {
          urls.push((item as any).url);
        } else if (item && typeof item === 'object') {
          const u = (item as any).image || (item as any).href;
          if (typeof u === 'string') urls.push(u);
        }
      }
    } else if (typeof out === 'string') {
      urls.push(out);
    }

    if (!urls.length) {
      console.log('[gpt-image-1] urls empty, attempting fallback with replicate.run');
      try {
        const runOut = await replicate.run('openai/gpt-image-1', { input });
        console.log('[gpt-image-1] run() output type:', Array.isArray(runOut) ? 'array' : typeof runOut);
        if (Array.isArray(runOut)) {
          for (const item of runOut) {
            if (typeof item === 'string') urls.push(item);
            else if (item && typeof (item as any).url === 'function') {
              try {
                const u = (item as any).url();
                if (typeof u === 'string') urls.push(u);
              } catch {}
            } else if (item && typeof (item as any).url === 'string') {
              urls.push((item as any).url);
            } else if (item && typeof item === 'object') {
              const u = (item as any).image || (item as any).href;
              if (typeof u === 'string') urls.push(u);
            }
          }
        } else if (typeof runOut === 'string') {
          urls.push(runOut);
        }
      } catch (e) {
        console.warn('[gpt-image-1] replicate.run fallback error:', e);
      }
    }
    console.log('[gpt-image-1] collected urls:', urls);

    if (!urls.length) {
      return NextResponse.json({
        success: false,
        error: 'No images generated',
        status: pred?.status || null,
        id: pred?.id || null,
        logs: pred?.logs || null,
        metrics: pred?.metrics || null,
      }, { status: 200 });
    }

    const primaryUrl = urls[0];
    console.log('[gpt-image-1] primaryUrl selected:', primaryUrl);
    let resizedDataUrl: string | null = null;

    if (resize) {
      try {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 30000);
        const res = await fetch(primaryUrl, { signal: controller.signal });
        clearTimeout(timeout);
        if (!res.ok) {
          throw new Error(`Fetch generated image failed: ${res.status} ${res.statusText}`);
        }
        const arrayBuffer = await res.arrayBuffer();

        const sharp = await ensureSharp();
        if (!sharp) {
          throw new Error('sharp not available');
        }

        const img = sharp(Buffer.from(arrayBuffer));
        const format = output_format === 'jpeg' ? 'jpeg' : output_format === 'webp' ? 'webp' : 'png';
        const resized = await img.resize(1280, 720, { fit: 'cover' }).toFormat(format);
        const buffer = await resized.toBuffer();
        const mime = format === 'jpeg' ? 'image/jpeg' : format === 'webp' ? 'image/webp' : 'image/png';

        const base64 = buffer.toString('base64');
        resizedDataUrl = `data:${mime};base64,${base64}`;

        const id = `${Date.now()}-${Math.random().toString(36).slice(2)}`;
        memoryStore.set(id, { mime, buffer });
      } catch (e: any) {
        console.warn('Resize step skipped:', e?.message || e);
      }
    }

    try {
      if (resizedDataUrl) {
        console.log('[gpt-image-1] Uploading resized image to Cloudinary for durable URL');
        const uploaded = await (await import('../../../lib/cloudinary')).default.uploader.upload(resizedDataUrl, {
          folder: 'ai-generated-images',
        });
        const finalUrl = uploaded.secure_url;
        console.log('[gpt-image-1] Cloudinary upload done:', finalUrl);
        return NextResponse.json({
          success: true,
          images: [finalUrl],
          original: urls,
          resized: true,
          aspect_ratio,
          bytes_exact_1280x720: true,
        });
      }
    } catch (upErr) {
      console.warn('[gpt-image-1] Cloudinary upload failed, falling back to existing URL:', upErr);
    }

    return NextResponse.json({
      success: true,
      images: [primaryUrl],
      original: urls,
      resized: false,
      aspect_ratio,
      bytes_exact_1280x720: false,
    });
  } catch (error: any) {
    console.error('[gpt-image-1] API error:', error);
    return NextResponse.json(
      { error: error?.message || 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  return NextResponse.json({ error: 'Not implemented' }, { status: 404 });
}