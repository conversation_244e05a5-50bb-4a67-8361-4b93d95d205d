import React from 'react';

// Credit plans data organized to match the reference image layout:
// First row: 200, 410, 620, 3500
// Second row: 3500, 850, 960, 740  
// Third row: 200, 740, 410, 740
const creditPlans = [
  // First row
  { credits: 200, price: 5, bonus: 0 },
  { credits: 410, price: 10, bonus: 0 },
  { credits: 620, price: 15, bonus: 0 },
  { credits: 3500, price: 50, bonus: 500 }, // 3000 base + 500 bonus = 3500 total
  
  // Second row
  { credits: 3500, price: 50, bonus: 500 }, // 3000 base + 500 bonus = 3500 total
  { credits: 850, price: 25, bonus: 0 },
  { credits: 960, price: 30, bonus: 0 },
  { credits: 740, price: 20, bonus: 0 },
  
  // Third row
  { credits: 200, price: 5, bonus: 0 },
  { credits: 740, price: 20, bonus: 0 },
  { credits: 410, price: 10, bonus: 0 },
  { credits: 740, price: 20, bonus: 0 },
];

const CreditsPlans: React.FC = () => {
  return (
    <div className="w-full flex justify-center">
      <div className="relative z-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full max-w-7xl">
        {creditPlans.map((plan, index) => (
          <div
            key={index}
            className="bg-gradient-to-b from-[#1a1f2e] to-[#151a26] border border-[#2a2f3e] rounded-xl p-5 sm:p-6 lg:p-5 flex flex-col items-center shadow-xl min-h-[200px] sm:min-h-[220px] lg:min-h-[200px] justify-between"
          >
            {/* Top section with credits and bonus */}
            <div className="text-center flex-1 flex flex-col justify-center mb-3 sm:mb-4">
              <div className="text-4xl sm:text-5xl lg:text-4xl xl:text-5xl font-extrabold text-white mb-1 sm:mb-2 leading-none">{plan.credits.toLocaleString()}</div>
              {plan.bonus > 0 && (
                <div className="text-xs sm:text-sm text-gray-400 mt-1 sm:mt-2 leading-tight">
                  Total: {(plan.credits - plan.bonus).toLocaleString()} + {plan.bonus.toLocaleString()} Bonus
                </div>
              )}
            </div>
            
            {/* Bottom section with price and button */}
            <div className="text-center w-full">
              <div className="text-2xl sm:text-3xl lg:text-2xl xl:text-3xl font-bold text-white mb-3 sm:mb-4">${plan.price}</div>
              <button className="w-full bg-gradient-to-r from-[#4c1d95] to-[#5b21b6] text-white rounded-full py-2.5 sm:py-3 px-4 sm:px-6 text-sm sm:text-base font-medium border border-[#6d28d9] hover:from-[#5b21b6] hover:to-[#7c3aed] hover:border-[#8b5cf6] transition-all duration-300 ease-in-out transform hover:scale-[1.02] shadow-lg hover:shadow-xl">
                Purchase Now
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CreditsPlans;
