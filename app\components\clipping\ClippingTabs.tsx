"use client";
import React from "react";

type TabKey = "recent" | "exported";

type Props = {
  active: TabKey;
  onChange: (key: Tab<PERSON>ey) => void;
};

export default function ClippingTabs({ active, onChange }: Props) {
  return (
    <div className="flex items-center gap-6 mb-3 pl-1">
      <button
        type="button"
        role="tab"
        aria-selected={active === "recent"}
        onClick={() => onChange("recent")}
        className={`flex items-center px-8 py-2 rounded-full text-[17px] font-medium shadow-[0_2px_16px_0_#a78bfa33] ${
          active === "recent"
            ? "bg-[#32284e] text-white"
            : "bg-transparent text-white/80 hover:text-white"
        }`}
        style={active === "recent" ? { boxShadow: "0 2px 16px 0 #a78bfa33" } : {}}
      >
        Recent
      </button>

      <button
        type="button"
        role="tab"
        aria-selected={active === "exported"}
        onClick={() => onChange("exported")}
        className={`text-[17px] font-normal ${
          active === "exported" ? "text-white" : "text-white/90 hover:text-white"
        }`}
      >
        Exported Clips
      </button>
    </div>
  );
}