"use client";
import React from "react";
import Image from "next/image";
import { ConnectPinIcon, EditPenIcon } from "./icons/icons";
// If your project already has an Avatar component, feel free to replace this with it.

const socialIcons: Record<string, React.ReactNode> = {
    facebook: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-white" aria-hidden>
            <path d="M22.675 0H1.325C.593 0 0 .593 0 1.326v21.348C0 23.406.593 24 1.325 24h11.495V14.708h-3.13v-3.622h3.13V8.413c0-3.1 1.893-4.788 4.66-4.788 1.325 0 2.464.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.714-1.795 1.763v2.314h3.587l-.467 3.622h-3.12V24h6.116C23.406 24 24 23.406 24 22.674V1.326C24 .593 23.406 0 22.675 0z" />
        </svg>
    ),
    linkedin: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-white" aria-hidden>
            <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 20h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.783-1.75-1.75s.784-1.75 1.75-1.75 1.75.784 1.75 1.75-.784 1.75-1.75 1.75zm13.5 12.268h-3v-5.604c0-1.337-.025-3.06-1.865-3.06-1.867 0-2.153 1.459-2.153 2.965v5.699h-3v-11h2.882v1.507h.041c.402-.762 1.384-1.566 2.848-1.566 3.045 0 3.607 2.007 3.607 4.619v6.44z" />
        </svg>
    ),
    snapchat: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-white" aria-hidden>
            <path d="M12 0c-4.333 0-6.667 2.333-6.667 7 0 3.667-.333 4-.333 4s-.333.333-.333.667c0 .667.667 1 1.333 1.333.667.333 1 1 1 1.667 0 .667-1.667.667-2 .667s-.667 0-.667.667c0 .667.667 1 1.333 1 .667 0 1.333 0 1.333.333 0 .667-1.333 2.667-4 2.667-.667 0-1.333.333-1.333 1.333 0 .667 1.333 1 2.667 1 1.333 0 3.333-1 4.667-1s3.333 1 4.667 1c1.333 0 2.667-.333 2.667-1 0-1-.667-1.333-1.333-1.333-2.667 0-4-2-4-2.667 0-.333.667-.333 1.333-.333.667 0 1.333-.333 1.333-1 0-.667-.333-1.333-1-1.667-.667-.333-1.333-.666-1.333-1.333 0-.667-.333-1-1-1.333-.667-.333-1.333-.666-1.333-1.333 0-.333.333-.667.333-.667s-.333-.333-.333-4c0-4.667 2.334-7 6.667-7z" />
        </svg>
    ),
    reddit: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-white" aria-hidden>
            <path d="M24 11.868c0-1.142-.927-2.069-2.068-2.069-.531 0-1.04.2-1.422.563-2.444-1.715-5.851-2.822-9.595-2.958l2.005-6.333 5.4 1.266c.003 1.145.932 2.07 2.077 2.07 1.146 0 2.077-.931 2.077-2.077s-.931-2.077-2.077-2.077c-.889 0-1.646.561-1.963 1.346l-6.037-1.416c-.459-.108-.919.153-1.067.606l-2.229 7.044c-3.825.132-7.274 1.274-9.734 3.027-.384-.377-.896-.593-1.432-.593-1.145 0-2.077.933-2.077 2.077 0 .887.566 1.643 1.35 1.963-.057.314-.098.632-.098.96 0 2.888 3.16 5.222 7.49 5.904.049.842.765 1.515 1.624 1.515.856 0 1.571-.669 1.623-1.508.871.095 1.771.155 2.689.155.919 0 1.819-.06 2.69-.155.051.839.766 1.508 1.622 1.508.86 0 1.576-.673 1.625-1.515 4.33-.682 7.49-3.016 7.49-5.904 0-.327-.036-.645-.092-.957.779-.322 1.343-1.077 1.343-1.965zM6.545 13.945c0-.948.762-1.71 1.71-1.71.948 0 1.71.762 1.71 1.71s-.762 1.71-1.71 1.71c-.948 0-1.71-.762-1.71-1.71zm10.2 4.291c-.996.996-2.809 1.105-4.747 1.105-1.937 0-3.751-.109-4.747-1.105-.249-.249-.249-.653 0-.902.249-.249.653-.249.902 0 .709.709 2.254.821 3.845.821 1.593 0 3.141-.114 3.847-.821.248-.249.652-.249.9 0 .249.249.249.653 0 .902zm-.14-2.581c-.948 0-1.71-.762-1.71-1.71s.762-1.71 1.71-1.71c.948 0 1.71.762 1.71 1.71s-.762 1.71-1.71 1.71z" />
        </svg>
    ),
};

type ConnectedAccount = {
    platform: keyof typeof socialIcons;
    url: string;
    alias: string;
};

const dummyAccounts: ConnectedAccount[] = [
    { platform: "facebook", url: "https://www.facebook.com/in/", alias: "keoo" },
    { platform: "linkedin", url: "https://www.linkedin.com/in/", alias: "keoo" },
    { platform: "snapchat", url: "https://www.snapchat.com/in/", alias: "keoo" },
    { platform: "reddit", url: "https://www.reddit.com/in/", alias: "keoo" },
];

const ProfileSettings: React.FC = () => {
    return (
        <div className="text-white px-12 py-12">
            {/* Header */}
            <div className="p-8 md:p-12 bg-gradient-to-r from-[#1a1a2e]/90 to-[#1a1a2e]/30 rounded-xl mb-6 flex flex-col   ">
                <h2 className="text-2xl md:text-3xl font-semibold mb-4">Profile Settings</h2>
                <p className="text-sm md:text-base text-gray-300 max-w-2xl leading-relaxed">
                    Explore tutorials, guides, and tips to master Keoo AI. Whether you're just starting out or looking to sharpen your skills, everything you need to create smarter is right here.
                </p>
            </div>
            
            {/* Main Card */}
            <div className=" border border-[#2a2a3e]/30 rounded-xl overflow-hidden">
                {/* User Profile Section */}
                <div className="p-6 flex flex-col sm:flex-row justify-evenly">
                <div>    {/* Avatar */}
                    <div className="relative group cursor-pointer">
                        <div className="w-40 h-40 rounded-full overflow-hidden bg-[#333] border border-[#2a2a3e]/50 flex items-center justify-center transition-all duration-300 hover:border-indigo-500">
                            <Image 
                                src="/inspiration/10.png" 
                                alt="avatar" 
                                width={400} 
                                height={400} 
                                className="object-cover transition-transform duration-300 group-hover:scale-110" 
                            />
                        </div>
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="bg-black/50 rounded-full p-2">
                                <EditPenIcon  />
                            </div>
                        </div>
                    </div></div>
                    
                    <div>{/* User Info */}
                    <div className="flex-1">
                        <table className="w-full border-separate border-spacing-y-2">
                            <tbody>
                                <tr className="group transition-all duration-200 hover:bg-[#2a2a3e]/10 rounded-lg">
                                    <td className="text-gray-400 text-sm w-24 pl-3">ID</td>
                                    <td className="rounded-lg py-2">
                                        <div className="flex items-center">
                                            <span className="font-mono">KEOO21982</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr className="group transition-all duration-200 hover:bg-[#2a2a3e]/10">
                                    <td className="text-gray-400 text-sm pl-3">Name</td>
                                    <td className="rounded-lg py-2">
                                        <div className="flex items-center gap-2">
                                            <span>First Name Last Name</span>
                                            <button className="opacity-0 group-hover:opacity-100 transition-opacity">
                                                <EditPenIcon  />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr className="group transition-all duration-200 hover:bg-[#2a2a3e]/10">
                                    <td className="text-gray-400 text-sm pl-3">Email</td>
                                    <td className="rounded-lg py-2">
                                        <div className="flex items-center gap-2">
                                            <span><EMAIL></span>
                                            <button className="opacity-0 group-hover:opacity-100 transition-opacity">
                                                <EditPenIcon  />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr className="group transition-all duration-200 hover:bg-[#2a2a3e]/10">
                                    <td className="text-gray-400 text-sm pl-3">Password</td>
                                    <td className="rounded-lg py-2">
                                        <a href="#" className="text-indigo-400 hover:text-indigo-300 transition-colors">
                                            Reset Password
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div></div>
                </div>
                
                <hr className="border-[#2a2a3e]/30" />
                
                {/* Social Connect Section */}
                <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-[1fr_2fr] gap-6">
                        {/* Left description */}
                        <div>
                            <p className="text-sm text-gray-400 leading-relaxed">
                                Link your social media to Keoo AI for easier content sharing, faster access, and a more personalized experience.
                            </p>
                        </div>
                        
                        {/* Right content */}
                        <div className="bg-[#1a1a2e] rounded-xl p-5 space-y-5">
                            {/* Add new account */}
                            <div>
                                <label className="block text-sm mb-3">Add new account</label>
                                <div className="flex items-center gap-3">
                                    <div className="relative flex-1">
                                        <div className="flex items-center gap-2 bg-[#2a2a3e]/30 rounded-full py-2 px-4 text-sm">
                                            <svg width="20" height="20" viewBox="0 0 48 48" fill="currentColor" className="text-white">
                                                <path d="M38.0066 15.5732C38.0066 6.97179 31.1254 0 22.6364 0C14.1474 0 7.26624 6.97179 7.26624 15.5732V16.8871C7.26624 25.4885 0.385071 32.4603 0.385071 32.4603V36.7027H44.8878V32.4603C44.8878 32.4603 38.0066 25.4885 38.0066 16.8871V15.5732Z" />
                                                <path d="M28.7805 40.9451H16.4924C16.4924 44.8384 19.6345 48 23.5 48C27.3655 48 30.5076 44.8384 30.5076 40.9451H28.7805Z" />
                                            </svg>
                                            TikTok
                                            <svg className="ml-auto" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <button className="bg-indigo-500 hover:bg-indigo-600 px-4 py-2 rounded-full flex items-center gap-2 transition-colors text-sm">
                                        Connect <ConnectPinIcon />
                                    </button>
                                </div>
                            </div>
                            
                            {/* Connected accounts */}
                            <div>
                                <p className="text-sm mb-3">Connected accounts</p>
                                <ul className="space-y-1">
                                    {dummyAccounts.map((acc, idx) => (
                                        <li key={idx} className="flex items-center justify-between py-2.5 px-3 bg-black/20 rounded-md">
                                            <div className="flex items-center gap-3">
                                                {socialIcons[acc.platform]}
                                                <span className="text-[#6D6D6D] text-sm">{acc.url}</span>
                                            </div>
                                            <div className="flex items-center gap-4">
                                                <span className="text-gray-400">{acc.alias}</span>
                                                <button className="text-gray-400 hover:text-red-400 transition-colors" title="Remove">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill="currentColor"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <hr className="border-[#2a2a3e]/30" />
                
                {/* Delete Account Section */}
                <div className="p-6">
                    <h3 className="font-medium mb-2">Delete account</h3>
                    <p className="text-sm text-gray-500 mb-4">
                        Permanently delete your account, all generations and cancel your subscription
                    </p>
                    <button className="bg-red-900/30 hover:bg-red-800/50 text-red-400 px-4 py-2 rounded-full flex items-center gap-2 transition-colors text-sm">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill="currentColor"/>
                        </svg>
                        Delete Account
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ProfileSettings;
