"use client";

import React, { useState } from 'react';
import { AnalyzingIcon } from '../icons/YoutubeIcons';
import { CopyIcon, GeneratingLoadingIcon } from '../icons/icons';

interface ThumbnailDesignerProps {
  generatedTitles: string[];
  gptThumbUrl: string | null;
  isGeneratingGptThumb: boolean;
  gptThumbError: string | null;
  onGenerate: (prompt: string, input_images: string[]) => void;
  setThumbTitle: (title: string) => void;
  thumbTitle: string;
  styles: any[];
  styleIndex: number;
  setStyleIndex: (index: number) => void;
  mainImage: File | null;
  setMainImage: (file: File | null) => void;
  referenceImage: File | null;
  setReferenceImage: (file: File | null) => void;
  backgroundAssets: string;
  setBackgroundAssets: (text: string) => void;
  onClearBackgroundAssets: () => void;
  promptPreview: string;
  setPromptPreview: (prompt: string) => void;
}

const ThumbnailDesigner: React.FC<ThumbnailDesignerProps> = ({
  generatedTitles,
  gptThumbUrl,
  isGeneratingGptThumb,
  gptThumbError,
  onGenerate,
  setThumbTitle,
  thumbTitle,
  styles,
  styleIndex,
  setStyleIndex,
  mainImage,
  setMainImage,
  referenceImage,
  setReferenceImage,
  backgroundAssets,
  setBackgroundAssets,
  onClearBackgroundAssets,
  promptPreview,
  setPromptPreview,
}) => {
  const [showPromptPreview, setShowPromptPreview] = useState(false);

  return (
    <div className="mt-8 space-y-8">
      {/* Title and Background Assets input row */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="rounded-2xl border border-white/10 bg-[#0A0F14] p-6 sm:p-8">
          <div className="mb-4 flex flex-wrap items-center justify-between gap-3">
            <span className="text-sm text-white/80">Enter thumbnail title</span>
            <div className="flex items-center gap-4 text-xs">
              {/* <button className="text-[#8B9BFF] hover:underline" onClick={() => {
                if (generatedTitles[0]) setThumbTitle(generatedTitles[0]);
              }}>Generate title</button> */}
            </div>
          </div>
          <input
            type="text"
            value={thumbTitle}
            onChange={(e) => setThumbTitle(e.target.value)}
            placeholder=""
            className="w-full rounded-full border border-white/15 bg-[#05080A] py-3 px-4 text-sm text-white outline-none focus:border-[#8B5CF6] focus:shadow-[0_0_0_3px_rgba(139,92,246,.25)]"
          />
        </div>

        <div className="rounded-2xl border border-white/10 bg-[#0A0F14] p-6 sm:p-8">
          <div className="mb-4 flex flex-wrap items-center justify-between gap-3">
            <span className="text-sm text-white/80">Background Assets</span>
            <div className="flex items-center gap-4 text-xs">
              {/* <button className="text-white/60 hover:text-white/90" onClick={onClearBackgroundAssets}>Clear background assets</button> */}
            </div>
          </div>
          <input
            type="text"
            value={backgroundAssets}
            onChange={(e) => setBackgroundAssets(e.target.value)}
            placeholder="Enter description for background assets"
            className="w-full rounded-full border border-white/15 bg-[#05080A] py-3 px-4 text-sm text-white outline-none focus:border-[#8B5CF6] focus:shadow-[0_0_0_3px_rgba(139,92,246,.25)]"
          />
        </div>
      </div>

      {/* Style carousel */}
      <div className="rounded-2xl border border-white/10 bg-[#0A0F14] p-4 sm:p-6">
        <div className="mb-4 text-sm text-white/80">Choose thumbnail style</div>
        <div className="relative">
          <div className="flex overflow-x-auto space-x-4 p-2 -mx-2">
            {styles.map((s, idx) => {
              const active = styleIndex === idx;
              return (
                <button
                  key={s.id}
                  onClick={() => setStyleIndex(idx)}
                  className={`group relative h-32 w-48 flex-shrink-0 overflow-hidden rounded-xl border text-left transition
                  ${active ? 'border-[#8B5CF6] shadow-[0_0_0_2px_rgba(139,92,246,.25)]' : 'border-white/10 hover:border-white/20'}`}
                  title={s.label}
                >
                  {s.badge && (
                    <span className="absolute right-2 top-2 z-10 rounded-full bg-[#2B2F57] px-2 py-0.5 text-[10px] text-[#A9B4FF]">{s.badge}</span>
                  )}
                  <img
                    src={s.image}
                    alt={s.label}
                    className="absolute inset-0 h-full w-full object-cover transition-transform duration-300 group-hover:scale-[1.03]"
                  />
                  <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/60 to-transparent p-2 text-xs text-white/80">
                    {s.label}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Upload zones */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="rounded-2xl border border-white/10 bg-[#0A0F14] p-4 sm:p-6">
          <div className="mb-3 text-sm text-white/80">Upload Main Image</div>
          <label className="flex h-28 cursor-pointer items-center justify-between rounded-xl border border-dashed border-white/20 bg-[#05080A] px-4 text-sm text-white/60">
            <span>Choose Files</span>
            <span className="text-white/40">{mainImage ? mainImage.name : 'No file chosen'}</span>
            <input
              type="file"
              accept="image/*"
              className="hidden"
              onChange={(e) => {
                const f = e.target.files?.[0] || null;
                setMainImage(f || null);
              }}
            />
          </label>
        </div>
        <div className="rounded-2xl border border-white/10 bg-[#0A0F14] p-4 sm:p-6">
          <div className="mb-3 text-sm text-white/80">Upload reference image</div>
          <label className="flex h-28 cursor-pointer items-center justify-between rounded-xl border border-dashed border-white/20 bg-[#05080A] px-4 text-sm text-white/60">
            <span>Choose Files</span>
            <span className="text-white/40">{referenceImage ? referenceImage.name : 'No file chosen'}</span>
            <input
              type="file"
              accept="image/*"
              className="hidden"
              onChange={(e) => {
                const f = e.target.files?.[0] || null;
                setReferenceImage(f || null);
              }}
            />
          </label>
        </div>
      </div>

      {/* Generation area */}
      <div className="rounded-2xl border border-white/10 bg-[#0A0F14] p-4 sm:p-6">
        <div className="relative overflow-hidden rounded-xl border border-white/10 bg-[#0B1318]">
          <div className="relative" style={{ width: '100%', minHeight: 260 }}>
            {!gptThumbUrl && (
              <div className="flex h-64 w-full items-center justify-center text-[#A9B4FF]">
                {isGeneratingGptThumb ? (
                  <div className="flex items-center gap-2">
                    <span className="animate-pulse"><AnalyzingIcon /></span>
                    <span>Generating</span>
                    <span className="inline-flex">
                    <GeneratingLoadingIcon />
                    </span>
                  </div>
                ) : (
                  <span> </span>
                )}
              </div>
            )}
            {gptThumbUrl && (
              <img
                src={gptThumbUrl}
                alt="Generated thumbnail (GPT-Image-1)"
                className="h-full w-full object-cover"
              />
            )}
          </div>
        </div>

        {gptThumbError && (
          <div className="mt-4 rounded-md border border-red-500/20 bg-red-500/10 p-3 text-sm text-red-100/90">
            {gptThumbError}
          </div>
        )}

        <div className="mt-4 rounded-xl border border-white/10 bg-[#0B1318]">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="text-sm text-white/80">Prompt Preview</div>
            <div className="flex items-center gap-2">
              <button
                className="text-xs rounded-md px-2 py-1 hover:bg-white/5 text-white/70"
                onClick={() => setShowPromptPreview((v) => !v)}
              >
                {showPromptPreview ? 'Hide' : 'Show'}
              </button>
              <button
                className="text-xs rounded-md px-2 py-1 hover:bg-white/5 text-white/70"
                onClick={() => navigator.clipboard.writeText(promptPreview)}
                disabled={!promptPreview}
              >
                <CopyIcon />
              </button>
            </div>
          </div>
          {showPromptPreview && (
            <div className="px-4 pb-4">
              <textarea
                readOnly
                value={promptPreview}
                className="w-full h-56 resize-y rounded-lg border border-white/10 bg-[#05080A] p-3 text-xs text-white/80"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ThumbnailDesigner;
