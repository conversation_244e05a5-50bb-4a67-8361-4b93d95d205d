import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/mongodb';
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/auth.config";

export async function GET(req: NextRequest) {
  const reqId = `api-recent-clipping-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
  const started = Date.now();
  console.log(`[API][RECENT_CLIPPING][${reqId}] Request received.`);

  // Auth required
  const session = await getServerSession(authOptions);
  if (!session || !session.user || !session.user.id) {
    console.warn(`[API][RECENT_CLIPPING][${reqId}] Unauthorized request or missing user ID in session`);
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  console.log(`[API][RECENT_CLIPPING][${reqId}] User ID: ${session.user.id}`);

  try {
    const { db } = await connectToDatabase();
    const projectsCollection = db.collection('clipping_projects');

    // Fetch projects for the current user, sorted by creation date (newest first)
    const projects = await projectsCollection.find(
      { userId: session.user.id },
      { sort: { createdAt: -1 }, limit: 10 } // Limit to 10 most recent projects
    ).toArray();

    // Map to the ClippingProject interface
    const formattedProjects = projects.map(project => ({
      id: project.projectId,
      title: project.projectName || `Project ${project.projectId}`,
      createdAt: project.createdAt.toISOString(),
      thumbnailUrl: project.thumbnailUrl, // Assuming thumbnailUrl might be saved
    }));

    console.log(`[API][RECENT_CLIPPING][${reqId}] Fetched ${formattedProjects.length} projects in ${Date.now() - started}ms`);
    console.log(`[API][RECENT_CLIPPING][${reqId}] Projects data:`, formattedProjects);
    return NextResponse.json({ projects: formattedProjects });
  } catch (error) {
    console.error(`[API][RECENT_CLIPPING][${reqId}] Error fetching recent clipping projects:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch recent clipping projects' },
      { status: 500 }
    );
  }
}