import React from 'react';
import { ImagetoImageicon, ImagetoVideoicon, Musicgeneratoricon, SoundEffect, TexttoImageIcon, TexttoSpeechicon, TexttoVideoicon, LipSyncIcon } from "../components/icons/icons";

type Mode = 'text-to-image' | 'image-to-image' | 'text-to-video' | 'image-to-video' | 'lip-sync' | 'text-to-speech' | 'music-generation' | 'sound-effects';

interface ToolSelectionButtonsProps {
  activeMode: Mode;
  handleModeChange: (mode: Mode) => void;
}

const ToolSelectionButtons: React.FC<ToolSelectionButtonsProps> = ({ activeMode, handleModeChange }) => {
  return (
    <div className="flex flex-wrap gap-1 justify-between">
      {/* Text to Image */}
      <button
        className={`flex flex-col items-center justify-center p-1 sm:p-2 rounded-lg border transition-all duration-200 min-h-[50px] sm:min-h-[60px] md:min-h-[70px] relative flex-shrink-0 ${activeMode === 'text-to-image'
          ? 'text-[#6366f1] border-none '
          : ' border-none text-gray-400 hover:border-[#4a4a6e] hover:text-white'
          }`}
        onClick={() => handleModeChange('text-to-image')}
      >
        <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 flex items-center justify-center mb-1">
          {/* SVG for Text to Image */}
          <TexttoImageIcon />
        </div>
        <span className="text-xs sm:text-sm font-medium text-center leading-tight">Text to Image</span>
        {activeMode === 'text-to-image' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-white to-[#6366f1] rounded-full"></div>
        )}
      </button>
      {/* Image to Image */}
      <button
        className={`flex flex-col items-center justify-center p-2 rounded-lg border transition-all duration-200 min-h-[70px] relative flex-shrink-0 ${activeMode === 'image-to-image'
          ? 'text-[#6366f1] border-none '
          : ' border-none text-gray-400 hover:border-[#4a4a6e] hover:text-white'
          }`}
        onClick={() => handleModeChange('image-to-image')}
      >
        <div className="w-6 h-6 flex items-center justify-center mb-1">
          <ImagetoImageicon />
        </div>
        <span className="text-xs sm:text-sm font-medium text-center leading-tight">Image to Image</span>
        {activeMode === 'image-to-image' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-white to-[#6366f1] rounded-full"></div>
        )}
      </button>
      {/* Text to Video */}
      <button
        className={`flex flex-col items-center justify-center p-2 rounded-lg border transition-all duration-200 min-h-[70px] relative flex-shrink-0 ${activeMode === 'text-to-video'
          ? 'text-[#6366f1] border-none '
          : ' border-none text-gray-400 hover:border-[#4a4a6e] hover:text-white'
          }`}
        onClick={() => handleModeChange('text-to-video')}
      >
        <div className="w-6 h-6 flex items-center justify-center mb-1">
          <TexttoVideoicon />
        </div>
        <span className="text-xs sm:text-sm font-medium text-center leading-tight">Text to Video</span>
        {activeMode === 'text-to-video' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-white to-[#6366f1] rounded-full"></div>
        )}
      </button>
      {/* Image to Video */}
      <button
        className={`flex flex-col items-center justify-center p-2 rounded-lg border transition-all duration-200 min-h-[70px] relative flex-shrink-0 ${activeMode === 'image-to-video'
          ? 'text-[#6366f1] border-none '
          : ' border-none text-gray-400 hover:border-[#4a4a6e] hover:text-white'
          }`}
        onClick={() => handleModeChange('image-to-video')}
      >
        <div className="w-6 h-6 flex items-center justify-center mb-1">
          <ImagetoVideoicon />
        </div>
        <span className="text-xs sm:text-sm font-medium text-center leading-tight">Image to Video</span>
        {activeMode === 'image-to-video' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-white to-[#6366f1] rounded-full"></div>
        )}
      </button>
      {/* Lip Sync */}
      <button
        className={`flex flex-col items-center justify-center p-2 rounded-lg border transition-all duration-200 min-h-[70px] relative flex-shrink-0 ${activeMode === 'lip-sync'
          ? 'text-[#6366f1] border-none '
          : ' border-none text-gray-400 hover:border-[#4a4a6e] hover:text-white'
          }`}
        onClick={() => handleModeChange('lip-sync')}
      >
        <div className="w-6 h-6 flex items-center justify-center mb-1">
          <LipSyncIcon />
        </div>
        <span className="text-xs sm:text-sm font-medium text-center leading-tight">Lip Sync</span>
        {activeMode === 'lip-sync' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-white to-[#6366f1] rounded-full"></div>
        )}
      </button>
      {/* Text to Speech */}
      <button
        className={`flex flex-col items-center justify-center p-2 rounded-lg border transition-all duration-200 min-h-[70px] relative flex-shrink-0 ${activeMode === 'text-to-speech'
          ? 'text-[#6366f1] border-none '
          : ' border-none text-gray-400 hover:border-[#4a4a6e] hover:text-white'
          }`}
        onClick={() => handleModeChange('text-to-speech')}
      >
        <div className="w-6 h-6 flex items-center justify-center mb-1">
          <TexttoSpeechicon />
        </div>
        <span className="text-xs sm:text-sm font-medium text-center leading-tight">Text to Speech</span>
        {activeMode === 'text-to-speech' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-white to-[#6366f1] rounded-full"></div>
        )}
      </button>
      {/* Music Generator */}
      <button
        className={`flex flex-col items-center justify-center p-2 rounded-lg border transition-all duration-200 min-h-[70px] relative flex-shrink-0 ${activeMode === 'music-generation'
          ? 'text-[#6366f1] border-none '
          : ' border-none text-gray-400 hover:border-[#4a4a6e] hover:text-white'
          }`}
        onClick={() => handleModeChange('music-generation')}
      >
        <div className="w-6 h-6 flex items-center justify-center mb-1">
          {/* SVG for Music Generator */}
          <Musicgeneratoricon />

        </div>
        <span className="text-xs sm:text-sm font-medium text-center leading-tight">Music Generator</span>
        {activeMode === 'music-generation' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-white to-[#6366f1] rounded-full"></div>
        )}
      </button>
      {/* Sound Effects */}
      <button
        className={`flex flex-col items-center justify-center p-2 rounded-lg border transition-all duration-200 min-h-[70px] relative flex-shrink-0 ${activeMode === 'sound-effects'
          ? 'text-[#6366f1] border-none '
          : ' border-none text-gray-400 hover:border-[#4a4a6e] hover:text-white'
          }`}
        onClick={() => handleModeChange('sound-effects')}
      >
        <div className="w-6 h-6 flex items-center justify-center mb-1">
          <SoundEffect />
        </div>
        <span className="text-xs sm:text-sm font-medium text-center leading-tight">Sound Effects</span>
        {activeMode === 'sound-effects' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-white to-[#6366f1] rounded-full"></div>
        )}
      </button>
    </div>
  );
};

export default ToolSelectionButtons; 