import { NextRequest, NextResponse } from 'next/server';
import cloudinary from '../../../lib/cloudinary';

export async function POST(request: NextRequest) {
  try {
    console.log('Upload API called');
    
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      console.log('No file provided');
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    console.log('File received:', {
      name: file.name,
      type: file.type,
      size: file.size
    });

    // Validate file type
    const allowedVideoTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
    const allowedAudioTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/wave'];
    const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const allowedTypes = [...allowedVideoTypes, ...allowedAudioTypes, ...allowedImageTypes];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only video (MP4, AVI, MOV, WMV), audio (MP3, WAV), and image (JPEG, PNG, WebP) files are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size (100MB limit)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 100MB.' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Determine resource type based on file type
    const isVideo = allowedVideoTypes.includes(file.type);
    const isImage = allowedImageTypes.includes(file.type);
    const resourceType = isVideo ? 'video' : isImage ? 'image' : 'raw';

    console.log('Uploading to Cloudinary:', {
      resourceType,
      isVideo,
      isImage,
      fileType: file.type
    });

    // Upload to Cloudinary
    const uploadResult = await new Promise((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          resource_type: resourceType,
          folder: 'lipsync-uploads',
          public_id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          format: isVideo ? 'mp4' : undefined, // Let Cloudinary auto-detect audio and image formats
        },
        (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve(result);
          }
        }
      );

      uploadStream.end(buffer);
    });

    const cloudinaryResult = uploadResult as any;

    console.log('Upload successful:', {
      url: cloudinaryResult.secure_url,
      publicId: cloudinaryResult.public_id,
      fileName: file.name
    });

    return NextResponse.json({
      success: true,
      url: cloudinaryResult.secure_url,
      publicId: cloudinaryResult.public_id,
      fileName: file.name
    });

  } catch (error) {
    console.error('Upload error:', error);
    
    // Handle Cloudinary-specific errors
    if (error instanceof Error) {
      if (error.message.includes('cloudinary')) {
        return NextResponse.json(
          { error: 'File upload service error. Please try again.' },
          { status: 500 }
        );
      }
      if (error.message.includes('authentication') || error.message.includes('credentials')) {
        return NextResponse.json(
          { error: 'Upload service authentication failed. Please check configuration.' },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to upload file', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 