"use client";
import React, { useState } from "react";
import { KeywordInsight, formatVolume, getCompetitionColor, formatTrend, getTrendColor, formatKeywordsForCopy } from "../types/keyword-insights";
import { CopyIcon, ShareIcon } from "./icons/icons";
// import { ShareIcon } from "./icons/ShareIcon";

interface KeywordInsightsTableProps {
  data: KeywordInsight[];
  isLoading: boolean;
  error: string | null;
}

export default function KeywordInsightsTable({ data, isLoading, error }: KeywordInsightsTableProps) {
  const [selectedKeywords, setSelectedKeywords] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [showCopiedMessage, setShowCopiedMessage] = useState(false);

  const handleShare = async () => {
    const keywordsToShare = Array.from(selectedKeywords).map(id => {
      const keyword = data.find(k => k.id === id);
      return keyword ? keyword.text : '';
    }).filter(Boolean).join('\n');

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Keyword Insights',
          text: keywordsToShare,
        });
        console.log('Keywords shared successfully');
      } catch (error) {
        console.error('Error sharing keywords:', error);
      }
    } else {
      alert('Web Share API is not supported in your browser.');
    }
  };


  // Handle individual checkbox selection
  const handleKeywordSelect = (index: number) => {
    const newSelected = new Set(selectedKeywords);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedKeywords(newSelected);
    setSelectAll(newSelected.size === data.length);
  };

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedKeywords(new Set());
      setSelectAll(false);
    } else {
      setSelectedKeywords(new Set(data.map((_, index) => index)));
      setSelectAll(true);
    }
  };

  // Copy selected keywords to clipboard
  const copySelectedKeywords = async () => {
    if (selectedKeywords.size === 0) return;

    const selectedData = Array.from(selectedKeywords).map(index => data[index]);
    const formattedData = formatKeywordsForCopy(selectedData);

    try {
      await navigator.clipboard.writeText(formattedData);
      setShowCopiedMessage(true);
      setTimeout(() => setShowCopiedMessage(false), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = formattedData;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setShowCopiedMessage(true);
        setTimeout(() => setShowCopiedMessage(false), 2000);
      } catch (fallbackErr) {
        console.error('Fallback copy failed:', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };

  if (error) {
    return (
      <div className="mt-8 p-6 border border-red-500/20 rounded-xl bg-red-500/10">
        <p className="text-red-400 text-center">Error: {error}</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="mt-8 p-6 border border-[#2a2a3e] rounded-xl">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6246ea]"></div>
          <span className="ml-3 text-gray-400">Loading keyword insights...</span>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="mt-8 p-6 border border-[#2a2a3e] rounded-xl">
        <p className="text-gray-400 text-center">No keyword data available. Search for keywords to see insights.</p>
      </div>
    );
  }

  // Copy all keywords
  const copyAllKeywords = async () => {
    const formattedData = formatKeywordsForCopy(data);

    try {
      await navigator.clipboard.writeText(formattedData);
      setShowCopiedMessage(true);
      setTimeout(() => setShowCopiedMessage(false), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  return (
    <div className="mt-8">
      {/* Copy Controls - Always show if there's data */}
      {data.length > 0 && (
        <div className="mb-4 flex items-center justify-between p-4 bg-[#1a1a2e] border border-[#2a2a3e] rounded-xl">
          <div className="flex items-center gap-4">
            <span className="text-gray-300">
              {selectedKeywords.size > 0
                ? `${selectedKeywords.size} keyword${selectedKeywords.size !== 1 ? 's' : ''} selected`
                : `${data.length} total keywords`
              }
            </span>

          </div>
          <div className="flex gap-2 items-center">
            {showCopiedMessage && (
              <span className="text-green-400 text-sm flex items-center gap-1">
                {/* <DownloadIcon />  */}
                Keywords Copied!
              </span>
            )}
            {selectedKeywords.size > 0 && (
              <button
                onClick={copySelectedKeywords}
                className="p-2 bg-[#6246ea] text-white rounded-full hover:bg-[#5235d1] transition-colors flex items-center justify-center"
                title="Copy Selected Keywords"
              >
                <CopyIcon />
              </button>
            )}
            <button
              onClick={copyAllKeywords}
              className="p-2 bg-gray-600 text-white rounded-full hover:bg-gray-500 transition-colors flex items-center justify-center"
              title="Copy All Keywords"
            >
              <CopyIcon />
            </button>
            <button
              onClick={handleShare}
              className="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-500 transition-colors flex items-center justify-center"
              title="Share Keywords"
            >
              <ShareIcon />
            </button>
          </div>
        </div>
      )}



      <div className="overflow-x-auto border border-[#2a2a3e] rounded-xl">
        <table className="min-w-full text-sm">
          <thead className="bg-[#101624] text-gray-400 uppercase text-xs tracking-wider">
            <tr>
              <th className="px-6 py-4 text-left w-8">
                <input
                  type="checkbox"
                  className="accent-[#6246ea]"
                  checked={selectAll}
                  onChange={handleSelectAll}
                />
              </th>
              <th className="px-6 py-4 text-left">Keyword</th>
              <th className="px-6 py-4 text-left">Search Volume</th>
              <th className="px-6 py-4 text-left">Competition</th>
              <th className="px-6 py-4 text-left">Competition Index</th>
              <th className="px-6 py-4 text-left">Trend</th>
              {/* <th className="px-6 py-4 text-left">Bid Range</th> */}
              <th className="px-6 py-4 text-left">Word Count</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-[#1f2937]">
            {data.map((row, idx) => (
              <tr key={idx} className="hover:bg-[#1a1a2e] transition-colors">
                <td className="px-6 py-3 w-8">
                  <input
                    type="checkbox"
                    className="accent-[#6246ea]"
                    checked={selectedKeywords.has(idx)}
                    onChange={() => handleKeywordSelect(idx)}
                  />
                </td>
                <td className="px-6 py-3 text-gray-100 whitespace-nowrap">{row.text}</td>
                <td className="px-6 py-3 text-gray-100">{formatVolume(row.volume)}</td>
                <td className={`px-6 py-3 ${getCompetitionColor(row.competition_level)}`}>
                  {row.competition_level}
                </td>
                <td className="px-6 py-3 text-gray-100">{row.competition_index}</td>
                <td className={`px-6 py-3 ${getTrendColor(row.trend)}`}>
                  {formatTrend(row.trend)}
                </td>
                {/* <td className="px-6 py-3 text-gray-100">
                  ${row.low_bid.toFixed(2)} - ${row.high_bid.toFixed(2)}
                </td> */}
                <td className="px-6 py-3 text-gray-100">{row.text.split(' ').length}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
