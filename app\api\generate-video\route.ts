import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';
import cloudinary from '../../../lib/cloudinary';

// Helper function to upload video to Cloudinary
async function uploadToCloudinary(dataUrl: string): Promise<string> {
  try {
    const result = await cloudinary.uploader.upload(dataUrl, {
      resource_type: 'video',
      folder: 'ai-generated-videos', // Optional: organize in a folder
    });
    return result.secure_url;
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw new Error('Failed to upload video to Cloudinary');
  }
}

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function POST(req: NextRequest) {
  try {
    const {
      prompt,
      mode,
      imageUrl,
      duration = 5,
      aspectRatio = "16:9",
      model = "kling-v1.6-standard"
    } = await req.json();

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    // Validate image-to-video mode requirements
    if (mode === 'image-to-video' && !imageUrl) {
      return NextResponse.json(
        { error: "Image URL is required for image-to-video mode" },
        { status: 400 }
      );
    }

    // Model configuration
    const modelConfigs = {
      "kling-v1.6-standard": {
        name: "kwaivgi/kling-v1.6-standard",
        supportedModes: ["text-to-video", "image-to-video"],
        supportedDurations: [5, 10],
        supportedAspectRatios: ["16:9", "9:16", "1:1"],
        imageParam: "start_image"
      },
      "google-veo-3": {
        name: "google/veo-3",
        supportedModes: ["text-to-video", "image-to-video"],
        supportedDurations: [5, 10],
        supportedAspectRatios: ["16:9", "9:16", "1:1"],
        imageParam: "image"
      },
      "wan-2.1-i2v-480p": {
        name: "wavespeedai/wan-2.1-i2v-480p",
        supportedModes: ["image-to-video"],
        supportedDurations: [5],
        supportedAspectRatios: ["16:9", "9:16", "1:1"],
        imageParam: "image"
      },
      "wan-2.1-i2v-720p": {
        name: "wavespeedai/wan-2.1-i2v-720p",
        supportedModes: ["image-to-video"],
        supportedDurations: [5],
        supportedAspectRatios: ["16:9", "9:16", "1:1"],
        imageParam: "image"
      },
      "wan-2.1-t2v-480p": {
        name: "wavespeedai/wan-2.1-t2v-480p",
        supportedModes: ["text-to-video"],
        supportedDurations: [5],
        supportedAspectRatios: ["16:9", "9:16", "1:1"],
        imageParam: null
      },
      "wan-2.1-t2v-720p": {
        name: "wavespeedai/wan-2.1-t2v-720p",
        supportedModes: ["text-to-video"],
        supportedDurations: [5],
        supportedAspectRatios: ["16:9", "9:16", "1:1"],
        imageParam: null
      },
      "minimax-video-01": {
        name: "minimax/video-01",
        supportedModes: ["text-to-video", "image-to-video"],
        supportedDurations: [6],
        supportedAspectRatios: ["16:9", "9:16", "1:1"],
        imageParam: "image_url"
      }
    };

    const selectedModel = modelConfigs[model as keyof typeof modelConfigs];
    if (!selectedModel) {
      return NextResponse.json(
        { error: `Unsupported model: ${model}` },
        { status: 400 }
      );
    }

    // Validate mode support
    if (!selectedModel.supportedModes.includes(mode)) {
      return NextResponse.json(
        { error: `Model ${model} does not support ${mode} mode` },
        { status: 400 }
      );
    }

    // Validate duration
    const videoDuration = selectedModel.supportedDurations.includes(duration)
      ? duration
      : selectedModel.supportedDurations[0];

    // Validate aspect ratio
    const videoAspectRatio = selectedModel.supportedAspectRatios.includes(aspectRatio)
      ? aspectRatio
      : selectedModel.supportedAspectRatios[0];

    // Build input parameters based on model
    let input: any = {
      prompt: prompt,
    };

    // Add model-specific parameters
    if (model.startsWith("kling")) {
      input.duration = videoDuration;
      input.aspect_ratio = videoAspectRatio;
    } else if (model.startsWith("google-veo")) {
      input.duration = videoDuration;
      input.aspect_ratio = videoAspectRatio;
    } else if (model.startsWith("wan-2.1")) {
      // Wan models have different parameter structure
      input.num_frames = videoDuration * 24; // Assuming 24fps
      input.width = videoAspectRatio === "16:9" ? 854 : videoAspectRatio === "9:16" ? 480 : 512;
      input.height = videoAspectRatio === "16:9" ? 480 : videoAspectRatio === "9:16" ? 854 : 512;
    } else if (model.startsWith("minimax")) {
      // MiniMax has 6-second fixed duration
      input.duration = 6;
    }

    // Handle image-to-video mode
    if (mode === 'image-to-video' && imageUrl && selectedModel.imageParam) {
      input[selectedModel.imageParam] = imageUrl;
    }

    const modelName = selectedModel.name as `${string}/${string}`;

    console.log(`Generating video with model: ${modelName}`);
    console.log(`Input parameters:`, input);

    const output = await replicate.run(modelName, { input });

    console.log('Raw Replicate output:', output);
    console.log('Output type:', typeof output);
    console.log('Is array:', Array.isArray(output));

    // Handle different output formats from Replicate
    let videoUrls: string[] = [];

    // Check if output is an async iterable (common for Replicate streaming)
    if (output && typeof output === 'object' && Symbol.asyncIterator in output) {
      console.log('Output is an async iterable, collecting binary data...');
      const chunks: Uint8Array[] = [];

      try {
        for await (const chunk of output as AsyncIterable<Uint8Array>) {
          if (chunk instanceof Uint8Array) {
            chunks.push(chunk);
          }
        }

        console.log(`Collected ${chunks.length} chunks of binary data`);

        if (chunks.length > 0) {
          // Combine all chunks into a single buffer
          const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
          const combinedBuffer = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of chunks) {
            combinedBuffer.set(chunk, offset);
            offset += chunk.length;
          }

          // Convert to base64 data URL for video
          const base64String = Buffer.from(combinedBuffer).toString('base64');
          const dataUrl = `data:video/mp4;base64,${base64String}`;
          const cloudinaryUrl = await uploadToCloudinary(dataUrl);
          videoUrls = [cloudinaryUrl];

          console.log('Successfully created data URL from binary data');
        }

      } catch (e) {
        console.error('Error reading async iterator:', e);
      }
    } else if (Array.isArray(output)) {
      console.log('Output is an array, processing each item...');

      for (const item of output) {
        if (typeof item === 'string') {
          videoUrls.push(item);
        } else if (item && typeof item === 'object' && Symbol.asyncIterator in item) {
          // Handle ReadableStream in array
          console.log('Found ReadableStream in array, collecting binary data...');
          const chunks: Uint8Array[] = [];

          try {
            for await (const chunk of item as AsyncIterable<Uint8Array>) {
              if (chunk instanceof Uint8Array) {
                chunks.push(chunk);
              }
            }

            console.log(`Collected ${chunks.length} chunks from array item`);

            if (chunks.length > 0) {
              // Combine all chunks into a single buffer
              const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
              const combinedBuffer = new Uint8Array(totalLength);
              let offset = 0;

              for (const chunk of chunks) {
                combinedBuffer.set(chunk, offset);
                offset += chunk.length;
              }

              // Convert to base64 data URL for video
              const base64String = Buffer.from(combinedBuffer).toString('base64');
              const dataUrl = `data:video/mp4;base64,${base64String}`;
              const cloudinaryUrl = await uploadToCloudinary(dataUrl);
              videoUrls.push(cloudinaryUrl);

              console.log('Successfully created data URL from array item binary data');
            }
          } catch (e) {
            console.error('Error reading async iterator from array item:', e);
          }
        } else if (item && typeof item === 'object') {
          const obj = item as any;
          if (typeof obj.url === 'string') {
            videoUrls.push(obj.url);
          } else if (typeof obj.video === 'string') {
            videoUrls.push(obj.video);
          }
        }
      }
    } else if (typeof output === 'string') {
      // If output is a single string URL
      videoUrls = [output];
    } else if (output && typeof output === 'object') {
      // If output is an object, try to extract URL
      const obj = output as any;
      if (obj.url) {
        videoUrls = [obj.url];
      } else if (obj.video) {
        videoUrls = [obj.video];
      } else if (obj.videos && Array.isArray(obj.videos)) {
        videoUrls = obj.videos.filter(Boolean);
      }
    }

    console.log('Processed video URLs:', videoUrls);

    // Validate that we have at least one valid video URL
    if (!videoUrls || videoUrls.length === 0) {
      console.error('No valid video URLs found in output:', output);
      return NextResponse.json(
        {
          error: "No videos generated",
          details: "The model did not return any valid video URLs",
          rawOutput: output
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      videos: videoUrls,
      model: modelName,
      modelKey: model,
      prompt: prompt,
      mode: mode,
      duration: videoDuration,
      aspectRatio: videoAspectRatio,
      parameters: {
        ...input,
        ...(mode === 'image-to-video' && { [selectedModel.imageParam || 'image']: imageUrl })
      }
    });

  } catch (error: any) {
    console.error("Video generation error:", error);
    
    // Handle specific Replicate errors
    if (error.message?.includes('authentication')) {
      return NextResponse.json(
        { error: "Authentication failed. Please check API token." },
        { status: 401 }
      );
    }
    
    if (error.message?.includes('rate limit')) {
      return NextResponse.json(
        { error: "Rate limit exceeded. Please try again later." },
        { status: 429 }
      );
    }

    return NextResponse.json(
      { 
        error: "Failed to generate video", 
        details: error.message || "Unknown error occurred"
      },
      { status: 500 }
    );
  }
}
