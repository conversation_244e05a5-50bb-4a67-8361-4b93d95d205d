"use client";
import { useState } from 'react';
import Image from 'next/image';
import type { ImageData, ImageGridProps } from './types';
import { DownloadIcon } from './icons/icons';

export default function ImageGrid({
  images,
  onImageClick,
  onDownload,
  columns = "columns-1 sm:columns-2 md:columns-3 lg:columns-4"
}: ImageGridProps) {
  const [hoveredImage, setHoveredImage] = useState<number | null>(null);

  return (
    <section className="px-3 sm:px-4 md:px-6 py-4 sm:py-6 flex-1 bg-[#0a0f0d] overflow-y-auto">
      <h1 className="text-2xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-white tracking-tight mb-4 sm:mb-6">
        {" create faster "}
        <span className="text-[#6b7280] font-bold">create smarter</span>
      </h1>
      <div className={`${columns} gap-2 sm:gap-3`}>
        {images.map((img, i) => (
          <div
            key={i}
            className="relative group break-inside-avoid rounded-lg overflow-hidden bg-[#1a1a1a] shadow-lg mb-2 sm:mb-3 cursor-pointer border border-[#2a2a2a] hover:border-[#3a3a3a] transition-all duration-300"
            onMouseEnter={() => setHoveredImage(i)}
            onMouseLeave={() => setHoveredImage(null)}
            onClick={() => onImageClick(img)}
          >
            <Image
              src={img.src}
              alt={`Inspiration ${i + 1}`}
              width={400}
              height={400}
              className={`object-cover w-full ${img.height} transition-all duration-300 group-hover:scale-[1.02]`}
            />
            {/* Hover overlay */}
            <div className={`absolute inset-0 bg-black bg-opacity-10 transition-opacity duration-200 ${hoveredImage === i ? 'opacity-100' : 'opacity-0'
              }`} />
            <button
              className={`absolute top-2 right-2 sm:top-3 sm:right-3   transition-all duration-200 ${hoveredImage === i ? 'opacity-100 scale-100' : 'opacity-0 scale-95 sm:opacity-0 sm:scale-95'
                } touch:opacity-100 touch:scale-100`}
              onClick={e => {
                e.stopPropagation();
                onDownload(img.src);
              }}
              title="Download"
            >

              <DownloadIcon />
            </button>
          </div>
        ))}
      </div>
    </section>
  );
}
