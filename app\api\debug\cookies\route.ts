import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  try {
    // Collect request cookies
    const cookiesArr = Array.from(req.cookies.getAll()).map((c) => ({
      name: c.name,
      valuePreview: c.value?.slice(0, 20) ?? null, // avoid dumping full sensitive values
      length: c.value?.length ?? 0,
    }));

    // Also include headers relevant to cookies/auth for debugging
    const headers = {
      host: req.headers.get("host"),
      origin: req.headers.get("origin"),
      referer: req.headers.get("referer"),
      cookieHeaderPresent: req.headers.has("cookie"),
      userAgent: req.headers.get("user-agent"),
      xForwardedProto: req.headers.get("x-forwarded-proto"),
      xForwardedHost: req.headers.get("x-forwarded-host"),
    };

    // Identify typical next-auth cookies by name
    const nextAuthCookies = cookiesArr.filter((c) =>
      c.name.includes("next-auth") || c.name.includes("Session")
    );

    console.log("[debug][cookies] total cookies:", cookiesArr.length, "next-auth related:", nextAuthCookies.length);

    return NextResponse.json({
      ok: true,
      summary: {
        totalCookies: cookiesArr.length,
        nextAuthRelatedCount: nextAuthCookies.length,
      },
      nextAuthCookies,
      allCookies: cookiesArr,
      headers,
    });
  } catch (err: any) {
    console.error("[debug][cookies] error", err?.message);
    return NextResponse.json(
      { ok: false, error: err?.message ?? "unknown error" },
      { status: 500 }
    );
  }
}