"use client";
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  DashboardLayout,
  Header,
  Sidebar,
} from '../components';
import KeywordSearchCard from '../components/KeywordSearchCard';
import KeywordInsightsTable from '../components/KeywordInsightsTable';
import { useChatHistory } from '../hooks/useChatHistory';
import { Accounticon, Clippingicon, IdeaLabicon, Inspirationicon, Keywordicon, SocialPosticon, Storyboardicon, Youtubeicon } from '../components/icons/icons';
import { KeywordInsight, KeywordSearchState } from '../types/keyword-insights';

// Icon components for better consistency


const sidebarMenu = [
  { label: "Inspiration", icon: <Inspirationicon />, active: false },
  { label: "Idea Lab", icon: <IdeaLabicon />, active: false },
  { label: "Social Post", icon: <SocialPosticon />, active: false },
  { label: "Clipping", icon: <Clippingicon />, active: false },
  { label: "Storyboard Editor", icon: <Storyboardicon />, active: false },
  { label: "YouTube Tools", icon: <Youtubeicon />, active: false },
  { label: "Keyword Insights", icon: <Keywordicon />, active: true },
  { label: "Account", icon: <Accounticon />, active: false },
];

export default function KeywordInsights() {
  const router = useRouter();

  // Chat history management for sidebar
  const {
    historySections,
    isLoading,
    clearAllHistory
  } = useChatHistory();

  // Keyword search state
  const [keywordState, setKeywordState] = useState<KeywordSearchState>({
    isLoading: false,
    data: [],
    error: null,
    searchQuery: '',
    activeTab: 'related'
  });

  // Filter data based on active tab
  const filteredData = keywordState.data.filter(keyword => {
    if (keywordState.activeTab === 'matching' && keywordState.searchQuery) {
      return keyword.text.toLowerCase().includes(keywordState.searchQuery.toLowerCase());
    }
    return true;
  });

  // Handle keyword search
  const handleSearch = async (query: string) => {
    setKeywordState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      searchQuery: query
    }));

    try {
      const response = await fetch(`/api/keyword-insights?keyword=${encodeURIComponent(query)}`);
      const result = await response.json();

      if (result.success) {
        setKeywordState(prev => ({
          ...prev,
          isLoading: false,
          data: result.data ? result.data.map((item: KeywordInsight, index: number) => ({ ...item, id: index })) : [],
          error: null
        }));
      } else {
        setKeywordState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Failed to fetch keyword insights'
        }));
      }
    } catch (error) {
      setKeywordState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Network error. Please try again.'
      }));
    }
  };

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setKeywordState(prev => ({
      ...prev,
      activeTab: tab
    }));
  };

  // Handle menu item clicks with navigation
  const handleMenuItemClick = (item: any) => {
    const routes: { [key: string]: string } = {
      "Inspiration": "/dashboard",
      "Idea Lab": "/idealab",
      "Social Post": "/social-post",
      "Clipping": "/clipping",
      "Storyboard Editor": "/storyboard",
      "YouTube Tools": "/youtube-tools",
      "Keyword Insights": "/keyword-insights",
      "Account": "/account"
    };

    const route = routes[item.label];
    if (route) {
      router.push(route);
    }
  };

  // Handle history item click to navigate to Idea Lab
  const handleHistoryItemClick = (historyItem: any) => {
    if (historyItem.sessionId) {
      // Navigate to Idea Lab with the session
      router.push('/idealab');
    }
  };

  // Handle clear history
  const handleClearHistory = () => {
    clearAllHistory();
  };

  return (
    <DashboardLayout
      header={<Header />}
      sidebar={
        <Sidebar
          menuItems={sidebarMenu}
          histories={historySections}
          isLoading={isLoading}
          onMenuItemClick={handleMenuItemClick}
          onHistoryItemClick={handleHistoryItemClick}
          onClearHistory={handleClearHistory}
        />
      }
    >
      <div className="flex-1 p-6 overflow-auto">
        <div className="max-w-7xl mx-auto">
          <KeywordSearchCard
            onSearch={handleSearch}
            onTabChange={handleTabChange}
            isLoading={keywordState.isLoading}
          />
          <KeywordInsightsTable
            data={filteredData}
            isLoading={keywordState.isLoading}
            error={keywordState.error}
          />
        </div>
      </div>
    </DashboardLayout>
  );
}
