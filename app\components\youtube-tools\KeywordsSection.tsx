"use client";

import React from 'react';
import { KeywordInsightsIcon } from '../icons/YoutubeIcons';
import { CopyIcon, ShareIcon } from '../icons/icons';

interface KeywordsSectionProps {
  keywords: any;
  copyToClipboard: (text: string) => void;
  shareContent: (title: string, text: string) => void;
  regenerateSection: (type: 'titles' | 'description' | 'keywords' | 'hashtags' | 'all') => void;
}

const KeywordsSection: React.FC<KeywordsSectionProps> = ({ keywords, copyToClipboard, shareContent, regenerateSection }) => {
  const allKeywords = [
    ...(keywords.longtail || []),
    ...(keywords.midtail || []),
    ...(keywords.shorttail || [])
  ];

  if (allKeywords.length === 0) return null;

  return (
    <div className="mt-8 rounded-2xl border border-white/10 bg-[#0A0F14] p-6 sm:p-8">
      <div className="mb-5 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-[#8B5CF6]"><KeywordInsightsIcon /></span>
          <h3 className="text-lg font-semibold">Keyword Insights</h3>
        </div>
        <div className="flex items-center gap-2 text-white/70">
          <button
            onClick={() => copyToClipboard(allKeywords.join(', '))}
            className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
            title="Copy"
          >
            <CopyIcon /><span>Copy</span>
          </button>
          <button
            onClick={() => shareContent('Keyword Insights', allKeywords.join(', '))}
            className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
            title="Share"
          >
            <ShareIcon /><span>Share</span>
          </button>
          <button
            onClick={() => regenerateSection('keywords')}
            className="rounded-md px-2 py-1 text-xs hover:bg-white/5"
          >
            Redo
          </button>
        </div>
      </div>
      <div className="flex flex-wrap gap-2">
        {allKeywords.map((kw: string, i: number) => (
          <span key={i} className="rounded-full border border-white/10 bg-[#0B1318] px-3 py-1 text-xs text-white/85">{kw}</span>
        ))}
      </div>
    </div>
  );
};

export default KeywordsSection;
