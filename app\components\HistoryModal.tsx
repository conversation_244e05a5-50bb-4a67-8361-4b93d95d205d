import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom';
import type { HistorySection, HistoryItem } from './types';

interface HistoryModalProps {
  open: boolean;
  onClose: () => void;
  histories: HistorySection[];
  onHistoryItemClick: (historyItem: HistoryItem, sectionIndex: number, itemIndex: number) => void;
}

const HistoryModal: React.FC<HistoryModalProps> = ({ open, onClose, histories, onHistoryItemClick }) => {
  const [search, setSearch] = useState('');
  const [isBrowser, setIsBrowser] = useState(false);

  useEffect(() => {
    setIsBrowser(true);
  }, []);

  // Flatten all history items for search
  const allItems = histories.flatMap((section, sectionIndex) =>
    section.items.map((item, itemIndex) => ({
      ...item,
      sectionTitle: section.title,
      sectionIndex,
      itemIndex
    }))
  );

  const filteredItems = search.trim()
    ? allItems.filter(item =>
        item.text.toLowerCase().includes(search.toLowerCase()) ||
        (item.sectionTitle && item.sectionTitle.toLowerCase().includes(search.toLowerCase()))
      )
    : allItems;

  const modalContent = open ? (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60" onClick={onClose}>
      <div className="bg-[#0a0f0d] rounded-2xl shadow-lg w-full max-w-2xl max-h-[80vh] flex flex-col p-6 relative border border-gray-700" onClick={e => e.stopPropagation()}>
        <div className="relative mb-4">
          <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path></svg>
          </div>
          <input
            type="text"
            className="w-full px-4 py-3 pl-12 rounded-full border border-gray-600 bg-[#18181b] text-white placeholder-[#888] focus:outline-none focus:border-[#6366f1]"
            placeholder="Search chats..."
            value={search}
            onChange={e => setSearch(e.target.value)}
            autoFocus
          />
        </div>
        <div className="flex-1 overflow-y-auto pr-2" style={{ scrollbarWidth: 'thin', scrollbarColor: '#4B5563 #1F2937' }}>
          {filteredItems.length === 0 ? (
            <div className="text-[#888] text-center py-8">No chats found.</div>
          ) : (
            <ul>
              {filteredItems.map((item, idx) => (
                <li key={item.sectionTitle + '-' + item.itemIndex + '-' + item.text}>
                  <button
                    className="w-full text-left px-4 py-3 rounded-lg hover:bg-[#1a1a1a] text-[#e5e7eb] mb-2"
                    onClick={() => {
                      onHistoryItemClick(item, item.sectionIndex, item.itemIndex);
                      onClose();
                    }}
                    title={item.text}
                  >
                    <div className="flex items-start ">
                      <div className="place-self-center mr-2"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fillRule="evenodd" clipRule="evenodd" d="M9.99951 -0.000488281C4.47667 -0.000488281 -0.000488281 4.47667 -0.000488281 9.99951C-0.000488281 10.8318 0.101414 11.6417 0.293893 12.4168L0.549393 14.4608C0.550892 14.4728 0.550644 14.485 0.548658 14.4969L0.20577 16.5542C0.00724095 17.7454 0.840004 18.8624 2.03828 19.0121L7.5822 19.7051C8.3573 19.8976 9.16719 19.9995 9.99951 19.9995C15.5224 19.9995 19.9995 15.5224 19.9995 9.99951C19.9995 4.47667 15.5224 -0.000488281 9.99951 -0.000488281ZM1.99951 9.99951C1.99951 5.58123 5.58123 1.99951 9.99951 1.99951C14.4178 1.99951 17.9995 5.58123 17.9995 9.99951C17.9995 14.4178 14.4178 17.9995 9.99951 17.9995C9.31296 17.9995 8.648 17.9133 8.01425 17.7515L7.95335 17.736L2.28635 17.0276C2.21587 17.0188 2.16688 16.9531 2.17856 16.883L2.52145 14.8257C2.55522 14.623 2.55943 14.4166 2.53395 14.2128L2.26306 12.0457L2.24752 11.9848C2.08577 11.351 1.99951 10.6861 1.99951 9.99951Z" fill="#6D6D6D"/>
</svg>
</div>
                      <div className="flex-1">
                        <div className="font-medium text-base text-[#6D6D6D]">{item.text}</div>
                        <div className="text-sm text-[#6D6D6D] mt-1">
                          {item.sectionTitle}           </div>
                      </div>
                    </div>
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  ) : null;

  if (isBrowser) {
    return ReactDOM.createPortal(
      modalContent,
      document.body
    );
  } else {
    return null;
  }
};

export default HistoryModal;
