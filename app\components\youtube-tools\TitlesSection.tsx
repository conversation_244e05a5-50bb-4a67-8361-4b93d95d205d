"use client";

import React from 'react';
import { VideoAnalyzerIcon } from '../icons/YoutubeIcons';
import { CopyIcon, ShareIcon } from '../icons/icons';

interface TitlesSectionProps {
  titles: string[];
  copyToClipboard: (text: string) => void;
  shareContent: (title: string, text: string) => void;
  regenerateSection: (type: 'titles' | 'description' | 'keywords' | 'hashtags' | 'all') => void;
}

const TitlesSection: React.FC<TitlesSectionProps> = ({ titles, copyToClipboard, shareContent, regenerateSection }) => {
  if (titles.length === 0) return null;

  return (
    <div className="mt-8 rounded-2xl border border-white/10 bg-[#0A0F14] p-6 sm:p-8">
      <div className="mb-5 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-[#8B5CF6]"><VideoAnalyzerIcon /></span>
          <h3 className="text-lg font-semibold">Optimized Title Suggestions</h3>
        </div>
        <div className="flex items-center gap-2 text-white/70">
          <button
            onClick={() => copyToClipboard(titles.join('\n'))}
            className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
            title="Copy"
          >
            <CopyIcon /><span>Copy</span>
          </button>
          <button
            onClick={() => shareContent('Optimized Title Suggestions', titles.join('\n'))}
            className="inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs hover:bg-white/5"
            title="Share"
          >
            <ShareIcon /><span>Share</span>
          </button>
          <button
            onClick={() => regenerateSection('all')}
            className="rounded-md px-2 py-1 text-xs hover:bg-white/5"
          >
            Redo all
          </button>
        </div>
      </div>
      <ul className="space-y-3">
        {titles.map((title, index) => (
          <li key={index} className="flex items-start gap-3">
            <span className="mt-1 h-2 w-2 flex-none rounded-full bg-white/40"></span>
            <span className="text-white/90">{title}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default TitlesSection;
